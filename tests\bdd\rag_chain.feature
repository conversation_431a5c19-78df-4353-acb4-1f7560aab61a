Feature: <PERSON><PERSON>hai<PERSON>: Ask a valid question
    Given a RAGChain instance
    When I ask a valid question
    Then a response should be returned

  Scenario: Ask with empty question
    Given a RAGChain instance
    When I ask an empty question
    Then a validation error should be returned

  Scenario: Ask with error in chain
    Given a RAGChain instance
    When I ask a question and an error occurs
    Then an error message for RA<PERSON>hain should be returned

  Scenario: Get store stats
    Given a RAGChain instance
    When I get store stats
    Then stats should be returned

  Scenario: Create a conversation
    Given a RAGChain instance
    When I create a conversation with title "Test"
    Then a conversation ID should be returned

  Scenario: Load a conversation
    Given a RAGChain instance
    When I load a conversation with ID "123"
    Then loading should succeed

  Scenario: Save current conversation
    Given a RAGChain instance
    When I save the current conversation
    Then saving should succeed

  Scenario: Get all conversations
    Given a RAGChain instance
    When I get all conversations
    Then a list of conversations should be returned

  Scenario: Update conversation title
    Given a RAGChain instance
    When I update the conversation title to "New Title" in RAGChain
    Then updating should succeed

  Scenario: Delete a conversation
    Given a RAGChain instance
    When I delete a conversation with ID "123"
    Then deleting should succeed 