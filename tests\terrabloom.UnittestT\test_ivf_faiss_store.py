"""Unit tests for the IVF FAISS vector store implementation."""

import os
import shutil
import sys
import tempfile
import unittest
from unittest.mock import MagicMock, patch

import numpy as np

sys.path.insert(
    0, os.path.abspath(os.path.join(os.path.dirname(__file__), "../../"))
)
from src.vector_store.ivf_faiss_store import IVFFAISSStore


class TestIVFFAISSStore(unittest.TestCase):
    """Test cases for the IVF FAISS vector store."""

    def setUp(self):
        """Set up test resources for each test."""
        self.embedding_dim = 4
        self.nlist = 2
        self.store = IVFFAISSStore(
            embedding_dim=self.embedding_dim, nlist=self.nlist
        )

    @patch("src.vector_store.ivf_faiss_store.faiss.IndexFlatL2")
    @patch("src.vector_store.ivf_faiss_store.faiss.IndexIVFFlat")
    def test_create_ivf_index(self, mock_ivf, mock_flat):
        """Test creating an IVF index with mocked FAISS classes."""
        mock_flat.return_value = MagicMock()
        mock_ivf.return_value = MagicMock(is_trained=True)
        sample_embeddings = np.random.rand(20, self.embedding_dim)
        self.store._create_ivf_index(sample_embeddings)
        self.assertTrue(mock_ivf.called)
        self.assertTrue(mock_flat.called)

    @patch("src.vector_store.ivf_faiss_store.faiss.IndexFlatL2")
    @patch("src.vector_store.ivf_faiss_store.faiss.IndexIVFFlat")
    def test_add_documents_and_search(self, mock_ivf, mock_flat):
        """Test adding documents and searching in the vector store."""
        # Mock FAISS index
        mock_index = MagicMock()
        mock_index.is_trained = True
        mock_index.ntotal = 2
        mock_index.search.return_value = (
            np.array([[0.1, 0.2]]),
            np.array([[0, 1]]),
        )
        mock_ivf.return_value = mock_index
        mock_flat.return_value = MagicMock()
        embeddings = np.random.rand(2, self.embedding_dim)
        documents = [{"content": "doc1"}, {"content": "doc2"}]
        metadata = [{"meta": 1}, {"meta": 2}]
        self.store.add_documents(embeddings, documents, metadata)
        self.assertEqual(self.store.id_counter, 2)
        # Search
        results = self.store.search(np.random.rand(self.embedding_dim), k=2)
        self.assertEqual(len(results), 2)
        self.assertEqual(results[0]["content"], "doc1")
        self.assertEqual(results[1]["content"], "doc2")

    def test_compress_and_decompress_document(self):
        """Test compressing and decompressing a document."""
        doc = {"content": "test content", "foo": "bar"}
        compressed = self.store._compress_document(doc)
        decompressed = self.store._decompress_document(compressed)
        self.assertEqual(doc, decompressed)

    def test_get_stats_empty(self):
        """Test getting stats when the store is empty."""
        stats = self.store.get_stats()
        self.assertIn("error", stats)

    @patch("src.vector_store.ivf_faiss_store.faiss.write_index")
    def test_save_and_load(self, mock_write_index):
        """Test saving and loading the vector store."""
        # Setup store with fake data
        self.store.index = MagicMock()
        self.store.document_store = {
            0: self.store._compress_document({"content": "doc1"})
        }
        self.store.metadata_store = {0: {"meta": 1}}
        self.store.id_counter = 1
        temp_dir = tempfile.mkdtemp()
        try:
            self.store.save(temp_dir)
            # Now test load
            with patch(
                "src.vector_store.ivf_faiss_store.faiss.read_index"
            ) as mock_read_index:
                mock_read_index.return_value = MagicMock()
                loaded = IVFFAISSStore.load(temp_dir)
                self.assertEqual(loaded.id_counter, 1)
                self.assertEqual(len(loaded.document_store), 1)
                self.assertEqual(len(loaded.metadata_store), 1)
        finally:
            shutil.rmtree(temp_dir)

    @patch("src.vector_store.ivf_faiss_store.faiss.IndexFlatL2")
    @patch("src.vector_store.ivf_faiss_store.faiss.IndexIVFFlat")
    def test_search_no_index_or_docs(self, mock_ivf, mock_flat):
        """Test searching when there is no index or documents."""
        # No index
        self.store.index = None
        results = self.store.search(np.random.rand(self.embedding_dim), k=2)
        self.assertEqual(results, [])
        # No docs
        self.store.index = MagicMock()
        self.store.id_counter = 0
        results = self.store.search(np.random.rand(self.embedding_dim), k=2)
        self.assertEqual(results, [])

    def test_get_stats_with_docs(self):
        """Test getting stats when documents are present."""
        # Add fake compressed docs
        doc = {"content": "test"}
        compressed = self.store._compress_document(doc)
        self.store.document_store = {0: compressed, 1: compressed}
        self.store.metadata_store = {0: {}, 1: {}}
        self.store.id_counter = 2
        stats = self.store.get_stats()
        self.assertEqual(stats["document_count"], 2)
        self.assertIn("compression_ratio", stats)


if __name__ == "__main__":
    unittest.main()
