"""BDD step definitions for RAG chain features."""

from unittest.mock import MagicMock, patch

from behave import given, then, when

from src.database.conversations import conversation_store
from src.rag.chain import <PERSON><PERSON>hain


@given("a fresh RAG chain")
def step_given_fresh_rag_chain(context):
    """Set up a fresh RAGChain instance and clear its state."""
    context.rag_chain = RAGChain()
    context.conversation_store = conversation_store
    context.rag_chain.clear_history()
    context.rag_chain.active_conversation_id = None


@when('I ask the question "{question}"')
def step_when_ask_question(context, question):
    """Ask a question using the RAGChain instance."""
    with (
        patch(
            "src.rag.chain.llm_manager.generate_sql_query",
            return_value="SELECT * FROM test;",
        ),
        patch(
            "src.rag.chain.llm_manager.format_response", return_value="Result!"
        ),
        patch(
            "src.rag.chain.db_manager.execute_query",
            return_value="query result",
        ),
        patch("src.rag.chain.IVFFAISSRetriever.invoke", return_value=[]),
    ):
        context.response = context.rag_chain.ask_question(question)


@when("I ask a None question")
def step_when_ask_none_question(context):
    """Ask a None question using the RAGChain instance."""
    with (
        patch(
            "src.rag.chain.llm_manager.generate_sql_query",
            return_value="SELECT * FROM test;",
        ),
        patch(
            "src.rag.chain.llm_manager.format_response", return_value="Result!"
        ),
        patch(
            "src.rag.chain.db_manager.execute_query",
            return_value="query result",
        ),
        patch("src.rag.chain.IVFFAISSRetriever.invoke", return_value=[]),
    ):
        context.response = context.rag_chain.ask_question(None)


@then("the response should not be empty")
def step_then_response_not_empty(context):
    """Assert that the response is not empty."""
    assert context.response and context.response.strip() != ""


@then('the response should contain "{text}"')
def step_then_response_contains(context, text):
    """Assert that the response contains the specified text."""
    assert text in context.response


@when('I create a new conversation with the title "{title}"')
def step_when_create_conversation(context, title):
    """Create a new conversation with the given title."""
    context.conv_id = conversation_store.create_conversation(title)
    context.active_conversation_id = context.conv_id
    context.conversation_title = title


@then('the active conversation title should be "{title}"')
def step_then_active_conversation_title(context, title):
    """Check that the active conversation title matches the expected title."""
    # This assumes the conversation store stores the title
    from src.database.conversations import conversation_store

    convs = conversation_store.list_conversations()
    found = any(
        c["id"] == context.rag_chain.active_conversation_id
        and c["title"] == title
        for c in convs
    )
    assert found


@when("I save the current conversation")
def step_when_save_current_conversation(context):
    """Save the current conversation."""
    context.save_result = context.rag_chain.save_current_conversation()


@when("I load the conversation")
def step_when_load_conversation(context):
    """Load the current conversation by its active conversation ID."""
    context.load_result = context.rag_chain.load_conversation(
        context.rag_chain.active_conversation_id
    )


@then('the chat history should contain "{text}"')
def step_then_chat_history_contains(context, text):
    """Assert that the chat history contains the specified text."""
    found = any(
        getattr(m, "content", "") == text
        for m in context.rag_chain.chat_history
    )
    assert found


@then("the conversation should not exist")
def step_then_conversation_should_not_exist(context):
    """Assert that the conversation does not exist in the store."""
    from src.database.conversations import conversation_store

    convs = conversation_store.list_conversations()
    found = any(
        c["id"] == context.rag_chain.active_conversation_id for c in convs
    )
    assert not found


@when("I get the store stats")
def step_when_get_store_stats(context):
    """Get the store statistics using a patched retriever."""
    with patch(
        "src.rag.chain.IVFFAISSRetriever.get_store_stats",
        return_value={"document_count": 1},
    ):
        context.stats = context.rag_chain.get_store_stats()


@then('the stats should include "{key}"')
def step_then_stats_should_include(context, key):
    """Check that the stats include the specified key."""
    assert key in context.stats


@given("a RAGChain instance")
def step_given_rag_chain_instance(context):
    """Provide a RAGChain instance and clear its history."""
    context.rag_chain = RAGChain()
    context.rag_chain.clear_history()
    context.rag_chain.active_conversation_id = None


@when("I ask a valid question")
def step_when_ask_valid_question(context):
    """Ask a valid question using the RAGChain instance."""
    with (
        patch(
            "src.rag.chain.llm_manager.generate_sql_query",
            return_value="SELECT * FROM test;",
        ),
        patch(
            "src.rag.chain.llm_manager.format_response", return_value="Result!"
        ),
        patch(
            "src.rag.chain.db_manager.execute_query",
            return_value="query result",
        ),
        patch("src.rag.chain.IVFFAISSRetriever.invoke", return_value=[]),
    ):
        context.response = context.rag_chain.ask_question("What is foo?")


@then("a response should be returned")
def step_then_response_should_be_returned(context):
    """Assert that a non-empty response is returned."""
    assert isinstance(context.response, str)
    assert context.response.strip() != ""


@when("I ask an empty question")
def step_when_ask_empty_question(context):
    """Ask an empty question using the RAGChain instance."""
    with (
        patch(
            "src.rag.chain.llm_manager.generate_sql_query",
            return_value="SELECT * FROM test;",
        ),
        patch(
            "src.rag.chain.llm_manager.format_response", return_value="Result!"
        ),
        patch(
            "src.rag.chain.db_manager.execute_query",
            return_value="query result",
        ),
        patch("src.rag.chain.IVFFAISSRetriever.invoke", return_value=[]),
    ):
        context.response = context.rag_chain.ask_question("")


@then("a validation error should be returned")
def step_then_validation_error_should_be_returned(context):
    """Assert that a validation error is returned in the response."""
    assert "Validation error" in context.response


@when("I ask a question and an error occurs")
def step_when_ask_question_and_error_occurs(context):
    """Ask a question that triggers an error in the RAGChain."""
    with patch(
        "src.rag.chain.llm_manager.generate_sql_query",
        side_effect=Exception("fail"),
    ):
        context.response = context.rag_chain.ask_question("fail")


@then("an error message for RAGChain should be returned")
def step_then_error_message_for_rag_chain_should_be_returned(context):
    """Assert that an error message for RAGChain is returned."""
    assert "Error processing question" in context.response


@when("I get store stats")
def step_when_get_store_stats_orig(context):
    """Get the original store statistics using a patched retriever."""
    with patch(
        "src.rag.chain.IVFFAISSRetriever.get_store_stats",
        return_value={"document_count": 1},
    ):
        context.stats = context.rag_chain.get_store_stats()


@then("stats should be returned")
def step_then_stats_should_be_returned(context):
    """Assert that stats are returned and contain 'document_count'."""
    assert "document_count" in context.stats


@when('I create a conversation with title "{title}"')
def step_when_create_conversation_with_title(context, title):
    """Create a conversation with the specified title using RAGChain."""
    context.conv_id = context.rag_chain.create_conversation(title)


@then("a conversation ID should be returned")
def step_then_conversation_id_should_be_returned(context):
    """Assert that a conversation ID is returned and is a string."""
    assert isinstance(context.conv_id, str)
    assert context.conv_id


@when('I load a conversation with ID "{conv_id}"')
def step_when_load_conversation_with_id(context, conv_id):
    """Load a conversation by its ID using the conversation store."""
    from src.database.conversations import conversation_store

    # Patch load_messages to return a dummy message for this test
    with patch.object(
        conversation_store,
        "load_messages",
        return_value=[MagicMock(content="dummy")],
    ):
        context.load_result = context.rag_chain.load_conversation(conv_id)


@then("loading should succeed")
def step_then_loading_should_succeed(context):
    """Assert that loading the conversation succeeded."""
    assert context.load_result is True


@then("saving should succeed")
def step_then_saving_should_succeed(context):
    """Assert that saving the conversation succeeded."""
    assert context.save_result is True


@when("I get all conversations")
def step_when_get_all_conversations(context):
    """Get all conversations using the RAGChain instance."""
    context.all_convs = context.rag_chain.get_all_conversations()


@then("a list of conversations should be returned")
def step_then_list_of_conversations_should_be_returned(context):
    """Assert that a list of conversations is returned."""
    assert isinstance(context.all_convs, list)


@when('I update the conversation title to "New Title" in RAGChain')
def step_when_update_conversation_title_in_ragchain(context):
    """Update the conversation title to 'New Title' in RAGChain."""
    from src.database.conversations import conversation_store

    # Ensure there is an active conversation
    if not context.rag_chain.active_conversation_id:
        context.rag_chain.active_conversation_id = (
            context.rag_chain.create_conversation("Initial Title")
        )
    with patch.object(
        conversation_store, "update_conversation_title", return_value=True
    ):
        context.update_result = context.rag_chain.update_conversation_title(
            "New Title"
        )


@then("updating should succeed")
def step_then_updating_should_succeed(context):
    """Assert that updating the conversation title succeeded."""
    assert context.update_result is True


@when('I delete a conversation with ID "{conv_id}"')
def step_when_delete_conversation_with_id(context, conv_id):
    """Delete a conversation by its ID using the conversation store."""
    from src.database.conversations import conversation_store

    with patch.object(
        conversation_store, "delete_conversation", return_value=True
    ):
        context.delete_result = context.rag_chain.delete_conversation(conv_id)


@then("deleting should succeed")
def step_then_deleting_should_succeed(context):
    """Assert that deleting the conversation succeeded."""
    assert context.delete_result is True
