"""Global exception handler for the API."""

from litestar import Request, Response
from litestar.exceptions.http_exceptions import (
    ValidationException as LitestarValidationException,
)
from litestar.status_codes import HTTP_500_INTERNAL_SERVER_ERROR

from infrastructure.exceptions import (
    BusinessExceptionError,
    ValidationBehaviorExceptionError,
)
from infrastructure.logging_utils.logger import logger
from infrastructure.metrics import track_exception


def global_exception_handler(
    request: Request,
    exc: Exception,
) -> Response:
    """Global exception handler for the API.

    This function handles exceptions raised during request processing
    and returns a standardized error response in RFC 9110 (problem+json) format.

    Args:
        request (Request): The incoming request object.
        exc (Exception): The exception that was raised.

    Returns:
        Response: A response object with a 500 status code and error message.
    """
    error_url = "about:blank"
    if isinstance(exc, LitestarValidationException):
        validation_errors = getattr(exc, "detail", {})
        if hasattr(exc, "_errors") and exc._errors:

            for error in exc._errors:
                if isinstance(error, dict) and "url" in error:
                    error_url = error["url"]
                    break
        # Extract validation errors from Litestar's exception

        if hasattr(exc, "_messages") and exc._messages:
            # Format depends on Litestar version, try to extract useful information
            messages = []
            for msg in exc._messages:
                if isinstance(msg, dict) and "message" in msg:
                    messages.append(msg["message"])
                else:
                    messages.append(str(msg))
            detail = "; ".join(messages)
        else:
            detail = "Validation error in request body"

        # Create our custom exception
        exc = ValidationBehaviorExceptionError(
            detail=detail, data={"validation_errors": validation_errors}
        )

    if isinstance(exc, BusinessExceptionError):
        # If the exception is a BusinessExceptionError, use its status code and detail
        status_code = exc.status_code
        detail = exc.detail
        error_data = exc.data
    else:
        status_code = getattr(
            exc, "status_code", HTTP_500_INTERNAL_SERVER_ERROR
        )
        detail = getattr(exc, "detail", str(exc))
        error_data = {}

    # Track the exception for metrics
    track_exception(request, exc, status_code)

    path = request.url.path
    method = getattr(request, "method", None) or getattr(
        request, "scope", {}
    ).get("method", "")
    # Log with context
    logger.structured(
        level="error",
        message="Unhandled exception occurred",
        input_params={
            "path": path,
            "method": method,
            "status_code": status_code,
            "detail": detail,
            "exception_type": exc.__class__.__name__,
        },
        additional_props=error_data,
        exception=exc,
    )

    problem_response = {
        "type": error_url,
        "title": exc.__class__.__name__,
        "status": status_code,
        "detail": detail,
        "instance": f"{method} {path}",
    }
    return Response(
        status_code=status_code,
        content=problem_response,
        media_type="application/problem+json",
    )
