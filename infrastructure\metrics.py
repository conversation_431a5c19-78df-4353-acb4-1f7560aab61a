"""
Metrics counter.
This file also has helper functions for fetching labels
"""

from typing import Dict

from prometheus_client import Counter

from infrastructure.exceptions import BusinessExceptionError

business_exception_counter = Counter(
    "app_global_business_exception_total",
    "Total number of global business exceptions",
    [
        "type",
        "environment",
        "warehouse",
        "bu",
        "account",
        "customer",
        "status_code",
    ],
)

exception_counter = Counter(
    "app_global_unhandled_exception",
    "Total number of unhandled exceptions",
    [
        "type",
        "environment",
        "warehouse",
        "bu",
        "account",
        "customer",
        "status_code",
    ],
)


def extract_labels_from_request(request) -> Dict[str, str]:
    """Extract metric labels from a request.

    Args:
        request: The HTTP request object

    Returns:
        Dictionary of label values
    """
    if hasattr(request, "state") and hasattr(request.state, "context"):
        # Use the pre-processed application context
        context = request.state.context
        return context.to_metrics_dict()
    headers = dict(request.headers)

    # # Extract values with fallbacks for each header
    environment = headers.get("app-environment", "unknown")

    warehouse = headers.get("app-warehouse-id", "unknown")

    bu = headers.get("app-bu-id", "unknown")

    account = headers.get("app-account-id", "unknown")

    customer = headers.get("app-customer-id", "unknown")

    return {
        "environment": environment,
        "warehouse": warehouse,
        "bu": bu,
        "account": account,
        "customer": customer,
    }


def track_exception(request, exc, status_code):
    """Track an exception in Prometheus metrics.

    Args:
        request: The HTTP request
        exc: The exception object
        status_code: The HTTP status code
    """
    labels = extract_labels_from_request(request)
    if isinstance(exc, BusinessExceptionError):
        business_exception_counter.labels(
            type=exc.__class__.__name__,
            environment=labels["environment"],
            warehouse=labels["warehouse"],
            bu=labels["bu"],
            account=labels["account"],
            customer=labels["customer"],
            status_code=str(status_code),
        ).inc()
    else:
        exception_counter.labels(
            type=exc.__class__.__name__,
            environment=labels["environment"],
            warehouse=labels["warehouse"],
            bu=labels["bu"],
            account=labels["account"],
            customer=labels["customer"],
            status_code=str(status_code),
        ).inc()
