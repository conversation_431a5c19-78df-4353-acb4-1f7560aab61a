"""BDD step definitions for IVF FAISS store scenarios."""

import tempfile
from unittest.mock import MagicMock, patch

import numpy as np
from behave import given, then, when

from src.vector_store.ivf_faiss_store import IVFFAISSStore


@given("an IVFFAISSStore instance")
def step_given_store(context):
    """Provide an IVFFAISSStore instance for testing."""
    context.store = IVFFAISSStore(embedding_dim=4, nlist=2)


@given("an IVFFAISSStore instance with documents")
def step_given_store_with_docs(context):
    """Provide an IVFFAISSStore instance pre-populated with documents."""
    s = IVFFAISSStore(embedding_dim=4, nlist=2)
    emb = np.random.rand(2, 4).astype("float32")
    docs = [{"content": "a"}, {"content": "b"}]
    meta = [{"id": 1}, {"id": 2}]
    with (
        patch(
            "src.vector_store.ivf_faiss_store.faiss.IndexFlatL2"
        ) as mock_flat,
        patch(
            "src.vector_store.ivf_faiss_store.faiss.IndexIVFFlat"
        ) as mock_ivf,
    ):
        mock_flat.return_value = MagicMock()
        mock_ivf.return_value = MagicMock(
            is_trained=True,
            add=MagicMock(),
            nprobe=1,
            ntotal=2,
            search=lambda x, y: (np.array([[0.1, 0.2]]), np.array([[0, 1]])),
        )
        s.add_documents(emb, docs, meta)
    context.store_with_docs = s


@given("an empty IVFFAISSStore instance")
def step_given_empty_store(context):
    """Provide an empty IVFFAISSStore instance for testing."""
    context.empty_store = IVFFAISSStore(embedding_dim=4, nlist=2)


@when("I add documents and search with a query embedding")
def step_when_add_and_search(context):
    """Add documents to the store and perform a search with a query embedding."""
    emb = np.random.rand(2, 4).astype("float32")
    docs = [{"content": "a"}, {"content": "b"}]
    meta = [{"id": 1}, {"id": 2}]
    with (
        patch(
            "src.vector_store.ivf_faiss_store.faiss.IndexFlatL2"
        ) as mock_flat,
        patch(
            "src.vector_store.ivf_faiss_store.faiss.IndexIVFFlat"
        ) as mock_ivf,
    ):
        mock_flat.return_value = MagicMock()
        mock_ivf.return_value = MagicMock(
            is_trained=True,
            add=MagicMock(),
            nprobe=1,
            ntotal=2,
            search=lambda x, y: (np.array([[0.1, 0.2]]), np.array([[0, 1]])),
        )
        context.store.add_documents(emb, docs, meta)
        context.search_results = context.store.search(
            np.random.rand(4).astype("float32")
        )


@then("search results should be returned")
def step_then_search_results(context):
    """Assert that search results are returned and non-empty."""
    assert isinstance(context.search_results, list)
    assert len(context.search_results) > 0


@when("I get stats")
def step_when_get_stats(context):
    """Get statistics from the store with documents."""
    context.stats = context.store_with_docs.get_stats()


@then("stats should include document count")
def step_then_stats(context):
    """Assert that stats include a positive document count."""
    assert "document_count" in context.stats
    assert context.stats["document_count"] > 0


@when("I get stats with no documents")
def step_when_get_stats_no_docs(context):
    """Get statistics from an empty store."""
    context.stats = context.store.get_stats()


@then("an error message should be returned")
def step_then_stats_error(context):
    """Assert that an error message is present in the stats."""
    assert "error" in context.stats


@when("I save and load the store")
def step_when_save_and_load(context):
    """Save the store with documents and load it back from disk."""
    with (
        tempfile.TemporaryDirectory() as tmpdir,
        patch("src.vector_store.ivf_faiss_store.faiss.write_index"),
        patch(
            "src.vector_store.ivf_faiss_store.faiss.read_index",
            return_value=MagicMock(),
        ),
    ):
        context.store_with_docs.save(tmpdir)
        context.loaded_store = IVFFAISSStore.load(tmpdir)


@then("the loaded store should have the same document count")
def step_then_loaded_store(context):
    """Assert that the loaded store has the same document count as the original."""
    assert len(context.loaded_store.document_store) == len(
        context.store_with_docs.document_store
    )


@when("I search with a query embedding")
def step_when_search_empty(context):
    """Search the empty store with a query embedding."""
    context.empty_results = context.empty_store.search(
        np.random.rand(4).astype("float32")
    )


@then("no results should be returned")
def step_then_no_results(context):
    """Assert that no results are returned from the empty store."""
    assert context.empty_results == []


@when("I compress and decompress a document")
def step_when_compress_decompress(context):
    """Compress and then decompress a document using the store's methods."""
    doc = {"foo": "bar"}
    compressed = context.store._compress_document(doc)
    context.recovered_doc = context.store._decompress_document(compressed)


@then("the original document should be recovered")
def step_then_recovered_doc(context):
    """Assert that the original document is recovered after decompression."""
    assert context.recovered_doc == {"foo": "bar"}
