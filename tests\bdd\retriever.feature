Feature: IVFFAISSRetriever

  Scenario: Load or create store successfully
    When I initialize the IVFFAISSRetriever
    Then the store should be set

  Scenario: Handle error when loading store
    When I initialize the IVFFAISSRetriever and loading fails
    Then a new store should be created

  Scenario: Invoke with a query and return documents
    Given an IVFFAISSRetriever instance
    When I invoke with a query that matches documents
    Then documents should be returned

  Scenario: Invoke with a query and get no results
    Given an IVFFAISSRetriever instance
    When I invoke with a query that matches nothing
    Then no documents should be returned

  Scenario: Invoke with a query and search raises error
    Given an IVFFAISSRetriever instance
    When I invoke and search raises an error
    Then no documents should be returned

  Scenario: Get store stats
    Given an IVFFAISSRetriever instance
    When I get store stats from retriever
    Then stats should be returned 

Scenario: Invoke with a query that causes an exception in the retriever
  Given an IVFFAISSRetriever instance with a faulty store
  When I invoke the retriever with a query that triggers an error
  Then no documents should be returned

Scenario: Invoke with a query that returns results missing metadata fields
  Given an IVFFAISSRetriever instance with incomplete metadata in results
  When I invoke the retriever with a query
  Then no exception should be raised and documents should be returned or skipped 