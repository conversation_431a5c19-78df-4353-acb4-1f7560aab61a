"""Retriever for table schemas using IVF FAISS store and embeddings, focused on table.

comments matching in the TerraBLOOM RAG System.
"""

from typing import List

import numpy as np
import pandas as pd
from langchain_core.documents import Document
from langchain_google_genai import GoogleGenerativeAIEmbeddings
from loguru import logger

from src.config.config import config
from src.vector_store.ivf_faiss_store import IVFFAISSStore
from src.vector_store.reranker import Reranker


class IVFFAISSRetriever:
    """Simplified LangChain-compatible retriever for IVF FAISS store focused on table.

    comments matching.
    """

    def __init__(self, store: IVFFAISSStore = None, reranker: Reranker = None):
        """Initialize IVFFAISSRetriever with optional store and embeddings model."""
        self.store = store
        self.embeddings_model = GoogleGenerativeAIEmbeddings(
            model=config.EMBEDDING_MODEL
        )
        self.reranker = reranker

        # Load existing store if available
        if store is None:
            self.store = self._load_or_create_store()

    def _load_or_create_store(self) -> IVFFAISSStore:
        """Load existing store or create new one."""
        logger.info(
            f"Attempting to load schema store from: {config.SCHEMA_STORE_PATH}"
        )
        try:
            # Try to load existing store
            store = IVFFAISSStore.load(config.SCHEMA_STORE_PATH)
            if store is not None:
                logger.info(
                    f"Successfully loaded schema store with {store.id_counter} documents"
                )
                logger.info(
                    f"Index size: {store.index.ntotal if store.index else 'No index'}"
                )
                logger.info(f"Document store size: {len(store.document_store)}")
                logger.info(f"Metadata store size: {len(store.metadata_store)}")
                return store
            else:
                logger.warning(
                    "Schema store could not be loaded (store is None). Will create new store."
                )
        except Exception as e:
            logger.error(f"ERROR loading schema store: {str(e)}")
            import traceback

            logger.error(traceback.format_exc())
        logger.info("Creating new schema store...")
        return self._create_schema_store()

    def _create_schema_store(self) -> IVFFAISSStore:
        """Create an IVF FAISS store from schema data focused on table comments."""
        # Load schema data
        df = pd.read_csv(config.TABLE_SCHEMA_PATH)

        documents = []
        metadata = []

        for i, row in df.iterrows():
            # Extract schema and table name
            schema_name, table_name = (
                row["table_name"].split(".")
                if "." in row["table_name"]
                else ("public", row["table_name"])
            )

            # Get table comments - this is the primary content for similarity matching
            table_comments = row.get("comments", "").strip()

            if not table_comments:
                logger.warning(
                    f"No comments found for table {row['table_name']}"
                )
                table_comments = (
                    f"Table {row['table_name']} - no description available"
                )

            # Create comments-focused document for embedding and similarity matching
            comments_content = f"""TABLE_DESCRIPTION: {table_comments}
BUSINESS_PURPOSE: {table_comments}
FUNCTIONALITY: {table_comments}
USE_CASE: {table_comments}
CONTEXT: {table_comments}
TABLE_NAME: {row['table_name']}"""

            comments_doc = {
                "content": comments_content,
                "type": "table_comments",
            }

            comments_meta = {
                "table": row["table_name"],
                "schema": schema_name,
                "table_name": table_name,
                "type": "table_comments",
                "comments": table_comments,
                "ddl": row["DDL"],  # Store DDL for final retrieval
            }

            documents.append(comments_doc)
            metadata.append(comments_meta)

        logger.info(
            f"Created {len(documents)} comment-focused documents for embedding"
        )

        # Get embeddings for all documents
        texts = [doc["content"] for doc in documents]
        embeddings = np.array(self.embeddings_model.embed_documents(texts))

        # Create IVF store
        store = IVFFAISSStore(
            embedding_dim=embeddings.shape[1],
            nlist=min(50, len(documents) // 2),
        )
        store.add_documents(embeddings, documents, metadata)

        # Save the store
        store.save(config.SCHEMA_STORE_PATH)
        logger.info(f"Schema store saved to {config.SCHEMA_STORE_PATH}")

        return store

    def invoke(self, query: str, k: int = None) -> List[Document]:
        """Invoke method that retrieves top N from vector store, reranks, and returns best K for LLM."""
        logger.info(
            f"\n=== IVFFAISSRetriever invoke called with query: '{query}' ==="
        )

        try:
            # Use config values if not provided
            retriever_k = k if k is not None else config.RETRIEVER_K
            reranker_k = config.RERANKER_K

            # Get query embedding
            logger.info("Embedding query...")
            query_embedding = np.array(self.embeddings_model.embed_query(query))
            logger.info(f"Query embedding shape: {len(query_embedding)}")

            # Search for similar table comments
            logger.info(
                f"Searching for tables with similar comments (retriever_k={retriever_k})..."
            )
            results = self.store.search(query_embedding, k=retriever_k)
            logger.info(f"Search returned {len(results)} results")

            if not results:
                logger.warning("No results returned from search!")
                return []

            # Log DDLs before reranking
            logger.info("DDLs BEFORE RERANKING:")
            for idx, result in enumerate(results):
                ddl = result["metadata"].get("ddl", "[No DDL]")
                logger.info(
                    f"[{idx}] {ddl[:200]}{'...' if len(ddl) > 200 else ''}"
                )

            passages = [result["content"] for result in results]
            rerank_k = min(reranker_k, len(results))
            reranked_indices = self.reranker.rerank(query, passages, rerank_k)
            # Only use the top reranker_k indices
            reranked_results = [results[i] for i in reranked_indices[:rerank_k]]

            # Log DDLs after reranking
            logger.info("DDLs AFTER RERANKING:")
            for idx, result in enumerate(reranked_results):
                ddl = result["metadata"].get("ddl", "[No DDL]")
                logger.info(
                    f"[{idx}] {ddl[:200]}{'...' if len(ddl) > 200 else ''}"
                )

            # Convert reranked results to LangChain Documents
            documents = []
            for result in reranked_results:
                metadata = result["metadata"]
                doc = Document(
                    page_content=metadata[
                        "ddl"
                    ],  # Or combine with comments if desired
                    metadata={
                        "table": metadata["table"],
                        "schema": metadata["schema"],
                        "table_name": metadata["table_name"],
                        "type": "table_comments",
                        "similarity_score": result.get("distance", 0),
                    },
                )
                documents.append(doc)
                logger.info(
                    f"Retrieved table: {metadata['table']} (similarity: {result.get('distance', 'N/A')})"
                )
                logger.info(f"Comments: {metadata['comments'][:100]}...")

            logger.info(
                f"Returning {len(documents)} table schema documents (reranked, best {rerank_k})"
            )
            return documents

        except Exception as e:
            logger.error(f"ERROR in invoke: {str(e)}")
            import traceback

            logger.error(traceback.format_exc())
            return []

    def get_store_stats(self):
        """Get statistics about the vector store."""
        return self.store.get_stats()
