Feature: Config Management

  Scenario: Config uses default values when no environment variables are set
    Given no environment variables are set for config
    When I create a Config instance
    Then the config should use default values

  Scenario: Config uses environment variable values when set
    Given environment variables are set for config
    When I create a Config instance
    Then the config should use the environment variable values

  Sc<PERSON>rio: Get database URL
    Given a Config instance
    When I get the database URL
    Then it should be a valid PostgreSQL URL

  Scenario: Set environment variables from config
    Given a Config instance
    When I call set_environment_variables
    Then the environment variables should be set accordingly

  Scenario: Get foreign key patterns
    Given a Config instance
    When I get the foreign key patterns
    Then it should return a dictionary with expected keys 