"""# src/api/service_container.py.
This module defines the ServiceContainer class, which manages the
lifecycle and dependencies of various services used in the RAG application.
It initializes services such as configuration, LLM, database, vector store, and RAG chain.
It provides methods to access these services and check the initialization status.
"""

import os
from datetime import datetime
from typing import Any, Dict

from loguru import logger

from infrastructure.config import ConfigService
from src.config.config import Config, config
from src.database.connection import DatabaseManager
from src.database.conversations import ConversationStore
from src.llm.chat_models import LLMManager
from src.rag.chain import RAGChain
from src.vector_store.ivf_faiss_store import IVFFAISSStore
from src.vector_store.reranker import Reranker
from src.vector_store.retriever import IVFFAISSRetriever


class ServiceContainer:
    """A container for core RAG services that can be used to manage dependencies in the application."""

    def __init__(self):
        """Initializes the ServiceContainer with an empty dictionary to hold services."""
        self._services: Dict[str, Any] = {}
        self._initialized = False
        self._initialized_time: datetime | None = None

    def initialize(self, config_service: ConfigService = None):
        """Initializes the service container, setting the initialized flag and timestamp.

        Args:
            config_service (ConfigService, optional): Configuration service instance. If not provided, uses bootstrapper.
        """
        if self._initialized:
            logger.info("ServiceContainer is already initialized")
            return

        logger.info("Initializing ServiceContainer...")

        try:
            # Use the provided config service or fall back to bootstrapper
            if config_service is None:
                from infrastructure.bootstrap import bootstrapper
                config_service = bootstrapper.get_config()
            self._services["config"] = config_service
            logger.info("Using ConfigService")

            # Keep RAG config separate for RAG-specific services
            rag_config = config
            self._services["rag_config"] = rag_config
            logger.info("Using RAG Config for RAG services")

            # LLM Manager
            llm_manager = LLMManager()
            self._services["llm_manager"] = llm_manager
            logger.info("LLMManager initialized.")

            # Database Manager
            db_manager = DatabaseManager()
            self._services["db_manager"] = db_manager
            logger.info("DatabaseManager initialized.")

            # Reranker
            reranker = Reranker()
            self._services["reranker"] = reranker
            logger.info("Reranker initialized.")

            # Ensure ivf_schema_store/ivf_index.faiss exists, if not, create embeddings/index
            schema_store_path = rag_config.SCHEMA_STORE_PATH
            faiss_index_path = os.path.join(schema_store_path, "ivf_index.faiss")
            if not os.path.exists(faiss_index_path):
                logger.warning(
                    f"FAISS index file '{faiss_index_path}' does not exist. Creating embeddings and FAISS index..."
                )
                _ = IVFFAISSRetriever(store=None)  # This will trigger creation

            # Always load the store from disk to ensure index is loaded
            ivf_faiss_store = IVFFAISSStore.load(schema_store_path)
            self._services["ivf_faiss_store"] = ivf_faiss_store
            logger.info("IVFFAISSStore loaded.")

            ivf_faiss_retriever = IVFFAISSRetriever(
                store=ivf_faiss_store, reranker=reranker
            )
            self._services["ivf_faiss_retriever"] = ivf_faiss_retriever
            logger.info("IVFFAISSRetriever initialized.")

            # Conversation Store
            conversation_store = ConversationStore()
            self._services["conversation_store"] = conversation_store
            logger.info("ConversationStore initialized.")

            # RAG Chain
            rag_chain = RAGChain(
                llm_manager=llm_manager,
                db_manager=db_manager,
                conversation_store=conversation_store,
                retriever=ivf_faiss_retriever,
            )
            self._services["rag_chain"] = rag_chain
            logger.info("RAGChain initialized.")

            self._initialized = True
            self._initialized_time = datetime.now()
            logger.info(f"Service container initialized at {self._initialized_time}")

        except Exception as e:
            logger.error(f"Failed to initialize services: {str(e)}")
            self._services.clear()
            self._initialized = False
            self._initialized_time = None
            raise RuntimeError(f"Failed to initialize services: {str(e)}") from e

    def get_config(self) -> ConfigService:
        """Get the configuration service.

        Returns:
            ConfigService: The configuration service instance.

        Raises:
            RuntimeError: If services are not initialized.
        """
        if not self._initialized:
            raise RuntimeError("Services are not initialized")
        return self._services["config"]

    def get_rag_config(self) -> Config:
        """Get the RAG configuration.

        Returns:
            Config: The RAG configuration instance.

        Raises:
            RuntimeError: If services are not initialized.
        """
        if not self._initialized:
            raise RuntimeError("Services are not initialized")
        return self._services["rag_config"]

    def get_llm_manager(self) -> LLMManager:
        """Get the LLM manager.

        Returns:
            LLMManager: The LLM manager instance.

        Raises:
            RuntimeError: If services are not initialized.
        """
        if not self._initialized:
            raise RuntimeError("Services are not initialized")
        return self._services["llm_manager"]

    def get_db_manager(self) -> DatabaseManager:
        """Get the database manager.

        Returns:
            DatabaseManager: The database manager instance.

        Raises:
            RuntimeError: If services are not initialized.
        """
        if not self._initialized:
            raise RuntimeError("Services are not initialized")
        return self._services["db_manager"]

    def get_retriever(self) -> IVFFAISSRetriever:
        """Get the FAISS retriever.

        Returns:
            IVFFAISSRetriever: The FAISS retriever instance.

        Raises:
            RuntimeError: If services are not initialized.
        """
        if not self._initialized:
            raise RuntimeError("Services are not initialized")
        return self._services["ivf_faiss_retriever"]

    def get_conversation_store(self) -> ConversationStore:
        """Get the conversation store.

        Returns:
            ConversationStore: The conversation store instance.

        Raises:
            RuntimeError: If services are not initialized.
        """
        if not self._initialized:
            raise RuntimeError("Services are not initialized")
        return self._services["conversation_store"]

    def get_rag_chain(self) -> RAGChain:
        """Get the RAG chain.

        Returns:
            RAGChain: The RAG chain instance.

        Raises:
            RuntimeError: If services are not initialized.
        """
        if not self._initialized:
            raise RuntimeError("Services are not initialized")
        return self._services["rag_chain"]

    def get_reranker(self) -> Reranker:
        """Get the reranker.

        Returns:
            Reranker: The reranker instance.

        Raises:
            RuntimeError: If services are not initialized.
        """
        if not self._initialized:
            raise RuntimeError("Services are not initialized")
        return self._services["reranker"]

    def is_initialized(self) -> bool:
        """Check if services are initialized.

        Returns:
            bool: True if services are initialized, False otherwise.
        """
        return self._initialized

    def get_initialization_time(self) -> datetime | None:
        """Get the time when services were initialized.

        Returns:
            datetime | None: The initialization time or None if not initialized.
        """
        return self._initialized_time


service_container = ServiceContainer()
