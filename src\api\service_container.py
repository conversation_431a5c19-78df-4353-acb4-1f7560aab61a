"""Service container module for the RAG system.

This module defines the ServiceContainer class, which acts as an Inversion of Control (IoC) container
for managing and providing access to all core services (LLM, database, vector store, retriever, conversation store, RAG chain, etc.)
used throughout the application. It ensures that dependencies are initialized in a single place and can be injected where needed.
"""

import os

from loguru import logger

from src.config.config import Config, config
from src.database.connection import DatabaseManager
from src.database.conversations import ConversationStore
from src.llm.chat_models import LLMManager
from src.rag.chain import <PERSON><PERSON>hain
from src.vector_store.ivf_faiss_store import IVFFAISSStore
from src.vector_store.reranker import Reranker
from src.vector_store.retriever import IVFFAISSRetriever


class ServiceContainer:
    """A container for core RAG services.

    This class manages the lifecycle and access to all major services required by the application.
    It ensures that each service is initialized only once and provides getter methods for dependency injection.
    """

    def __init__(self):
        """Initialize the ServiceContainer with all service attributes set to None."""
        self._initialized = False
        self.config: Config | None = None
        self.llm_manager: LLMManager | None = None
        self.db_manager: DatabaseManager | None = None
        self.ivf_faiss_store: IVFFAISSStore | None = None
        self.ivf_faiss_retriever: IVFFAISSRetriever | None = None
        self.conversation_store: ConversationStore | None = None
        self.rag_chain: RAGChain | None = None
        self.reranker: Reranker | None = None

    def initialize(self):
        """Initialize all core services managed by the container.
        This method should be called once at application startup.
        """
        if self._initialized:
            logger.info("ServiceContainer already initialized.")
            return

        logger.info("Initializing ServiceContainer...")

        # Config
        self.config = config

        # LLM Manager
        self.llm_manager = LLMManager()

        # Database Manager
        self.db_manager = DatabaseManager()

        # Reranker
        self.reranker = Reranker()

        # Ensure ivf_schema_store/ivf_index.faiss exists, if not, create embeddings/index
        schema_store_path = self.config.SCHEMA_STORE_PATH
        faiss_index_path = os.path.join(schema_store_path, "ivf_index.faiss")
        if not os.path.exists(faiss_index_path):
            logger.warning(
                f"FAISS index file '{faiss_index_path}' does not exist. Creating embeddings and FAISS index..."
            )
            _ = IVFFAISSRetriever(store=None)  # This will trigger creation

        # Always load the store from disk to ensure index is loaded
        self.ivf_faiss_store = IVFFAISSStore.load(schema_store_path)
        self.ivf_faiss_retriever = IVFFAISSRetriever(
            store=self.ivf_faiss_store, reranker=self.reranker
        )

        # Conversation Store
        self.conversation_store = ConversationStore()

        # RAG Chain
        self.rag_chain = RAGChain(
            llm_manager=self.llm_manager,
            db_manager=self.db_manager,
            conversation_store=self.conversation_store,
            retriever=self.ivf_faiss_retriever,
        )

        self._initialized = True
        logger.info("ServiceContainer initialized.")

    def is_initialized(self) -> bool:
        """Return True if the container has been initialized, else False."""
        return self._initialized

    def get_config(self) -> Config:
        """Get the Config instance from the container."""
        if not self._initialized:
            raise RuntimeError("ServiceContainer not initialized")
        return self.config
    
    def get_llm_manager(self) -> LLMManager:
        """Get the LLMManager instance from the container."""
        if not self._initialized:
            raise RuntimeError("ServiceContainer not initialized")
        return self.llm_manager

    def get_db_manager(self) -> DatabaseManager:
        """Get the DatabaseManager instance from the container."""
        if not self._initialized:
            raise RuntimeError("ServiceContainer not initialized")
        return self.db_manager

    def get_retriever(self) -> IVFFAISSRetriever:
        """Get the IVFFAISSRetriever instance from the container."""
        if not self._initialized:
            raise RuntimeError("ServiceContainer not initialized")
        return self.ivf_faiss_retriever

    def get_conversation_store(self) -> ConversationStore:
        """Get the ConversationStore instance from the container."""
        if not self._initialized:
            raise RuntimeError("ServiceContainer not initialized")
        return self.conversation_store

    def get_rag_chain(self) -> RAGChain:
        """Get the RAGChain instance from the container."""
        if not self._initialized:
            raise RuntimeError("ServiceContainer not initialized")
        return self.rag_chain

    def get_reranker(self) -> Reranker:
        """Get the Reranker instance from the container."""
        if not self._initialized:
            raise RuntimeError("ServiceContainer not initialized")
        return self.reranker


service_container = ServiceContainer()
