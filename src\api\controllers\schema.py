"""API controller for schema extraction endpoints."""

from litestar import Controller, get

from src.api.mappers import table_schema_list_to_schema
from src.models import TableSchema
from src.utils.schema_extract import extract_all_table_schemas


class <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(Controller):
    """Controller for schema-related API endpoints."""

    path = "/schema"

    @get("/extract")
    def extract_schema(self) -> dict:
        """Extract and return all table schemas from the database."""
        try:
            schemas_data = extract_all_table_schemas()
            schemas = [TableSchema(**schema) for schema in schemas_data]
            # Use mapper for list response
            return table_schema_list_to_schema(schemas).model_dump()
        except Exception as e:
            return {"error": str(e)}
