"""Custom JSON encoder for serializing complex objects.

This module provides a custom JSON encoder that can handle Pydantic models,
domain models, and other complex types not natively supported by the JSON module.
"""

import json
from datetime import date, datetime
from decimal import Decimal
from typing import Any
from uuid import UUID

from pydantic import BaseModel


class CustomJSONEncoder(json.JSONEncoder):
    """Custom JSON encoder that handles application-specific types."""

    def default(self, obj: Any) -> Any:
        """Convert non-standard types to JSON-serializable types.

        Args:
            obj: The object to convert

        Returns:
            A JSON-serializable version of the object
        """
        # Handle Pydantic models
        if isinstance(obj, BaseModel):
            return obj.model_dump()

        # Handle domain models with to_dict method
        if hasattr(obj, "to_dict") and callable(getattr(obj, "to_dict")):
            return obj.to_dict()

        # Handle domain models with __dict__ (convert private attributes)
        if hasattr(obj, "__dict__"):
            # Create a clean dict without private attributes
            result = {}
            for key, value in obj.__dict__.items():
                # Skip private attributes (starting with _)
                if not key.startswith("_"):
                    result[key] = value
            return result

        # Handle common types
        if isinstance(obj, (datetime, date)):
            return obj.isoformat()

        if isinstance(obj, UUID):
            return str(obj)

        if isinstance(obj, Decimal):
            return float(obj)

        # Let the parent class handle anything else or raise TypeError
        return super().default(obj)


def json_dumps(obj: Any) -> str:
    """Serialize obj to a JSON formatted string using the custom encoder.

    Args:
        obj: The object to serialize

    Returns:
        JSON string representation
    """
    return json.dumps(obj, cls=CustomJSONEncoder)
