"""Custom logger abstraction over loguru."""

import sys
from typing import Any, Dict, Optional

from loguru import logger as loguru_logger

from infrastructure.config import fluentbit_tcp_sink
from infrastructure.logging_utils.structured_logger import log_structured
from infrastructure.middleware.log_enricher import request_context


class Logger:
    """Logger abstraction over loguru.

    This class provides a consistent interface for logging throughout the application.
    It wraps loguru functionality while allowing for additional configuration and customization.
    """

    _instance = None

    def __new__(cls):
        """Singleton pattern implementation."""
        if cls._instance is None:
            cls._instance = super(Logger, cls).__new__(cls)
            cls._instance._initialized = False
        return cls._instance

    def __init__(self):
        """Initialize the logger if not already initialized."""
        if self._initialized:
            return

        self._logger = loguru_logger
        self._initialized = False
        self._log_path = None
        self._log_level = "INFO"
        self._log_rotation = "10 MB"
        self._log_max_files = 5
        self._enable_fluentbit = True

    def configure(
        self,
        log_path: str,
        log_level: str = "INFO",
        log_rotation: str = "10 MB",
        log_max_files: int = 5,
        enable_console: bool = True,
        console_level: str = "INFO",
        enable_fluentbit: bool = True,
        fluentbit_level: str = "INFO",
    ) -> None:
        """Configure the logger.

        Args:
            log_path: Path to log file
            log_level: Log level for file logging
            log_rotation: When to rotate logs (size or time)
            log_max_files: Maximum number of log files to keep
            enable_console: Whether to enable console logging
            console_level: Log level for console output
            enable_fluentbit: Whether to enable FluentBit logging
            fluentbit_level: Log level for FluentBit
        """
        self._log_path = log_path
        self._log_level = log_level
        self._log_rotation = log_rotation
        self._log_max_files = log_max_files
        self._enable_fluentbit = enable_fluentbit

        # Remove existing handlers
        self._logger.remove()

        # Add file handler
        self._logger.add(
            log_path,
            format="{message}",
            rotation=log_rotation,
            level=log_level.upper(),
            retention=log_max_files,
            enqueue=True,
        )

        # Add console handler
        if enable_console:
            self._logger.add(
                sys.stdout,
                format="{message}",
                level=console_level.upper(),
                enqueue=True,
                backtrace=False,
            )

        # Add FluentBit handler
        if enable_fluentbit:
            self._logger.add(
                fluentbit_tcp_sink,
                level=fluentbit_level.upper(),
                format="{message}",
                serialize=False,
                enqueue=True,
                backtrace=False,
                diagnose=False,
            )

        self._initialized = True
        self._logger.info(
            f"Logger initialized with file path: {log_path}, level: {log_level}"
        )

    def _ensure_initialized(self) -> None:
        """Ensure logger is initialized with defaults if not configured."""
        if not self._initialized:
            self.configure("/var/logs.log")

    def debug(self, message: str) -> None:
        """Log a debug message.

        Args:
            message: The message to log
        """
        self._ensure_initialized()
        self._logger.debug(message)

    def info(self, message: str) -> None:
        """Log an info message.

        Args:
            message: The message to log
        """
        self._ensure_initialized()
        self._logger.info(message)

    def warning(self, message: str) -> None:
        """Log a warning message.

        Args:
            message: The message to log
        """
        self._ensure_initialized()
        self._logger.warning(message)

    def error(
        self, message: str, exception: Optional[Exception] = None
    ) -> None:
        """Log an error message.

        Args:
            message: The message to log
            exception: Optional exception to include in the log
        """
        self._ensure_initialized()
        if exception:
            self._logger.opt(exception=exception).error(message)
        else:
            self._logger.error(message)

    def structured(
        self,
        level: str,
        message: str,
        input_params: Optional[Dict[str, Any]] = None,
        output_params: Optional[Dict[str, Any]] = None,
        additional_props: Optional[Dict[str, Any]] = None,
        folder: Optional[str] = None,
        route_id: Optional[str] = None,
        route_name: Optional[str] = None,
        custom_links: Optional[Dict[str, str]] = None,
        custom_timings_json: Optional[str] = None,
        exception: Optional[Exception] = None,
    ) -> None:
        """Log a structured message.

        Args:
            level: Log level (debug, info, warning, error, critical)
            message: Log message
            input_params: Input parameters
            output_params: Output parameters
            additional_props: Additional properties
            folder: Folder key value
            route_id: Route ID
            route_name: Route name
            custom_links: Custom links
            custom_timings_json: Custom timings JSON
            exception: Optional exception to include in the log
        """
        self._ensure_initialized()

        # Get context from the context variable
        context = request_context.get()

        context = dict(context) if context else {}
        context["log_level"] = level.upper()

        log_structured(
            level=level,
            message=message,
            input_params=input_params,
            output_params=output_params,
            additional_props=additional_props,
            folder=folder,
            route_id=route_id,
            route_name=route_name,
            custom_links=custom_links,
            custom_timings_json=custom_timings_json,
            exception=exception,
            context=context,
        )

    def get_loguru_logger(self):
        """Get the underlying loguru logger.

        Returns:
            The loguru logger instance
        """
        return self._logger


# Create a singleton instance
logger = Logger()
