"""
ApplicationContext class to hold request context information.

This class is used to encapsulate all relevant information about the request
and is designed to be easily converted to a dictionary for metrics and logging.
"""

from dataclasses import dataclass
from datetime import datetime
from typing import Optional

from opentelemetry import trace


@dataclass
class ApplicationContext:
    """ApplicationContext class to hold request context information."""

    environment: str
    user_id: str
    user_name: str
    customer_id: str
    vendor: str
    account_id: str
    business_unit_id: str
    warehouse_id: str
    user_agent: str
    session_id: str
    trace_id: str
    span_id: str
    browser_id: str
    browser_detail: str
    application_name: str
    instance_name: str
    application_version: str
    correlation_id: str
    current_location: str
    original_request_uri: str
    current_request_uri: str
    partition_key: str
    parent_trace_id: Optional[str] = None
    message_id: Optional[str] = None
    parent_message_id: Optional[str] = None
    record_version: Optional[datetime] = None

    @classmethod
    def get_from_headers(cls, headers: dict) -> "ApplicationContext":
        """Create an ApplicationContext from request headers."""
        return cls(
            environment=headers.get("app-environment", "unknown"),
            user_id=headers.get("app-user-id", "unknown"),
            user_name=headers.get("user-name", "unknown"),
            customer_id=headers.get("app-customer-id", "unknown"),
            vendor=headers.get("vendor", "unknown"),
            account_id=headers.get("app-account-id", "unknown"),
            business_unit_id=headers.get("app-bu-id", "unknown"),
            warehouse_id=headers.get("app-warehouse-id", "unknown"),
            user_agent=headers.get("user-agent", "unknown"),
            session_id=headers.get("app-session-id", "unknown"),
            trace_id=format(
                trace.get_current_span().get_span_context().trace_id, "032x"
            ),
            span_id=format(
                trace.get_current_span().get_span_context().span_id, "016x"
            ),
            parent_trace_id=headers.get("app-parent-trace-id", None),
            browser_id=headers.get("app-browser-id", "unknown"),
            browser_detail=headers.get("app-browser-detail", "unknown"),
            record_version=headers.get("app-record-version", None),
            application_name=headers.get("app-application-name", "unknown"),
            instance_name=headers.get("app-instance-name", "unknown"),
            application_version=headers.get(
                "app-application-version", "unknown"
            ),
            correlation_id=headers.get("app-correlation-id", "unknown"),
            current_location=headers.get("app-current-location", "unknown"),
            original_request_uri=headers.get(
                "app-original-request-uri", "unknown"
            ),
            current_request_uri=headers.get(
                "app-current-request-uri", "unknown"
            ),
            partition_key=headers.get("app-partition-key", "unknown"),
        )

    def to_dict(self) -> dict:
        """Convert the ApplicationContext to a dictionary."""
        return {
            "Environment": self.environment,
            "UserId": self.user_id,
            "UserName": self.user_name,
            "CustomerId": self.customer_id,
            "Vendor": self.vendor,
            "AccountId": self.account_id,
            "BusinessUnitId": self.business_unit_id,
            "WarehouseId": self.warehouse_id,
            "UserAgent": self.user_agent,
            "SessionId": self.session_id,
            "TraceId": self.trace_id,
            "SpanId": self.span_id,
            "ParentTraceId": self.parent_trace_id,
            "BrowserId": self.browser_id,
            "BrowserDetail": self.browser_detail,
            "RecordVersion": (
                self.record_version.isoformat() if self.record_version else None
            ),
            "ApplicationName": self.application_name,
            "InstanceName": self.instance_name,
            "ApplicationVersion": self.application_version,
            "CorrelationId": self.correlation_id,
            "CurrentLocation": self.current_location,
            "OriginalRequestUri": self.original_request_uri,
            "CurrentRequestUri": self.current_request_uri,
            "PartitionKey": self.partition_key,
        }

    def to_metrics_dict(self) -> dict:
        """Convert the ApplicationContext to a dictionary suitable for metrics."""
        return {
            "environment": self.environment,
            "customer": self.customer_id,
            "account": self.account_id,
            "bu": self.business_unit_id,
            "warehouse": self.warehouse_id,
        }
