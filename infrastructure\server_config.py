"""Server configuration and setup utilities.

This module provides functions to configure and run the application server.
"""

from typing import Optional

import uvicorn
from opentelemetry import trace
from opentelemetry.exporter.otlp.proto.grpc.trace_exporter import (
    OTLPSpanExporter,
)
from opentelemetry.sdk.resources import Resource
from opentelemetry.sdk.trace import Tracer<PERSON>rovider
from opentelemetry.sdk.trace.export import BatchSpanProcessor

from infrastructure.logging_utils.logger import logger


def configure_telemetry(
    service_name: str, endpoint: str = "localhost:4317"
) -> None:
    """Configure OpenTelemetry for distributed tracing.

    Args:
        service_name: Name of the service for telemetry
        endpoint: OTLP endpoint for exporting spans
    """
    resource = Resource.create(
        {
            "service.name": service_name,
        }
    )

    # Create OTLP exporter
    otlp_exporter = OTLPSpanExporter(endpoint=endpoint, insecure=True)

    # Set up tracer provider and processor
    provider = TracerProvider(resource=resource)
    processor = BatchSpanProcessor(otlp_exporter)
    provider.add_span_processor(processor)
    trace.set_tracer_provider(provider)

    logger.info(f"OpenTelemetry configured with endpoint: {endpoint}")


def run_server(
    app_module: str = "api.app:app",
    port: int = 8000,
    ssl_keyfile: Optional[str] = None,
    ssl_certfile: Optional[str] = None,
) -> None:
    """Run the application server with the provided configuration.

    Args:
        app_module: Application module path in format "module:app"
        port: Server port
        ssl_keyfile: Path to SSL key file
        ssl_certfile: Path to SSL certificate file
        reload: Whether to enable auto-reload on code changes
        workers: Number of worker processes
    """
    logger.info(f"Starting server on port {port}")

    if ssl_certfile and ssl_keyfile:
        logger.info(f"SSL enabled with certificate: {ssl_certfile}")

    uvicorn_config = {"app": app_module, "host": "0.0.0.0", "port": port}

    if ssl_keyfile and ssl_certfile:
        uvicorn_config["ssl_keyfile"] = ssl_keyfile
        uvicorn_config["ssl_certfile"] = ssl_certfile

    uvicorn.run(**uvicorn_config)
