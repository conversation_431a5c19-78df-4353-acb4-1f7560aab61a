table_name,DDL,comments
customersetup.accounts,"CREATE TABLE customersetup.accounts (
    id varchar(50) NOT NULL,
    customerid varchar(50),
    businessunitid varchar(50),
    businessunitnumber varchar(50),
    accountcode varchar(15),
    description varchar(50),
    currency varchar(50),
    currencyid varchar(50),
    metrics varchar(50),
    metricsid varchar(50),
    isactive bpchar,
    activedate timestamp,
    inactivedate timestamp,
    isaggregate bpchar,
    createdby varchar(250),
    createddate timestamp,
    modifiedby varchar(250),
    modifieddate timestamp,
    timeinepoch numeric,
    isdeleted bpchar,
    isarchived bpchar,
    synapsefacilityname varchar(40),
    environmentcode varchar(40),
    isverticaldefault bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    updatedlocation varchar(200),
    returnaddressline1 varchar(200),
    returnaddressline2 varchar(200),
    returnaddressline3 varchar(200),
    returncity varchar(150),
    returnstatecode varchar(150),
    returnpostalcode varchar(15),
    returncountrycode varchar(2),
    allowreturns bpchar,
    allowexchange bpchar,
    disposition bpchar,
    synapsecustomerid varchar(40),
    enableprescript bpchar,
    enablepostscript bpchar,
    isadvanced bpchar,
    numberofshift numeric,
    lineofbusiness varchar(50),
    accountid varchar(40),
    emergencycontractnumber varchar(25),
    emergencydomesticcontact varchar(25),
    emergencyintlcontact varchar(25),
    returncontactname varchar(100),
    returnwindow int4,
	CONSTRAINT accounts_id PRIMARY KEY (id)
);","Customer account configurations and settings within business units. Defines account-level parameters including currency, return policies, emergency contacts, and operational settings for warehouse management."
customersetup.building,"CREATE TABLE customersetup.building (
    id varchar(50) NOT NULL,
    buildingcode varchar(50) NOT NULL,
    addressline1 varchar(200) NOT NULL,
    addressline2 varchar(200),
    city varchar(35) NOT NULL,
    state varchar(2) NOT NULL,
    statedescription varchar(40) NOT NULL,
    postalcode varchar(10) NOT NULL,
    country varchar(3) NOT NULL,
    countrydescription varchar(50),
    timezone varchar(50) NOT NULL,
    phonenumber varchar(20),
    isactive bpchar,
    activedate timestamp,
    inactivedate timestamp,
    createdby varchar(250),
    createddate timestamp,
    modifiedby varchar(250),
    modifieddate timestamp,
    timeinepoch numeric,
    isdeleted bpchar,
    isarchived bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    updatedlocation varchar(200),
    buildingdescription varchar(200),
    businessunitid varchar(50),
    accountid varchar(50),
    customerid varchar(50),
    warehouseid varchar(50),
    timezonedescription varchar(40),
    addressline3 varchar(50),
	CONSTRAINT building_id PRIMARY KEY (id)
);","Physical building and facility information for warehouse locations. Contains address details, timezone settings, and contact information for warehouse buildings and distribution centers."
customersetup.businessunits,"CREATE TABLE customersetup.businessunits (
    id varchar(50) NOT NULL,
    customerid varchar(50),
    businessunitnumber varchar(50) NOT NULL,
    synapsecustomername varchar(50),
    activedate timestamp,
    inactivedate timestamp,
    isactive bpchar,
    createdby varchar(50),
    createddate timestamp,
    modifiedby varchar(50),
    modifieddate timestamp,
    timeinepoch numeric,
    isdeleted bpchar,
    channelcode varchar(4),
    verticalcode varchar(4),
    verticalid varchar(40),
    channelid varchar(40),
    businessunitname varchar(100),
    isexternal bpchar,
	CONSTRAINT businessunits_id PRIMARY KEY (id)
);","Business unit definitions within customer organizations. Represents operational divisions or subsidiaries with distinct business processes, channels, and vertical market focus areas."
customersetup.cartposition,"CREATE TABLE customersetup.cartposition (
    id varchar(40) NOT NULL,
    pickcartid varchar(40),
    position numeric,
    worktype varchar(40),
    cartontype varchar(40),
    createdby varchar(250),
    createddate timestamp,
    modifiedby varchar(250),
    modifieddate timestamp,
    timeinepoch numeric,
    isdeleted bpchar,
    isarchived bpchar,
    positionidentifier varchar(40),
    containerid varchar(40),
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    updatedlocation varchar(200),
    customerid varchar(50),
    accountid varchar(50),
    businessunitid varchar(50),
    warehouseid varchar(50),
	CONSTRAINT cartposition_id PRIMARY KEY (id)
);","Pick cart position configurations defining specific positions on warehouse picking carts. Used to organize and track container placement on mobile picking equipment for order fulfillment operations."
customersetup.container,"CREATE TABLE customersetup.container (
    id varchar(40) NOT NULL,
    businessunitid varchar(40),
    warehouseid varchar(40),
    accountid varchar(40),
    containerclass varchar(40),
    containegroup varchar(40),
    containername varchar(40),
    description varchar(40),
    volumemin numeric,
    volumemax numeric,
    volumeuom varchar(10),
    weightmin numeric,
    weightmax numeric,
    weightuom varchar(10),
    unitmax numeric,
    unituom varchar(10),
    dimensionslength numeric,
    dimensionswidth numeric,
    dimensionsheight numeric,
    dimensionsuom varchar(10),
    createdby varchar(250),
    createddate timestamp,
    modifiedby varchar(250),
    modifieddate timestamp,
    timeinepoch numeric,
    isdeleted bpchar,
    isarchived bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    updatedlocation varchar(200),
    customerid varchar(50),
	CONSTRAINT container_id PRIMARY KEY (id)
);","Container type definitions including boxes, cartons, pallets, and other packaging containers. Specifies dimensional constraints, weight limits, and volume capacities for warehouse packaging and shipping operations."
customersetup.customers,"CREATE TABLE customersetup.customers (
    id varchar(50) NOT NULL,
    name varchar(50),
    abbreviation varchar(50),
    addressline1 varchar(200),
    addressline2 varchar(200),
    addressline3 varchar(200),
    city varchar(35),
    state varchar(50),
    postalcode varchar(10),
    country varchar(50),
    contactname varchar(100),
    contactemail varchar(100),
    contactphone varchar(100),
    url varchar(200),
    logo varchar(200),
    marketlineid varchar(50),
    isactive bpchar,
    activedate timestamp,
    inactivedate timestamp,
    isdataretention int2,
    personalretention int4,
    orderretention int4,
    parcellabelretention int4,
    ismarketlineaccount int2,
    createdby varchar(250),
    createddate timestamp,
    modifiedby varchar(250),
    modifieddate timestamp,
    timeinepoch int4,
    isdeleted bpchar,
    isarchived bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    updatedlocation varchar(200),
    marketlinedescription varchar(100),
    marketlineleader varchar(100),
    marketlineowner varchar(100),
    imageurl varchar(150),
	CONSTRAINT customers_id PRIMARY KEY (id)
);","Master customer records containing all customer information including name, address, contact details, and data retention policies.Any customer related details should be searched here "
customersetup.locationorganizations,"CREATE TABLE customersetup.locationorganizations (
    id varchar(50) NOT NULL,
    locationid varchar(50) NOT NULL,
    organizationid varchar(50) NOT NULL,
    createdby varchar(250),
    createddate timestamp,
    modifiedby varchar(250),
    modifieddate timestamp,
    timeinepoch numeric,
    isdeleted bpchar,
    isarchived bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    updatedlocation varchar(200),
    customerid varchar(50),
    accountid varchar(50),
    businessunitid varchar(50),
    warehouseid varchar(50),
    accountcode varchar(15),
    businessunitnumber varchar(50),
	CONSTRAINT locationorganizations_id PRIMARY KEY (id)
);","Association table linking warehouse locations to organizational units. Defines which organizations have access to or responsibility for specific warehouse locations and storage areas."
customersetup.locations,"CREATE TABLE customersetup.locations (
    id varchar(50) NOT NULL,
    warehouseid varchar(50),
    warehouse varchar(40),
    description varchar(200),
    warehousedescription varchar(40),
    warehouseabbreviation varchar(50),
    warehousename varchar(200) NOT NULL,
    name varchar(40),
    locationdescription varchar(200),
    aisle varchar(50),
    areaid varchar(50),
    area varchar(40),
    areadescription varchar(40),
    bay varchar(50),
    level varchar(30),
    position varchar(50),
    zoneid varchar(50),
    levelcode varchar(50),
    outboundzoneid varchar(50),
    outboundzone varchar(40),
    outboundzonedescription varchar(40),
    inboundzoneid varchar(50),
    inboundzone varchar(40),
    slottingzoneid varchar(50),
    slottingzone varchar(40),
    slottingzonedescription varchar(40),
    putawayzoneid varchar(50),
    putawayzone varchar(40),
    putawayzonedescription varchar(40),
    subzoneid varchar(50),
    subzone varchar(40),
    subzonedescription varchar(40),
    locationtypeid varchar(50),
    locationtype varchar(40),
    locationtypedescription varchar(40),
    locationcategoryid varchar(50),
    locationcategorydescription varchar(50),
    locationcategory varchar(50),
    locationhandlingid varchar(50),
    locationhandling varchar(40),
    locationhandlingdescription varchar(40),
    checkdigit varchar(50),
    abc varchar(50),
    locationstatusid varchar(50),
    locationstatus varchar(40),
    locationstatusdescription varchar(40),
    locationflagid varchar(50),
    locationflag varchar(40),
    locationflagdescription varchar(40),
    equipmentprofileid varchar(50),
    equipmentprofile varchar(40),
    equipmentprofiledescription varchar(40),
    routesequencecyclecount int4,
    routesequencepick int4,
    routesequenceputaway int4,
    dimensionheight int4,
    dimensionlength int4,
    dimensionwidth int4,
    xcoordinate int4,
    ycoordinate int4,
    zcoordinate int4,
    orientation varchar(50),
    cubiccapacityheight int4,
    cubiccapacitylength int4,
    cubiccapacitywidth int4,
    weightcapacity int4,
    fillpercentage int4,
    stacklimit int4,
    footprint int4,
    cycleclassid varchar(50),
    cycleclass varchar(40),
    cycleclassdescription varchar(40),
    velocity varchar(4),
    activepicktaskscount int4,
    activecctaskscount int4,
    lastcounted timestamp,
    createdby varchar(250),
    createddate timestamp,
    modifiedby varchar(250),
    modifieddate timestamp,
    timeinepoch numeric,
    isdeleted bpchar,
    isarchived bpchar,
    isloseid bpchar,
    ismixedlot bpchar,
    ismixedbatch bpchar,
    ismixeditem bpchar,
    ismixeduom bpchar,
    ismixedvendor bpchar,
    ismixedsupplier bpchar,
    ismixedmanufacture bpchar,
    ismixedowner bpchar,
    ismixedinventorystatus bpchar,
    ispickfront bpchar,
    isstorage bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    updatedlocation varchar(200),
    accountid varchar(50),
    businessunitid varchar(50),
    customerid varchar(50),
    zone varchar(40),
    zonedescription varchar(40),
    cubiccapacityuom varchar(4),
    dimensionuom varchar(4),
    location varchar(40),
    slottingroutesequence varchar(50),
    uom varchar(4),
    lastcounteduser varchar(50),
    lastcountedrelease timestamp,
    lastcountedreleaseuser varchar(50),
    lastreplenish timestamp,
    lastreplenishuser varchar(50),
    lastpick timestamp,
    lastpickuser varchar(50),
    uomid varchar(50),
	CONSTRAINT locations_id PRIMARY KEY (id)
);","Comprehensive warehouse location master data including storage positions, zones, areas, and location attributes. Defines the physical warehouse layout with coordinates, capacities, and operational characteristics for inventory placement and picking operations."
customersetup.organizations,"CREATE TABLE customersetup.organizations (
    id varchar(50) NOT NULL,
    businessunitid varchar(50),
    accountid varchar(50),
    customerid varchar(50),
    createdby varchar(250),
    createddate timestamp,
    modifiedby varchar(250),
    modifieddate timestamp,
    timeinepoch numeric,
    isdeleted bpchar,
    isarchived bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    updatedlocation varchar(200),
    businessunitnumber varchar(50),
    accountcode varchar(50),
    accountdescription varchar(50),
    metrics varchar(40),
    metricsid varchar(40),
    currency varchar(50),
    isverticaldefault bpchar,
    activedate timestamp,
    inactivedate timestamp,
    isaggregate bpchar,
    synapsefacilityname varchar(100),
    allowexchange bpchar,
    allowreturns bpchar,
    disposition bpchar,
    synapsecustomerid varchar(40),
    returnaddress1 varchar(200),
    returnaddress2 varchar(200),
    returnaddress3 varchar(200),
    enableprescript bpchar,
    enablepostscript bpchar,
    returncity varchar(150),
    returnstatecode varchar(150),
    returncountrycode varchar(2),
    returnpostalcode varchar(15),
    isactive bpchar,
    accountenvironment varchar(40),
    isadvanced bpchar,
    numberofshift numeric,
    lineofbusiness varchar(50),
    currencyid varchar(40),
    enablepresp bpchar,
    enablepostsp bpchar,
    emergencycontractnumber varchar(25),
    emergencydomesticcontact varchar(25),
    emergencyintlcontact varchar(25),
    emergencyinternationalcontact varchar(25),
	CONSTRAINT organizations_id PRIMARY KEY (id)
);","Organizational structure definitions combining business units and accounts into operational entities. Consolidates customer hierarchy information for warehouse operations, billing, and access control management."
customersetup.organizationshifts,"CREATE TABLE customersetup.organizationshifts (
    id varchar(50) NOT NULL,
    organizationid varchar(50),
    shiftid varchar(50),
    issunday bpchar,
    ismonday bpchar,
    istuesday bpchar,
    iswednesday bpchar,
    isthursday bpchar,
    isfriday bpchar,
    issaturday bpchar,
    starttime varchar(5),
    endtime varchar(5),
    createdby varchar(250),
    createddate timestamp,
    modifiedby varchar(250),
    modifieddate timestamp,
    timeinepoch numeric,
    isdeleted bpchar,
    isarchived bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    updatedlocation varchar(200),
    accountid varchar(50),
    businessunitid varchar(50),
    customerid varchar(50),
    warehouseid varchar(50),
    shiftcode varchar(40),
	CONSTRAINT organizationshifts_id PRIMARY KEY (id)
);","Work shift schedules for organizational units defining operating hours and days. Manages warehouse operational schedules including start/end times and day-of-week coverage for workforce planning and system availability."
customersetup.organizationstores,"CREATE TABLE customersetup.organizationstores (
    id varchar(40) NOT NULL,
    organizationid varchar(40),
    storeid varchar(40),
    createdby varchar(250),
    createddate timestamp,
    modifiedby varchar(250),
    modifieddate timestamp,
    timeinepoch numeric,
    isdeleted bpchar,
    isarchived bpchar,
    isdefault bpchar,
    platformid varchar(40),
    storename varchar(256),
    accesstoken varchar(256),
    isregistered bpchar,
    isenabled bpchar,
    orderwebhooksenabled bpchar,
    productwebhooksenabled bpchar,
    fulfillmentwebhooksenabled bpchar,
    apisecret varchar(50),
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    updatedlocation varchar(200),
    accountid varchar(50),
    businessunitid varchar(50),
    customerid varchar(50),
    warehouseid varchar(50),
    storeorganizationid varchar(40),
    accountcode varchar(15),
    businessunitnumber varchar(50),
    platform varchar(40),
	CONSTRAINT organizationstores_id PRIMARY KEY (id)
);","E-commerce store integrations linking organizations to online sales platforms. Manages API connections, webhooks, and store configurations for order synchronization from external sales channels like Shopify, Amazon, eBay."
customersetup.pickcart,"CREATE TABLE customersetup.pickcart (
    id varchar(40) NOT NULL,
    pickcartcode varchar(40),
    statuscode varchar(40),
    locationid varchar(40),
    createdby varchar(250),
    createddate timestamp,
    modifiedby varchar(250),
    modifieddate timestamp,
    timeinepoch numeric,
    isdeleted bpchar,
    isarchived bpchar,
    warehouseid varchar(40),
    containerid varchar(40),
    carttype varchar(10),
    barcodetype varchar(10),
    description varchar(40),
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    updatedlocation varchar(200),
    accountid varchar(50),
    businessunitid varchar(50),
    customerid varchar(50),
	CONSTRAINT pickcart_id PRIMARY KEY (id)
);","Mobile picking cart equipment definitions used for warehouse order fulfillment. Tracks cart configurations, status, locations, and container assignments for efficient picking operations and equipment management."
customersetup.vendor,"CREATE TABLE customersetup.vendor (
    id varchar(50) NOT NULL,
    businessunitid varchar(50),
    vendorcode varchar(50),
    vendorname varchar(50),
    contact varchar(50),
    addressline1 varchar(50),
    addressline2 varchar(50),
    city varchar(50),
    state varchar(50),
    countrycode varchar(10),
    postalcode varchar(10),
    email varchar(50),
    createdby varchar(250),
    createddate timestamp,
    modifiedby varchar(250),
    modifieddate timestamp,
    timeinepoch numeric,
    isdeleted bpchar,
    isarchived bpchar,
    isvendor bpchar,
    issupplier bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    updatedlocation varchar(200),
    accountid varchar(50),
    customerid varchar(50),
    warehouseid varchar(50),
    addressline3 varchar(50),
    phonenumber varchar(20),
	CONSTRAINT vendor_id PRIMARY KEY (id)
);","Vendor and supplier master data including contact information and business relationships. Manages supplier information for inventory sourcing, purchase orders, and inbound shipment processing in the warehouse management system."
customersetup.warehousedocuments,"CREATE TABLE customersetup.warehousedocuments (
    id varchar(50) NOT NULL,
    warehouseid varchar(50) NOT NULL,
    filename varchar(200) NOT NULL,
    filetype varchar(50) NOT NULL,
    url varchar(200) NOT NULL,
    uploaddate timestamp,
    createdby varchar(250),
    createddate timestamp,
    modifiedby varchar(250),
    modifieddate timestamp,
    timeinepoch numeric,
    isdeleted bpchar,
    isarchived bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    updatedlocation varchar(200),
    accountid varchar(50),
    businessunitid varchar(50),
    customerid varchar(50),
	CONSTRAINT warehousedocuments_id PRIMARY KEY (id)
);","Document templates and configurations for warehouse operations including labels, reports, and forms. Manages document generation settings, templates, and formatting for warehouse documentation and compliance requirements."
customersetup.warehouseorganizations,"CREATE TABLE customersetup.warehouseorganizations (
    id varchar(50) NOT NULL,
    warehouseid varchar(50) NOT NULL,
    organizationid varchar(50) NOT NULL,
    isactive bpchar,
    activedate timestamp,
    inactivedate timestamp,
    createdby varchar(250),
    createddate timestamp,
    modifiedby varchar(250),
    modifieddate timestamp,
    timeinepoch numeric,
    isdeleted bpchar,
    isarchived bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    updatedlocation varchar(200),
    accountid varchar(50),
    businessunitid varchar(50),
    customerid varchar(50),
    accountdescription varchar(40),
    accountcode varchar(15),
    businessunitnumber varchar(50),
    customer varchar(50),
	CONSTRAINT warehouseorganizations_id PRIMARY KEY (id)
);","Association table linking warehouses to organizational units for access control and operational responsibility. Defines which organizations can operate within specific warehouse facilities and their operational scope."
customersetup.warehouses,"CREATE TABLE customersetup.warehouses (
    id varchar(50) NOT NULL,
    name varchar(200),
    campusid varchar(50),
    abbreviation varchar(50),
    addressline1 varchar(200),
    addressline2 varchar(200),
    addressline3 varchar(200),
    city varchar(35),
    state varchar(2),
    postalcode varchar(10),
    country varchar(3),
    timezone varchar(50),
    isactive bpchar,
    activedate timestamp,
    inactivedate timestamp,
    createdby varchar(250),
    createddate timestamp,
    modifiedby varchar(250),
    modifieddate timestamp,
    timeinepoch numeric,
    isdeleted bpchar,
    isarchived bpchar,
    buildingid varchar(50),
    phonenumber varchar(20),
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    updatedlocation varchar(200),
    buildingcode varchar(50),
    campuscode varchar(40),
    campuscodedescription varchar(40),
    statedescription varchar(40),
    countrydescription varchar(40),
    timezonedescription varchar(40),
	CONSTRAINT warehouses_id PRIMARY KEY (id)
);","Warehouse facility master data including location details, operational settings, and configuration parameters. Central table defining warehouse properties, addresses, time zones, and operational characteristics for distribution centers."
customersetup.warehousesites,"CREATE TABLE customersetup.warehousesites (
    id varchar(50) NOT NULL,
    warehouseid varchar(50) NOT NULL,
    createdby varchar(250),
    createddate timestamp,
    modifiedby varchar(250),
    modifieddate timestamp,
    timeinepoch numeric,
    isdeleted bpchar,
    isarchived bpchar,
    siteid varchar(50),
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    updatedlocation varchar(200),
    accountid varchar(50),
    businessunitid varchar(50),
    customerid varchar(50),
    sitecode varchar(40) NOT NULL,
	CONSTRAINT warehousesites_id PRIMARY KEY (id)
);","Warehouse site configurations and operational parameters for specific warehouse locations. Manages site-specific settings, operational rules, and configuration parameters that control warehouse management system behavior at individual facilities."
document.labeltemplates,"CREATE TABLE document.labeltemplates (
    id varchar(50) NOT NULL,
    createddate timestamp,
    createdby varchar(20),
    modifieddate timestamp,
    modifiedby varchar(20),
    timeinepoch numeric,
    effectivestartdate timestamp,
    effectiveenddate timestamp,
    code varchar(20),
    description varchar(150),
    typeid varchar(50),
    template bytea,
    templateschema bytea,
    isdeleted bpchar,
    isarchived bpchar,
    isdefault bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    updatedlocation varchar(200),
    templateheight numeric,
    templatewidth numeric,
    accountid varchar(50),
    businessunitid varchar(50),
    customerid varchar(50),
    warehouseid varchar(50),
    partitionkey varchar(10),
    CONSTRAINT labeltemplates_id PRIMARY KEY (id, id)
);","Stores configuration details for label templates used in the system, including template content, dimensions, associated metadata, and versioning information."
document.labeltypes,"CREATE TABLE document.labeltypes (
    id varchar(50) NOT NULL,
    createddate timestamp,
    createdby varchar(20),
    modifieddate timestamp,
    modifiedby varchar(20),
    timeinepoch numeric,
    code varchar(40),
    description varchar(150),
    isdeleted bpchar,
    isarchived bpchar,
    isdefault bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    updatedlocation varchar(200),
    accountid varchar(50),
    businessunitid varchar(50),
    customerid varchar(50),
    warehouseid varchar(50),
    partitionkey varchar(10),
    CONSTRAINT labeltypes_id PRIMARY KEY (id, id)
);","Defines different types of labels available, including metadata such as code, description, and lifecycle status (archived, deleted, etc.)."
document.shiplabeltemplates,"CREATE TABLE document.shiplabeltemplates (
    id varchar(50) NOT NULL,
    createddate timestamp,
    createdby varchar(20),
    modifieddate timestamp,
    modifiedby varchar(20),
    timeinepoch numeric,
    effectivestartdate timestamp,
    effectiveenddate timestamp,
    typeid varchar(50),
    shipserviceid varchar(50),
    template bytea,
    templateschema bytea,
    isdeleted bpchar,
    isarchived bpchar,
    isdefault bpchar,
    templatecodeid varchar(50),
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    updatedlocation varchar(200),
    templateheight numeric,
    templatewidth numeric,
    accountid varchar(50),
    businessunitid varchar(50),
    customerid varchar(50),
    warehouseid varchar(50),
    partitionkey varchar(10),
    CONSTRAINT shiplabeltemplates_id PRIMARY KEY (id, id)
);","Contains templates specifically designed for shipping labels, linked to ship services and template codes, along with schema and layout metadata."
document.shipservices,"CREATE TABLE document.shipservices (
    id varchar(50) NOT NULL,
    createddate timestamp,
    createdby varchar(20),
    modifieddate timestamp,
    modifiedby varchar(20),
    timeinepoch numeric,
    description varchar(150),
    carrier varchar(50),
    service varchar(50),
    isdeleted bpchar,
    isarchived bpchar,
    isdefault bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    updatedlocation varchar(200),
    accountid varchar(50),
    businessunitid varchar(50),
    customerid varchar(50),
    warehouseid varchar(50),
    partitionkey varchar(10),
    CONSTRAINT shipservices_id PRIMARY KEY (id, id)
);","Stores information about shipping services including carrier and service types, along with audit and organizational metadata."
document.templatecodes,"CREATE TABLE document.templatecodes (
    id varchar(50) NOT NULL,
    createddate timestamp,
    createdby varchar(20),
    modifieddate timestamp,
    modifiedby varchar(20),
    timeinepoch numeric,
    code varchar(25),
    description varchar(100),
    isdeleted bpchar,
    isarchived bpchar,
    isdefault bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    updatedlocation varchar(200),
    accountid varchar(50),
    businessunitid varchar(50),
    customerid varchar(50),
    warehouseid varchar(50),
    partitionkey varchar(10),
    CONSTRAINT templatecodes_id PRIMARY KEY (id, id)
);","Houses reusable codes and descriptions associated with templates, enabling categorization and standardization of template configurations."
inventorycontrol.cyclecountactivitydetail,"CREATE TABLE inventorycontrol.cyclecountactivitydetail (
    id varchar(40) NOT NULL,
    warehouseid varchar(40),
    cyclecountactivityid varchar(40),
    plateid varchar(40),
    taskid varchar(40),
    tasknumber varchar(40),
    activityuser varchar(320),
    activitydate timestamp,
    customerid varchar(40),
    status varchar(4),
    statusdesc varchar(80),
    cyclecountid varchar(40),
    businessunitidentered varchar(40),
    accountidentered varchar(40),
    itemidentered varchar(40),
    quantityentered numeric,
    platenumberentered varchar(50),
    lotnumberentered varchar(80),
    vendorentered varchar(40),
    locationidentered varchar(40),
    serialnumberentered varchar(80),
    manufacturedateentered date,
    expirationdateentered date,
    countryoforiginentered varchar(4),
    caseqrcodeentered varchar(80),
    inventoryclassentered varchar(4),
    unitofmeasureentered varchar(4),
    warehouse varchar(10),
    businessunitid varchar(40),
    accountid varchar(40),
    account varchar(10),
    itemcodeentered varchar(10),
    locationentered varchar(10),
    systemplatenumber varchar(40),
    systemitemid varchar(40),
    systemquantity numeric,
    itemcode varchar(10),
    systemlotnumber varchar(80),
    systemvendor varchar(40),
    systemlocationid varchar(40),
    location varchar(10),
    systemserialnumber varchar(80),
    systemmanufacturedate date,
    systemexpirationdate date,
    systemcountryoforigin varchar(4),
    systemunitofmeasure varchar(4),
    systemcaseqrcode varchar(80),
    systeminventoryclass varchar(4),
    matchingtaskid varchar(40),
    isuncountedcount bpchar,
    countsequence numeric,
    businessunit varchar(10),
    countseq numeric,
    createdby varchar(320),
    createddate timestamp,
    modifiedby varchar(320),
    modifieddate timestamp,
    timeinepoch numeric,
    isdeleted bpchar,
    isarchived bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    updatedlocation varchar(200),
    platenumber varchar(50),
    itemid varchar(40),
    quantity numeric,
    lotnumber varchar(80),
    vendor varchar(40),
    locationid varchar(40),
    serialnumber varchar(80),
    manufacturedate date,
    expirationdate date,
    countryoforigin varchar(4),
    unitofmeasure varchar(4),
    caseqrcode varchar(80),
    inventoryclass varchar(4),
	CONSTRAINT cyclecountactivitydetail_id PRIMARY KEY (id)
);","Records the detailed activities performed during a cycle count, including entered quantities, item information, and user activity. Essential for auditing and reconciling inventory counts."
inventorycontrol.cyclecountadjustment,"CREATE TABLE inventorycontrol.cyclecountadjustment (
    id varchar(40) NOT NULL,
    plateid varchar(40),
    platenumber varchar(50),
    countadjustmentactivityid varchar(40),
    businessunitid varchar(40),
    accountid varchar(40),
    customerid varchar(40),
    warehouseid varchar(50),
    locationid varchar(40),
    itemid varchar(40),
    lotnumber varchar(40),
    unitofmeasure varchar(4),
    quantity numeric,
    serialnumber varchar(80),
    inventoryclass varchar(4),
    vendor varchar(40),
    expirationdate date,
    manufacturedate date,
    inventorystatus varchar(4),
    countmode varchar(20),
    taskid varchar(40),
    adjustmenttype numeric,
    caseqrcode varchar(80),
    parentplateid varchar(40),
    countid varchar(40),
    platestatus varchar(4),
    status varchar(4),
    statusdescription varchar(10),
    overridecomment varchar(256),
    overridereason varchar(40),
    overrideholdreason varchar(40),
    cyclecountid varchar(40),
    parentinventoryplatenumber varchar(15),
    createdby varchar(320),
    createddate timestamp,
    modifiedby varchar(320),
    modifieddate timestamp,
    timeinepoch numeric,
    isdeleted bpchar,
    isarchived bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    updatedlocation varchar(200),
	CONSTRAINT cyclecountadjustment_id PRIMARY KEY (id)
);","Stores details of inventory adjustments made as a result of cycle counts, including quantity changes, reasons for adjustment, and associated inventory plates. Critical for maintaining accurate inventory records."
inventorycontrol.cyclecountsubtask,"CREATE TABLE inventorycontrol.cyclecountsubtask (
    id varchar(40) NOT NULL,
    taskid varchar(40),
    accountid varchar(40),
    businessunitid varchar(40),
    customerid varchar(40),
    warehouseid varchar(50),
    itemid varchar(40),
    platenumber varchar(50),
    lotnumber varchar(80),
    tasknumber varchar(80),
    businessunit varchar(10),
    account varchar(10),
    itemcode varchar(10),
    itemdesc varchar(80),
    quantitymin numeric,
    quantitymax numeric,
    inventoryclass varchar(4),
    inventorystatus varchar(4),
    inventoryclassdescription varchar(80),
    inventorystatusdescription varchar(80),
    vendor varchar(40),
    createddate timestamp,
    createdby varchar(320),
    modifieddate timestamp,
    modifiedby varchar(320),
    timeinepoch numeric,
    taskstarted timestamp,
    taskended timestamp,
    taskstartuser varchar(320),
    isarchived bpchar,
    isdeleted bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    updatedlocation varchar(200),
	CONSTRAINT cyclecountsubtask_id PRIMARY KEY (id)
);","Divides cycle count tasks into smaller, manageable subtasks, specifying items, plate numbers, and quantity ranges to be counted. Facilitates efficient execution of cycle count activities."
inventorycontrol.cyclecounttask,"CREATE TABLE inventorycontrol.cyclecounttask (
    id varchar(40) NOT NULL,
    identitycode varchar(20),
    warehouseid varchar(40),
    warehouse varchar(10),
    businessunitid varchar(40),
    businessunit varchar(10),
    accountid varchar(40),
    account varchar(10),
    locationid varchar(40),
    location varchar(20),
    locationentered varchar(20),
    customerid varchar(40),
    zoneid varchar(40),
    zonecode varchar(40),
    cyclecountid varchar(40),
    priority numeric,
    prioritydesc varchar(80),
    previouspriority numeric,
    previousprioritydesc varchar(80),
    assigneduser varchar(320),
    activeuser varchar(320),
    status numeric,
    statusdesc varchar(10),
    previousstatus numeric,
    locationsequence numeric,
    locationtype varchar(40),
    cyclecountseq numeric,
    requestid varchar(40),
    equipmentprofileid varchar(40),
    loadnumber int4,
    lastcountedby varchar(320),
    taskstarted timestamp,
    taskended timestamp,
    taskstartuser varchar(320),
    cyclecountactivityid varchar(40),
    taskid varchar(40),
    tasknumber varchar(40),
    activityuser varchar(320),
    activitydate timestamp,
    plateid varchar(40),
    countseq numeric,
    itemidentered varchar(40),
    quantityentered numeric,
    platenumberentered varchar(50),
    lotnumberentered varchar(80),
    vendorentered varchar(40),
    locationidentered varchar(40),
    serialnumberentered varchar(80),
    manufacturedateentered date,
    expirationdateentered date,
    countryoforiginentered varchar(4),
    caseqrcodeentered varchar(4),
    inventoryclassentered varchar(4),
    unitofmeasureentered varchar(4),
    matchingtaskid varchar(40),
    systemquantity numeric,
    systemlotnumber varchar(80),
    systemvendor varchar(40),
    systemlocationid varchar(40),
    systemserialnumber varchar(80),
    systemmanufacturedate date,
    systemexpirationdate date,
    systemcountryoforigin varchar(4),
    systemunitofmeasure varchar(4),
    systemcaseqrcode varchar(80),
    systeminventoryclass varchar(4),
    systemitemid varchar(40),
    isuncountedcount bpchar,
    itemcodeentered varchar(40),
    itemcode varchar(40),
    createdby varchar(320),
    createddate timestamp,
    modifiedby varchar(320),
    modifieddate timestamp,
    timeinepoch numeric,
    isarchived bpchar,
    isdeleted bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    updatedlocation varchar(200),
    traceid varchar(40),
    spanid varchar(40),
    systembusinessunitid varchar(50),
    systemaccountid varchar(50),
    systemplatenumber varchar(40),
	CONSTRAINT cyclecounttask_id PRIMARY KEY (id)
);","Defines individual cycle count tasks, including assigned users, locations, and status. Manages the workflow of physical inventory counts for specific areas or items."
inventorycontrol.inventoryadjustmentactivity,"CREATE TABLE inventorycontrol.inventoryadjustmentactivity (
    id varchar(40) NOT NULL,
    plateid varchar(40),
    platenumber varchar(50),
    originalbusinessunit varchar(10),
    originalaccountid varchar(40),
    originallocationid varchar(40),
    originalitemid varchar(40),
    originalinventoryclass varchar(4),
    originallotnumber varchar(80),
    originalserialnumber varchar(80),
    originalexpirationdate timestamp,
    originalmanufacturedate timestamp,
    originalquantity numeric,
    originalweight numeric,
    originalvendor varchar(40),
    originalcountryoforigin varchar(4),
    originalinventorystatus varchar(4),
    originalowner varchar(40),
    originalcaseqrcode varchar(80),
    originalbatchnumber varchar(80),
    originalcolorcode varchar(20),
    originalmanufacturingsiteid varchar(40),
    businessunitid varchar(40),
    businessunit varchar(10),
    accountid varchar(40),
    account varchar(10),
    locationid varchar(40),
    location varchar(10),
    customerid varchar(40),
    warehouseid varchar(40),
    warehouse varchar(10),
    itemid varchar(40),
    itemnumber varchar(10),
    inventoryclass varchar(4),
    lotnumber varchar(80),
    serialnumber varchar(80),
    expirationdate timestamp,
    manufacturedate timestamp,
    quantity numeric,
    weight numeric,
    vendor varchar(40),
    countryoforigin varchar(4),
    inventorystatus varchar(4),
    owner varchar(40),
    caseqrcode varchar(80),
    batchnumber varchar(80),
    colorcode varchar(20),
    manufacturingsiteid varchar(40),
    adjustmentreason varchar(20),
    adjusteduser varchar(320),
    tasktype varchar(20),
    adjustmentcomment varchar(256),
    createdby varchar(320),
    createddate timestamp,
    modifiedby varchar(320),
    modifieddate timestamp,
    timeinepoch numeric,
    isdeleted bpchar,
    isarchived bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    updatedlocation varchar(200),
    originalbusinessunitid varchar(40),
    originalaccount varchar(10),
    originallocationcode varchar(10),
    originalitemcode varchar(10),
    locationcode varchar(10),
    itemcode varchar(10),
	CONSTRAINT inventoryadjustmentactivity_id PRIMARY KEY (id)
);","Logs all inventory adjustments, capturing before and after states of inventory attributes, adjustment reasons, and user information. Provides a comprehensive audit trail for inventory changes."
inventorycontrol.inventoryplate,"CREATE TABLE inventorycontrol.inventoryplate (
    id varchar(50) NOT NULL,
    ancestor varchar(40),
    parentid varchar(50),
    platenumber varchar(40),
    customerid varchar(50),
    type varchar(4),
    typedescription varchar(10),
    status varchar(4),
    statusdescription varchar(10),
    businessunitid varchar(50),
    accountid varchar(50),
    businessunit varchar(10),
    account varchar(10),
    warehouseid varchar(50),
    warehouse varchar(10),
    locationid varchar(50),
    itemid varchar(50),
    item varchar(10),
    itemdescription varchar(80),
    parentlicenseplatenumber varchar(15),
    productgroupid varchar(40),
    productgroup varchar(50),
    quantity numeric,
    enteredquantity numeric,
    weight numeric,
    baseuom varchar(4),
    entereduom varchar(4),
    serialnumber varchar(80),
    batchnumber varchar(80),
    caseqrcode varchar(80),
    countryoforigin varchar(4),
    vendor varchar(40),
    owner varchar(40),
    supplier varchar(40),
    inventoryclass varchar(4),
    inventorystatus varchar(4),
    inventoryclassdescription varchar(80),
    inventorystatusdescription varchar(80),
    lotnumber varchar(80),
    manufacturedate date,
    expirationdate date,
    anivesarydate date,
    colorcode varchar(20),
    mfgsiteid varchar(40),
    sizecode varchar(40),
    imei varchar(20),
    imei2 varchar(20),
    isbn varchar(20),
    upc varchar(20),
    inboundweight numeric,
    inboundheight numeric,
    inboundwidth numeric,
    inboundlength numeric,
    comments varchar(350),
    reusabilitycode varchar(40),
    loadnumber numeric,
    lastcountdate timestamp,
    lastcountuser varchar(320),
    lastcounttaskid varchar(40),
    lastoperator varchar(320),
    location varchar(10),
    locationtypecode varchar(10),
    locationtypedescription varchar(80),
    pickuom varchar(4),
    baseuomdescription varchar(4),
    pickuomdescription varchar(4),
    countryoforigincode varchar(4),
    gs1barcode varchar(80),
    asnnumber varchar(80),
    createdby varchar(320),
    createddate timestamp,
    modifiedby varchar(320),
    modifieddate timestamp,
    timeinepoch numeric,
    isarchived bpchar,
    isdeleted bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    updatedlocation varchar(200),
    lastsyncdate timestamp,
	CONSTRAINT inventoryplate_id PRIMARY KEY (id)
);","Inventory license plates (LPs) containing detailed item information, quantities, locations, and tracking data. Core table for inventory management and warehouse operations tracking individual units of inventory."
inventorycontrol.itempickfront,"CREATE TABLE inventorycontrol.itempickfront (
    id varchar(40) NOT NULL,
    slotlocationid varchar(40),
    warehouseid varchar(40),
    warehouse varchar(10),
    accountid varchar(40),
    account varchar(10),
    itemnumber varchar(40),
    businessunitid varchar(40),
    customerid varchar(40),
    itemid varchar(40),
    item varchar(10),
    businessunit varchar(20),
    itemdescription varchar(250),
    locationid varchar(40),
    location varchar(20),
    productgroup varchar(4),
    productgroupid varchar(40),
    ismixedpalletsallowed bpchar,
    inventoryclass varchar(4),
    inventorystatus varchar(4),
    vendor varchar(40),
    countryoforigin varchar(4),
    lotnumber varchar(80),
    masterpackthreshold numeric,
    maxpickquantity numeric,
    maxshelflife numeric,
    minshelflife numeric,
    splitlpatputaway numeric,
    allowmixedpallets bpchar,
    splitlpatreplenishment bpchar,
    rategroupcode varchar(4),
    pickuomcode varchar(4),
    pickfrontstatuscode varchar(4),
    rategroup varchar(20),
    pickfrontstatus varchar(40),
    lastpickeddate timestamp,
    inventoryclassdescription varchar(80),
    inventorystatusdescription varchar(80),
    replenishuom varchar(4),
    slotid varchar(40),
    regulated int2,
    pickuom varchar(500),
    replenishminuom varchar(4),
    replenishminqty numeric,
    replenishmaxuom varchar(4),
    replenishmaxqty numeric,
    replenishtopoffuom varchar(4),
    replenishtopoffqty numeric,
    createdby varchar(320),
    createddate timestamp,
    modifiedby varchar(320),
    modifieddate timestamp,
    timeinepoch numeric,
    isdeleted bpchar,
    isarchived bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    updatedlocation varchar(200),
    productgroupcode varchar(4),
	CONSTRAINT itempickfront_id PRIMARY KEY (id)
);","Manages items configured for pick front locations, detailing picking strategies, UOMs, and inventory characteristics specific to rapid picking zones. Optimizes the efficiency of picking operations."
inventorycontrol.itempickfrontpickuoms,"CREATE TABLE inventorycontrol.itempickfrontpickuoms (
    id varchar(40) NOT NULL,
    slotitemid varchar(40),
    uom varchar(4),
    accountid varchar(50),
    businessunitid varchar(50),
    warehouseid varchar(50),
    customerid varchar(50),
    createdby varchar(320),
    createddate timestamp,
    modifiedby varchar(320),
    modifieddate timestamp,
    timeinepoch float8,
    isdeleted bpchar,
    isarchived bpchar,
    isdefault bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    updatedlocation varchar(200),
	CONSTRAINT itempickfrontpickuoms_id PRIMARY KEY (id)
);","Stores the different Units of Measure (UOMs) configured for items in pick front locations, indicating which UOMs are used for picking activities. Supports flexible picking strategies."
inventorycontrol.movementtask,"CREATE TABLE inventorycontrol.movementtask (
    id varchar(40) NOT NULL,
    identitycode varchar(20),
    warehouseid varchar(40),
    towarehouseid varchar(40),
    businessunitid varchar(40),
    accountid varchar(40),
    customerid varchar(40),
    fromlocationid varchar(40),
    fromlocation varchar(20),
    tolocationid varchar(40),
    tolocation varchar(20),
    droplocationid varchar(40),
    droplocation varchar(20),
    priority numeric,
    previouspriority numeric,
    plateid varchar(40),
    itemid varchar(40),
    item varchar(20),
    quantity numeric,
    unitofmeasure varchar(4),
    activeuser varchar(320),
    assigneduser varchar(320),
    status numeric,
    previousstatus numeric,
    equipmentprofileid varchar(40),
    locationsequence numeric,
    cartonsequence numeric,
    confirmid bpchar,
    showscanposition bpchar,
    ismasterpackenabled bpchar,
    tasktype varchar(40),
    platenumber varchar(50),
    cyclecountid varchar(40),
    physicalinventoryid varchar(40),
    cyclecountseq numeric,
    pirequestid varchar(40),
    pirequestcode varchar(20),
    pirequestdate timestamp,
    warehouse varchar(10),
    businessunit varchar(10),
    account varchar(10),
    statusdesc varchar(10),
    prioritydesc varchar(80),
    previousprioritydesc varchar(80),
    tasksequence numeric,
    locationid varchar(40),
    location varchar(10),
    locationtype varchar(40),
    zoneid varchar(40),
    zonecode varchar(40),
    tozoneid varchar(40),
    tozonecode varchar(40),
    requestid varchar(40),
    lastcountedby varchar(320),
    countstatus varchar(10),
    countstatusdesc varchar(80),
    countby varchar(40),
    countdate timestamp,
    reason varchar(40),
    origin varchar(10),
    weightuom varchar(4),
    averageweightuom varchar(4),
    recountsequence numeric,
    createdby varchar(320),
    createddate timestamp,
    modifiedby varchar(320),
    modifieddate timestamp,
    timeinepoch numeric,
    taskstarted timestamp,
    taskended timestamp,
    taskstartuser varchar(320),
    movetype varchar(4),
    tolocationsequence numeric,
    isarchived bpchar,
    isdeleted bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    updatedlocation varchar(200),
    traceid varchar(40),
    spanid varchar(40),
	CONSTRAINT movementtask_id PRIMARY KEY (id)
);","Warehouse movement tasks for relocating inventory between locations. Manages putaway, replenishment, cycle counting, and other inventory movement operations with user assignments and status tracking."
inventorycontrol.physicalinventorycountactivity,"CREATE TABLE inventorycontrol.physicalinventorycountactivity (
    id varchar(40) NOT NULL,
    taskid varchar(40),
    pirequestid varchar(40),
    pirequestcode varchar(20),
    businessunitid varchar(40),
    accountid varchar(40),
    customerid varchar(40),
    warehouseid varchar(40),
    platenumber varchar(40),
    itemid varchar(40),
    item varchar(10),
    itemdescription varchar(80),
    quantity numeric,
    lotnumber varchar(80),
    location varchar(40),
    vendor varchar(40),
    inventoryclass varchar(4),
    caseqrcode varchar(80),
    serialnumber varchar(80),
    countedby varchar(320),
    countdate timestamp,
    createdby varchar(320),
    createddate timestamp,
    modifiedby varchar(320),
    modifieddate timestamp,
    timeinepoch numeric,
    isarchived bpchar,
    isdeleted bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    updatedlocation varchar(200),
	CONSTRAINT physicalinventorycountactivity_id PRIMARY KEY (id)
);","Records the actual physical inventory counts performed, including counted quantities, item details, and the user who performed the count. Used to reconcile physical inventory with system records."
inventorycontrol.physicalinventorycountstatus,"CREATE TABLE inventorycontrol.physicalinventorycountstatus (
    id varchar(40) NOT NULL,
    physicalinventoryid varchar(40),
    detailid varchar(40),
    countid varchar(40),
    taskid varchar(40),
    accountid varchar(50),
    account varchar(10),
    businessunitid varchar(50),
    businessunit varchar(10),
    warehouseid varchar(50),
    warehouse varchar(10),
    customerid varchar(50),
    itemmatch bpchar,
    quantitymatch bpchar,
    locationmatch bpchar,
    lotnumbermatch bpchar,
    inventoryplatematch bpchar,
    countmatch bpchar,
    countitem varchar(40),
    countitemcode varchar(40),
    countitemdesc varchar(80),
    countquantity numeric,
    countlotnumber varchar(80),
    countlocation varchar(40),
    countvendor varchar(40),
    countby varchar(40),
    location varchar(40),
    countserialnumber varchar(40),
    countplatenumber varchar(40),
    pirequestid varchar(40),
    pirequestcode varchar(20),
    sequencenumber numeric,
    matchedtaskid varchar(40),
    caseqrcodematch bpchar,
    serialnumbermatch bpchar,
    businessunitidmatch bpchar,
    inventoryclassmatch bpchar,
    vendormatch bpchar,
    reviewed bpchar,
    reviewedby varchar(320),
    revieweddate timestamp,
    createdby varchar(320),
    createddate timestamp,
    modifiedby varchar(320),
    modifieddate timestamp,
    timeinepoch numeric,
    isdeleted bpchar,
    isarchived bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    updatedlocation varchar(200),
    countlocationid varchar(40),
    countlocationcode varchar(10),
	CONSTRAINT physicalinventorycountstatus_id PRIMARY KEY (id)
);","Tracks the matching status between system inventory records and physical inventory counts, highlighting discrepancies and review statuses. Crucial for reconciliation and audit of physical inventory."
inventorycontrol.physicalinventorydetail,"CREATE TABLE inventorycontrol.physicalinventorydetail (
    id varchar(40) NOT NULL,
    physicalinventoryid varchar(40),
    taskid varchar(40),
    tasknumber varchar(40),
    businessunitid varchar(40),
    businessunit varchar(10),
    accountid varchar(40),
    account varchar(10),
    customerid varchar(40),
    warehouse varchar(10),
    warehouseid varchar(40),
    pirequestid varchar(40),
    pirequestcode varchar(20),
    status varchar(4),
    statusdesc varchar(10),
    location varchar(40),
    locationcode varchar(10),
    item varchar(40),
    itemnumber varchar(40),
    itemdesc varchar(80),
    itemdescription varchar(250),
    quantity numeric,
    lotnumber varchar(80),
    vendor varchar(40),
    inventoryclass varchar(4),
    unitofmeasure varchar(4),
    plateid varchar(40),
    platenumber varchar(50),
    matchfound bpchar,
    newinventory bpchar,
    sequencenumber numeric,
    serialnumber varchar(80),
    revivedinventory bpchar,
    createdby varchar(320),
    createddate timestamp,
    modifiedby varchar(320),
    modifieddate timestamp,
    timeinepoch numeric,
    isdeleted bpchar,
    isarchived bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    updatedlocation varchar(200),
	CONSTRAINT physicalinventorydetail_id PRIMARY KEY (id)
);","Provides detailed breakdowns of physical inventory requests, including specific items, locations, quantities, and their reconciliation status. Supports comprehensive inventory auditing and discrepancy resolution."
inventorycontrol.physicalinventorysubtask,"CREATE TABLE inventorycontrol.physicalinventorysubtask (
    id varchar(40) NOT NULL,
    physicalinventorytaskid varchar(40),
    physicalinventoryid varchar(40),
    businessunitid varchar(40),
    accountid varchar(40),
    customerid varchar(40),
    warehouseid varchar(50),
    tasknumber varchar(80),
    businessunit varchar(10),
    account varchar(10),
    itemid varchar(40),
    itemcode varchar(10),
    itemdesc varchar(80),
    quantitymin numeric,
    quantitymax numeric,
    platenumber varchar(50),
    inventoryclass varchar(4),
    inventorystatus varchar(4),
    inventoryclassdescription varchar(80),
    inventorystatusdescription varchar(80),
    lotnumber varchar(80),
    vendor varchar(40),
    createdby varchar(320),
    createddate timestamp,
    modifiedby varchar(320),
    modifieddate timestamp,
    timeinepoch numeric,
    taskstarted timestamp,
    taskended timestamp,
    taskstartuser varchar(320),
    isdeleted bpchar,
    isarchived bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    updatedlocation varchar(200),
	CONSTRAINT physicalinventorysubtask_id PRIMARY KEY (id)
);","Breaks down physical inventory tasks into smaller subtasks, defining specific items, plate numbers, and quantity ranges for counting. Enables organized and detailed physical inventory execution."
inventorycontrol.physicalinventorytask,"CREATE TABLE inventorycontrol.physicalinventorytask (
    id varchar(40) NOT NULL,
    identitycode varchar(20),
    warehouse varchar(10),
    tasktype varchar(40),
    pirequestid varchar(40),
    pirequestcode varchar(20),
    pirequestdate timestamp,
    physicalinventoryid varchar(40),
    locationid varchar(40),
    locationtype varchar(4),
    zoneid varchar(40),
    zonecode varchar(40),
    accountid varchar(40),
    businessunitid varchar(40),
    customerid varchar(40),
    priority numeric,
    prioritydesc varchar(80),
    previouspriority numeric,
    previousprioritydesc varchar(80),
    assigneduser varchar(320),
    activeuser varchar(320),
    status numeric,
    statusdesc varchar(10),
    previousstatus numeric,
    nexttaskid varchar(40),
    recountsequence numeric,
    equipmentprofileid varchar(40),
    locationsequence numeric,
    plateid varchar(40),
    platenumber varchar(50),
    cyclecountid varchar(40),
    cyclecountseq numeric,
    businessunit varchar(10),
    account varchar(10),
    tasksequence numeric,
    location varchar(10),
    tolocationid varchar(40),
    tolocation varchar(10),
    tozoneid varchar(40),
    tozonecode varchar(40),
    requestid varchar(40),
    lastcountedby varchar(320),
    countstatus varchar(10),
    countstatusdesc varchar(80),
    countby varchar(40),
    countdate timestamp,
    reason varchar(40),
    showscanposition bpchar,
    ismasterpackenabled bpchar,
    origin varchar(10),
    weightuom varchar(4),
    averageweightuom varchar(4),
    createdby varchar(320),
    createddate timestamp,
    modifiedby varchar(320),
    modifieddate timestamp,
    timeinepoch numeric,
    taskstarted timestamp,
    taskended timestamp,
    taskstartuser varchar(320),
    traceid varchar(40),
    spanid varchar(40),
    isdeleted bpchar,
    isarchived bpchar,
    deleted bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    updatedlocation varchar(200),
    warehouseid varchar(50),
	CONSTRAINT physicalinventorytask_id PRIMARY KEY (id)
);","Defines and tracks individual physical inventory tasks, including assigned users, locations, and status. Manages the overall workflow for comprehensive inventory audits."
inventorycontrol.pirequest,"CREATE TABLE inventorycontrol.pirequest (
    id varchar(40) NOT NULL,
    warehouseid varchar(40),
    warehouse varchar(10),
    businessunitid varchar(40),
    businessunit varchar(10),
    accountid varchar(40),
    account varchar(10),
    customerid varchar(40),
    pirequestcode varchar(20),
    status varchar(4),
    statusdesc varchar(80),
    requestedby varchar(320),
    requestdate timestamp,
    createdby varchar(320),
    createddate timestamp,
    modifiedby varchar(320),
    modifieddate timestamp,
    timeinepoch numeric,
    isdeleted bpchar,
    isarchived bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    updatedlocation varchar(200),
	CONSTRAINT pirequest_id PRIMARY KEY (id)
);","Represents a request for a physical inventory count, detailing the scope, status, and associated warehouse/business units. Initiates and manages the physical inventory process."
item.biologicsubstancesregulations,"CREATE TABLE item.biologicsubstancesregulations (
    id varchar(40) NOT NULL,
    createddate timestamp,
    createdby varchar(250),
    modifieddate timestamp,
    modifiedby varchar(250),
    timeinepoch numeric,
    isenabled bpchar,
    isfdaregistered bpchar,
    biologictype varchar(20),
    businessunitid varchar(50),
    accountid varchar(50),
    itembusinessunitid varchar(40),
    requireshumiditycontrol bpchar,
    requiresfacilityfence bpchar,
    requiresyardfence bpchar,
    requiresaerosolcage bpchar,
    requiresrestrictedcage bpchar,
    requirescoldchainshipping bpchar,
    storagetemperature varchar(40),
    isdefault bpchar,
    isdeleted bpchar,
    isarchived bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    updatedlocation varchar(200),
	CONSTRAINT biologicsubstancesregulations_id PRIMARY KEY (id, id)
);","Manages regulatory requirements for biological substances, including FDA registration, storage conditions, and specific handling needs like humidity control or restricted access. Ensures compliance for sensitive biological items."
item.controlledsubstancesregulations,"CREATE TABLE item.controlledsubstancesregulations (
    id varchar(40) NOT NULL,
    createddate timestamp,
    createdby varchar(250),
    modifieddate timestamp,
    modifiedby varchar(250),
    timeinepoch numeric,
    usinessunitid varchar(50),
    ccountid varchar(50),
    itembusinessunitid varchar(40),
    requireshumiditycontrol bpchar,
    requiresfacilityfence bpchar,
    requiresyardfence bpchar,
    requiresaerosolcage bpchar,
    requiresrestrictedcage bpchar,
    requirescoldchainshipping bpchar,
    storagetemperature varchar(40),
    isenabled bpchar,
    controlledsubstancetype varchar(40),
    requiresdeavalidation bpchar,
    isdefault bpchar,
    isdeleted bpchar,
    isarchived bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    updatedlocation varchar(200),
	CONSTRAINT controlledsubstancesregulations_id PRIMARY KEY (id)
);","Defines regulatory requirements for controlled substances, including storage conditions, security measures like facility fences, and DEA validation needs. Critical for compliance in handling regulated items."
item.exchangeitem,"CREATE TABLE item.exchangeitem (
    id varchar(40) NOT NULL,
    accountid varchar(40),
    businessunitid varchar(40),
    warehouseid varchar(50),
    customerid varchar(50),
    createddate timestamp,
    createdby varchar(250),
    modifieddate timestamp,
    modifiedby varchar(250),
    timeinepoch numeric,
    itembusinessunitid varchar(40),
    itemreturnid varchar(40),
    productgroupbusinessunitid varchar(40),
    isdefault bpchar,
    isdeleted bpchar,
    isarchived bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    updatedlocation varchar(200),
    itemnumber varchar(20),
    itemdescription varchar(40),
    productgroupcode varchar(20),
    productgroupdescription varchar(40),
	CONSTRAINT exchangeitem_id PRIMARY KEY (id)
);","Lists items that are eligible for exchange, linking to their return policies and product group details. Facilitates customer service processes related to item exchanges."
item.itemactivelot,"CREATE TABLE item.itemactivelot (
    id varchar(40) NOT NULL,
    createddate timestamp,
    createdby varchar(250),
    modifieddate timestamp,
    modifiedby varchar(250),
    timeinepoch numeric,
    itembusinessunitid varchar(40),
    accountid varchar(40),
    businessunitid varchar(40),
    warehouseid varchar(50),
    customerid varchar(50),
    lotnumber varchar(40),
    inventorystatus varchar(40),
    expirationdate varchar(40),
    isdefault bpchar,
    isdeleted bpchar,
    isarchived bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    updatedlocation varchar(200),
    inventorystatusdescription varchar(40),
	CONSTRAINT itemactivelot_id PRIMARY KEY (id)
);","Tracks active lot numbers for items, including their inventory status and expiration dates. Essential for managing inventory with lot-specific attributes and for enforcing FIFO/FEFO strategies."
item.itemactivityinfo,"CREATE TABLE item.itemactivityinfo (
    id varchar(40) NOT NULL,
    createddate timestamp,
    createdby varchar(250),
    modifieddate timestamp,
    modifiedby varchar(250),
    timeinepoch numeric,
    itembusinessunitid varchar(40),
    accountid varchar(40),
    businessunitid varchar(40),
    warehouseid varchar(50),
    customerid varchar(50),
    lastcyclecounted timestamp,
    activedate timestamp,
    inactivedate timestamp,
    status varchar(40),
    averagemovement float8,
    isdefault bpchar,
    isdeleted bpchar,
    isarchived bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    updatedlocation varchar(200),
	CONSTRAINT itemactivityinfo_id PRIMARY KEY (id)
);","Stores activity-related information for items, such as last cycle count date, active/inactive dates, current status, and average movement. Provides insights into item lifecycle and velocity."
item.itemalias,"CREATE TABLE item.itemalias (
    id varchar(40) NOT NULL,
    createddate timestamp,
    createdby varchar(250),
    modifieddate timestamp,
    modifiedby varchar(250),
    timeinepoch numeric,
    itembusinessunitid varchar(40),
    alias varchar(40),
    barcodetype varchar(4),
    unitofmeasure varchar(4),
    isdefault bpchar,
    isdeleted bpchar,
    isarchived bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    updatedlocation varchar(200),
    accountid varchar(40),
    businessunitid varchar(50),
    warehouseid varchar(50),
    customerid varchar(50),
	CONSTRAINT itemalias_id PRIMARY KEY (id)
);","Defines alternative names or barcodes for items, along with their associated unit of measure. Facilitates scanning and identification of items using various aliases."
item.itembusinessunit,"CREATE TABLE item.itembusinessunit (
    id varchar(40) NOT NULL,
    createddate timestamp,
    createdby varchar(250),
    modifieddate timestamp,
    modifiedby varchar(250),
    timeinepoch numeric,
    businessunitid varchar(40),
    itemnumber varchar(40),
    description varchar(250),
    productgroupbusinessunitid varchar(40),
    isregulated bpchar,
    isdeleted bpchar,
    isarchived bpchar,
    isdefault bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    updatedlocation varchar(200),
    rategroup varchar(4),
    rategroupdescription varchar(40),
    productgroupdescription varchar(40),
    retailprice float4,
    color varchar(4),
    colordescription varchar(40),
    pattern varchar(4),
    patterndescription varchar(40),
    sizedescription varchar(40),
    gender varchar(4),
    genderdescription varchar(40),
    season varchar(4),
    seasondescription varchar(40),
    durability varchar(4),
    unitsperpickactive float4,
    unitsperpickreserve float4,
    productgroupcode varchar(40),
    itemsize varchar(40),
    listprice float8,
    shelflife numeric,
    title varchar(40),
    productname varchar(40),
    upc varchar(40),
    ean varchar(40),
    brandname varchar(40),
    gtin varchar(40),
    mpn varchar(40),
    nmfc varchar(40),
    nmfcarticle varchar(40),
    tmsproductcode varchar(40),
    extractid varchar(40),
    extlines varchar(40),
    condition varchar(40),
    producttype varchar(40),
	CONSTRAINT itembusinessunit_id PRIMARY KEY (id, id)
);","Defines item-specific attributes within a particular business unit, including descriptions, product group, pricing, and various physical characteristics. Central to managing item master data for different business contexts."
item.itemclassificationcodes,"CREATE TABLE item.itemclassificationcodes (
    id varchar(40) NOT NULL,
    createddate timestamp,
    createdby varchar(250),
    modifieddate timestamp,
    modifiedby varchar(250),
    timeinepoch numeric,
    itembusinessunitid varchar(40),
    classificationtype varchar(5),
    classificationcode varchar(4),
    isdefault bpchar,
    isdeleted bpchar,
    isarchived bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    updatedlocation varchar(200),
    accountid varchar(50),
    businessunitid varchar(50),
    customerid varchar(50),
    warehouseid varchar(50),
	CONSTRAINT itemclassificationcodes_id PRIMARY KEY (id)
);","Assigns classification codes to items based on their type, used for categorization and reporting. Supports various classification schemas for different business needs."
item.itemcountryoforigin,"CREATE TABLE item.itemcountryoforigin (
    id varchar(40) NOT NULL,
    createddate timestamp,
    createdby varchar(250),
    modifieddate timestamp,
    modifiedby varchar(250),
    timeinepoch numeric,
    itembusinessunitid varchar(40),
    accountid varchar(40),
    businessunitid varchar(40),
    warehouseid varchar(40),
    customerid varchar(40),
    country varchar(40),
    isdefault bpchar,
    isdeleted bpchar,
    isarchived bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    updatedlocation varchar(200),
	CONSTRAINT itemcountryoforigin_id PRIMARY KEY (id)
);","Records the country of origin for items, crucial for compliance, trade regulations, and international shipping. Essential for managing global supply chains."
item.itemcriticalinventorylevel,"CREATE TABLE item.itemcriticalinventorylevel (
    id varchar(40) NOT NULL,
    createddate timestamp,
    createdby varchar(250),
    modifieddate timestamp,
    modifiedby varchar(250),
    timeinepoch numeric,
    itembusinessunitid varchar(40),
    inventorylevel float8,
    amount float8,
    isdefault bpchar,
    isdeleted bpchar,
    isarchived bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    updatedlocation varchar(200),
    accountid varchar(50),
    businessunitid varchar(50),
    warehouseid varchar(50),
    customerid varchar(50),
	CONSTRAINT itemcriticalinventorylevel_id PRIMARY KEY (id)
);","Sets critical inventory levels for items, triggering alerts or reorder processes when stock falls below a specified threshold. Supports proactive inventory management and prevents stockouts."
item.itemcyclecountdatacapture,"CREATE TABLE item.itemcyclecountdatacapture (
    id varchar(40) NOT NULL,
    createddate timestamp,
    createdby varchar(250),
    modifieddate timestamp,
    modifiedby varchar(250),
    timeinepoch numeric,
    itembusinessunitid varchar(40),
    accountid varchar(40),
    businessunitid varchar(50),
    warehouseid varchar(50),
    customerid varchar(50),
    captureserialnumber bpchar,
    capturecountryoforigin bpchar,
    capturemanufacturingdate bpchar,
    captureexpirationdate bpchar,
    captureowner bpchar,
    capturevendor bpchar,
    capturesupplier bpchar,
    capturecolor bpchar,
    capturesize bpchar,
    capturemanufacturingsiteid bpchar,
    captureimei bpchar,
    captureimei2 bpchar,
    captureisbn bpchar,
    captureupc bpchar,
    isdefault bpchar,
    isdeleted bpchar,
    isarchived bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    updatedlocation varchar(200),
    enablecyclecounttolerance bpchar,
    cyclecounttolerancevalue numeric,
    cyclecounttolerancemode varchar(20),
	CONSTRAINT itemcyclecountdatacapture_id PRIMARY KEY (id)
);","Configures data capture requirements during cycle counts for items, such as serial numbers, country of origin, and manufacturing/expiration dates. Ensures accurate and detailed inventory auditing."
item.itemhandlingunit,"CREATE TABLE item.itemhandlingunit (
    id varchar(40) NOT NULL,
    createddate timestamp,
    createdby varchar(250),
    modifieddate timestamp,
    modifiedby varchar(250),
    timeinepoch numeric,
    itembusinessunitid varchar(40),
    accountid varchar(40),
    unitofmeasure varchar(40),
    handlingattribute varchar(40),
    unitsperpick float8,
    isdefault bpchar,
    isdeleted bpchar,
    isarchived bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    updatedlocation varchar(200),
    customerid varchar(50),
    warehouseid varchar(50),
    businessunitid varchar(50),
    unitofmeasuredescription varchar(40),
	CONSTRAINT itemhandlingunit_id PRIMARY KEY (id)
);","Defines how items are handled within different units of measure, including attributes and units per pick. Optimizes warehouse operations by specifying handling characteristics for various packaging configurations."
item.itemimages,"CREATE TABLE item.itemimages (
    id varchar(40) NOT NULL,
    createddate timestamp,
    createdby varchar(250),
    modifieddate timestamp,
    modifiedby varchar(250),
    timeinepoch numeric,
    itembusinessunitid varchar(40),
    accountid varchar(50),
    businessunitid varchar(50),
    warehouseid varchar(50),
    customerid varchar(50),
    sequence int4,
    imageurl varchar(150),
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    updatedlocation varchar(200),
    isdeleted bpchar,
    isarchived bpchar,
    isdefault bpchar,
	CONSTRAINT itemimages_id PRIMARY KEY (id)
);","Stores image URLs for items, allowing for visual representation in inventory systems or e-commerce platforms. Enhances item identification and presentation."
item.iteminbound,"CREATE TABLE item.iteminbound (
    id varchar(40) NOT NULL,
    createddate timestamp,
    createdby varchar(250),
    modifieddate timestamp,
    modifiedby varchar(250),
    timeinepoch numeric,
    itembusinessunitid varchar(40),
    accountid varchar(40),
    allowdamagedproducts bpchar,
    enablemultiskugs1receiving bpchar,
    enablemulticountrygs1receiving bpchar,
    enableqrcode bpchar,
    enablelpdimcapture bpchar,
    enableserialregistryvalidation bpchar,
    captureinboundcatchweight bpchar,
    weighttolerance float8,
    defaultinboundinventorystatuscode varchar(40),
    defaultputawayinventorystatuscode varchar(40),
    putawayconfirmationmethodcode varchar(40),
    allowsinglequantitylicenseplates bpchar,
    maintainoldestmanufacturingdate bpchar,
    convert9dmfgdate bpchar,
    issinglepackmultipack bpchar,
    instructions varchar(100),
    overridekey varchar(40),
    overridevalue varchar(40),
    isdefault bpchar,
    isdeleted bpchar,
    isarchived bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    updatedlocation varchar(200),
    businessunitid varchar(40),
    warehouseid varchar(50),
    customerid varchar(50),
	CONSTRAINT iteminbound_id PRIMARY KEY (id)
);","Configures inbound processing rules for items, including options for handling damaged products, GS1 receiving, and capturing various data points like weight and dimensions. Optimizes receiving and putaway processes."
item.iteminboundasncapture,"CREATE TABLE item.iteminboundasncapture (
    id varchar(40) NOT NULL,
    accountid varchar(40),
    businessunitid varchar(40),
    warehouseid varchar(50),
    customerid varchar(50),
    createddate timestamp,
    createdby varchar(250),
    modifieddate timestamp,
    modifiedby varchar(250),
    timeinepoch numeric,
    iteminboundid varchar(40),
    isenabled bpchar,
    serialnumber bpchar,
    isdefault bpchar,
    isdeleted bpchar,
    isarchived bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    updatedlocation varchar(200),
    itembusinessunitid varchar(40),
	CONSTRAINT iteminboundasncapture_id PRIMARY KEY (id)
);","Defines specific data capture requirements for items during ASN (Advanced Shipping Notice) receiving. Ensures that necessary information, such as serial numbers, is captured upon inbound receipt."
item.iteminboundcapture,"CREATE TABLE item.iteminboundcapture (
    id varchar(40) NOT NULL,
    accountid varchar(40),
    businessunitid varchar(40),
    itembusinessunitid varchar(40),
    warehouseid varchar(50),
    customerid varchar(50),
    createddate timestamp,
    createdby varchar(250),
    modifieddate timestamp,
    modifiedby varchar(250),
    timeinepoch numeric,
    iteminboundid varchar(40),
    isenabled bpchar,
    capturelotnumber varchar(40),
    captureserialnumber bpchar,
    capturecountryoforigin bpchar,
    capturemanufacturingdate bpchar,
    captureexpirationdate bpchar,
    captureimei bpchar,
    isdefault bpchar,
    isdeleted bpchar,
    isarchived bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    updatedlocation varchar(200),
    lotnumbercapturetypedescription varchar(40),
	CONSTRAINT iteminboundcapture_id PRIMARY KEY (id)
);","Specifies data capture requirements for items during inbound processing, such as lot numbers, serial numbers, and expiration dates. Ensures comprehensive data collection upon item receipt."
item.iteminboundconfigcode,"CREATE TABLE item.iteminboundconfigcode (
    id varchar(40) NOT NULL,
    createddate timestamp,
    createdby varchar(250),
    modifieddate timestamp,
    modifiedby varchar(250),
    timeinepoch numeric,
    iteminboundid varchar(40),
    configurationcode varchar(40),
    isserialnumberrealtimevalidation bpchar,
    isdefault bpchar,
    isdeleted bpchar,
    isarchived bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    updatedlocation varchar(200),
    accountid varchar(50),
    businessunitid varchar(50),
    warehouseid varchar(50),
    customerid varchar(50),
    itembusinessunitid varchar(40),
	CONSTRAINT iteminboundconfigcode_id PRIMARY KEY (id)
);","Manages configuration codes for item inbound processes, including settings for real-time serial number validation. Allows for fine-tuning of inbound workflows based on item characteristics."
item.iteminboundentryfieldparsing,"CREATE TABLE item.iteminboundentryfieldparsing (
    id varchar(40) NOT NULL,
    createddate timestamp,
    createdby varchar(250),
    modifieddate timestamp,
    modifiedby varchar(250),
    timeinepoch numeric,
    iteminboundid varchar(40),
    itembusinessunitid varchar(40),
    businessunitid varchar(50),
    accountid varchar(50),
    warehouseid varchar(50),
    customerid varchar(50),
    parseruleaction bpchar,
    entryfield varchar(40),
    entryfieldparsingrule varchar(40),
    isdefault bpchar,
    isdeleted bpchar,
    isarchived bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    updatedlocation varchar(200),
	CONSTRAINT iteminboundentryfieldparsing_id PRIMARY KEY (id)
);","Defines rules for parsing data from inbound entry fields, enabling automated data extraction and validation during receiving. Streamlines data entry and improves accuracy for incoming items."
item.itemkitmaintenance,"CREATE TABLE item.itemkitmaintenance (
    id varchar(40) NOT NULL,
    accountid varchar(40),
    businessunitid varchar(40),
    warehouseid varchar(50),
    customerid varchar(50),
    createddate timestamp,
    createdby varchar(250),
    modifieddate timestamp,
    modifiedby varchar(250),
    timeinepoch numeric,
    itembusinessunitid varchar(50),
    itemaccountid varchar(50),
    kitattributecode varchar(40),
    isdefault bpchar,
    isdeleted bpchar,
    isarchived bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    updatedlocation varchar(200),
	CONSTRAINT itemkitmaintenance_id PRIMARY KEY (id)
);","Manages the configuration of item kits, defining attributes and components that make up a kitted item. Essential for assembling and tracking product bundles."
item.itemmaintenance,"CREATE TABLE item.itemmaintenance (
    id varchar(40) NOT NULL,
    createddate timestamp,
    createdby varchar(250),
    modifieddate timestamp,
    modifiedby varchar(250),
    timeinepoch numeric,
    itembusinessunitid varchar(40),
    accountid varchar(40),
    businessunitid varchar(50),
    warehouseid varchar(50),
    customerid varchar(50),
    isvendortracked bpchar,
    reorderpoint float8,
    isshortdate bpchar,
    shortdateoffset int4,
    daysbeforeexpiration float8,
    isnoninventory bpchar,
    isnonasn bpchar,
    istopshelfeligible bpchar,
    rategroup varchar(40),
    expirationaction varchar(40),
    isdeleted bpchar,
    isarchived bpchar,
    isdefault bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    updatedlocation varchar(200),
	CONSTRAINT itemmaintenance_id PRIMARY KEY (id, id)
);","Contains general maintenance settings for items, including vendor tracking, reorder points, short date flags, and expiration actions. Defines how items are managed throughout their lifecycle."
item.itemoutbound,"CREATE TABLE item.itemoutbound (
    id varchar(40) NOT NULL,
    createddate timestamp,
    createdby varchar(250),
    modifieddate timestamp,
    modifiedby varchar(250),
    timeinepoch numeric,
    itembusinessunitid varchar(40),
    accountid varchar(40),
    backorderpolicy varchar(40),
    fifowindow float8,
    splitunitofmeasure varchar(40),
    transportationunitofmeasure varchar(40),
    sipcartonunitofmeasure varchar(40),
    allowsubstitution bpchar,
    forcerfaudit bpchar,
    cartonshipclass varchar(40),
    jiffypack bpchar,
    shipalone bpchar,
    weightcheck bpchar,
    platesubreason bpchar,
    demandxdocking bpchar,
    singlequantitylp bpchar,
    instructions varchar(250),
    bolcomments varchar(250),
    countryoforiginpickingcapture varchar(40),
    serialnumberpickingcapture varchar(40),
    imeicapture varchar(40),
    includeinventorystatus varchar(1),
    includeinventoryclass varchar(1),
    enablemultiskugs1picking bpchar,
    captureoutboundcatchweight bpchar,
    weighttolerance float8,
    netcatchweight float8,
    grosscatchweight float8,
    reportcountryoforiginonexports bpchar,
    isdefault bpchar,
    isdeleted bpchar,
    isarchived bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    updatedlocation varchar(200),
    businessunitid varchar(40),
    warehouseid varchar(50),
    customerid varchar(50),
    itemnumber varchar(40) NOT NULL,
    description varchar(250),
    backorderpolicydescription varchar(250),
    splituomdescription varchar(250),
    cartonshipclassdescription varchar(250),
    transportationuomdescription varchar(250),
    sipcartonuomdescription varchar(250),
    isoutboundcaptureenabled bpchar,
    oblotnumbercapturetype varchar(4),
    pickuom varchar(4),
    serialnumberuom varchar(4),
    capturelotnumber varchar(40),
    captureserialnumber bpchar,
    capturecountryoforigin bpchar,
    capturemanufacturingdate bpchar,
    captureexpirationdate bpchar,
    captureimei bpchar,
    obgs1picking bpchar,
	CONSTRAINT itemoutbound_id PRIMARY KEY (id)
);","Defines outbound processing rules for items, including backorder policies, picking strategies, and data capture requirements during shipping. Optimizes order fulfillment and shipping processes."
item.itemoutboundpickingcapture,"CREATE TABLE item.itemoutboundpickingcapture (
    id varchar(40) NOT NULL,
    accountid varchar(40),
    businessunitid varchar(40),
    warehouseid varchar(50),
    customerid varchar(50),
    createddate timestamp,
    createdby varchar(250),
    modifieddate timestamp,
    modifiedby varchar(250),
    timeinepoch numeric,
    itemoutboundid varchar(40),
    capturetype varchar(40),
    capturefield varchar(40),
    capturefieldvalue varchar(40),
    isdefault bpchar,
    isdeleted bpchar,
    isarchived bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    updatedlocation varchar(200),
    itembusinessunitid varchar(40),
	CONSTRAINT itemoutboundpickingcapture_id PRIMARY KEY (id)
);","Specifies which data fields must be captured during outbound picking for items, such as lot numbers or serial numbers. Ensures compliance and accurate inventory tracking during shipment preparation."
item.itempicktotypes,"CREATE TABLE item.itempicktotypes (
    id varchar(40) NOT NULL,
    createddate timestamp,
    createdby varchar(250),
    modifieddate timestamp,
    modifiedby varchar(250),
    timeinepoch numeric,
    itembusinessunitid varchar(40),
    accountid varchar(50),
    businessunitid varchar(50),
    warehouseid varchar(50),
    customerid varchar(50),
    unitofmeasure varchar(4),
    shiptype varchar(4),
    picktotype varchar(4),
    isdefault bpchar,
    isdeleted bpchar,
    isarchived bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    updatedlocation varchar(200),
	CONSTRAINT itempicktotypes_id PRIMARY KEY (id)
);","Defines allowed pick-to types for items based on unit of measure and ship type. Guides picking operations to ensure items are directed to the correct packing or staging areas."
item.itempricinglabel,"CREATE TABLE item.itempricinglabel (
    id varchar(40) NOT NULL,
    createddate timestamp,
    createdby varchar(250),
    modifieddate timestamp,
    modifiedby varchar(250),
    timeinepoch numeric,
    itembusinessunitid varchar(40),
    country varchar(40),
    pricelabel varchar(40),
    isdefault bpchar,
    isdeleted bpchar,
    isarchived bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    updatedlocation varchar(200),
    accountid varchar(50),
    businessunitid varchar(50),
    customerid varchar(50),
    warehouseid varchar(50),
	CONSTRAINT itempricinglabel_id PRIMARY KEY (id)
);","Stores pricing labels for items, potentially specific to certain countries or regions. Supports varied pricing strategies and regional sales compliance."
item.itemreturn,"CREATE TABLE item.itemreturn (
    id varchar(40) NOT NULL,
    createddate timestamp,
    createdby varchar(250),
    modifieddate timestamp,
    modifiedby varchar(250),
    timeinepoch numeric,
    itembusinessunitid varchar(40),
    accountid varchar(40),
    businessunitid varchar(50),
    warehouseid varchar(50),
    customerid varchar(50),
    allowreturns bpchar,
    disposition bpchar,
    returninstructions varchar(200),
    allowexchange bpchar,
    restockingfee numeric,
    contactname varchar(150),
    address1 varchar(200),
    address2 varchar(200),
    address3 varchar(200),
    city varchar(150),
    state varchar(150),
    zipcode varchar(15),
    country varchar(2),
    phone varchar(20),
    email varchar(200),
    isdefault bpchar,
    isdeleted bpchar,
    isarchived bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    updatedlocation varchar(200),
	CONSTRAINT itemreturn_id PRIMARY KEY (id)
);","Defines return policies for items, including whether returns or exchanges are allowed, disposition instructions, and restocking fees. Manages the process of handling returned merchandise."
item.itemsite,"CREATE TABLE item.itemsite (
    id varchar(40) NOT NULL,
    createddate timestamp,
    createdby varchar(250),
    modifieddate timestamp,
    modifiedby varchar(250),
    timeinepoch numeric,
    itembusinessunitid varchar(40),
    accountid varchar(40),
    siteid varchar(40),
    country varchar(40),
    version varchar(40),
    approved bpchar,
    isdefault bpchar,
    isdeleted bpchar,
    isarchived bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    updatedlocation varchar(200),
    businessunitid varchar(50),
    customerid varchar(50),
    warehouseid varchar(50),
	CONSTRAINT itemsite_id PRIMARY KEY (id)
);","Associates items with specific sites, indicating where an item can be handled or stored. Facilitates multi-site inventory management and localized item settings."
item.itemstackheight,"CREATE TABLE item.itemstackheight (
    id varchar(40) NOT NULL,
    createddate timestamp,
    createdby varchar(250),
    modifieddate timestamp,
    modifiedby varchar(250),
    timeinepoch numeric,
    itembusinessunitid varchar(40),
    stackheightunitofmeasure varchar(40),
    stackheightquantity float8,
    isdefault bpchar,
    isdeleted bpchar,
    isarchived bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    updatedlocation varchar(200),
    accountid varchar(50),
    businessunitid varchar(50),
    warehouseid varchar(50),
    customerid varchar(50),
    unitofmeasuredescription varchar(40),
	CONSTRAINT itemstackheight_id PRIMARY KEY (id)
);","Specifies the allowed stack height for items, including the unit of measure and quantity that can be stacked. Important for optimizing storage space and ensuring safety in the warehouse."
item.itemsubstitute,"CREATE TABLE item.itemsubstitute (
    id varchar(40) NOT NULL,
    createddate timestamp,
    createdby varchar(250),
    modifieddate timestamp,
    modifiedby varchar(250),
    timeinepoch numeric,
    itembusinessunitid varchar(40),
    sequence float8,
    substituteitemid varchar(40),
    isdefault bpchar,
    isdeleted bpchar,
    isarchived bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    updatedlocation varchar(200),
    accountid varchar(50),
    businessunitid varchar(50),
    customerid varchar(50),
    warehouseid varchar(50),
    itemnumber varchar(40),
    description varchar(250),
	CONSTRAINT itemsubstitute_id PRIMARY KEY (id)
);","Manages approved substitute items, allowing for flexibility in order fulfillment when primary items are unavailable. Helps maintain customer satisfaction and optimize inventory utilization."
item.itemunitofmeasure,"CREATE TABLE item.itemunitofmeasure (
    id varchar(40) NOT NULL,
    createddate timestamp,
    createdby varchar(250),
    modifieddate timestamp,
    modifiedby varchar(250),
    timeinepoch numeric,
    itembusinessunitid varchar(40),
    accountid varchar(40),
    businessunitid varchar(40),
    warehouseid varchar(50),
    customerid varchar(50),
    baseunitofmeasure varchar(4),
    weight float8,
    weightunitofmeasure varchar(4),
    unitvolume float8,
    averageweight float8,
    weighttolerance float8,
    velocity varchar(4),
    height float8,
    length float8,
    width float8,
    isdefault bpchar,
    isdeleted bpchar,
    isarchived bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    updatedlocation varchar(200),
    itemnumber varchar(40),
    description varchar(250),
    isregulated bpchar,
    baseuomdescription varchar(40),
    velocitydescription varchar(40),
    weightexpected float8,
    tareweight float8,
    cartontype varchar(40),
    picktotype varchar(40),
    casesperlayer int4,
    layersperpallet int4,
    palletstackheight float8,
	CONSTRAINT itemunitofmeasure_id PRIMARY KEY (id)
);","Defines various units of measure for items, including their physical dimensions, weight, and volume. Essential for accurate inventory tracking, storage, and shipping calculations."
item.itemunitofmeasureconversion,"CREATE TABLE item.itemunitofmeasureconversion (
    id varchar(40) NOT NULL,
    createddate timestamp,
    createdby varchar(250),
    modifieddate timestamp,
    modifiedby varchar(250),
    timeinepoch numeric,
    itembusinessunitid varchar(40),
    accountid varchar(40),
    businessunitid varchar(40),
    warehouseid varchar(50),
    customerid varchar(50),
    fromuom varchar(40),
    touom varchar(40),
    quantity float8,
    weight float8,
    tareweight float8,
    cubicinches float8,
    height float8,
    length float8,
    width float8,
    velocity varchar(40),
    isdefault bpchar,
    isdeleted bpchar,
    isarchived bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    updatedlocation varchar(200),
    sequence float8,
    fromuomdescription varchar(40),
    touomdescription varchar(40),
    velocitydescription varchar(40),
	CONSTRAINT itemunitofmeasureconversion_id PRIMARY KEY (id)
);","Manages conversions between different units of measure for items, including conversion factors for quantity, weight, and dimensions. Ensures accurate inventory calculations across various UOMs."
item.itemvendor,"CREATE TABLE item.itemvendor (
    id varchar(40) NOT NULL,
    createddate timestamp,
    createdby varchar(250),
    modifieddate timestamp,
    modifiedby varchar(250),
    timeinepoch numeric,
    itembusinessunitid varchar(40),
    accountid varchar(40),
    vendorid varchar(40),
    isdefault bpchar,
    isdeleted bpchar,
    isarchived bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    updatedlocation varchar(200),
    businessunitid varchar(40),
    warehouseid varchar(50),
    customerid varchar(50),
    vendornumber varchar(40),
    vendorname varchar(40),
    contact varchar(200),
    state varchar(50),
    baseuom varchar(4),
    baseuomdescription varchar(40),
    weight float8,
    unitvolume float8,
    averageweight float8,
    weighttolerance float8,
    velocity varchar(4),
    velocitydescription varchar(40),
    length float8,
    width float8,
    height float8,
    address1 varchar(50),
    address2 varchar(50),
    address3 varchar(50),
    city varchar(50),
    country varchar(10),
    postalcode varchar(10),
    email varchar(50),
    issupplier bpchar,
    isvendor bpchar,
	CONSTRAINT itemvendor_id PRIMARY KEY (id)
);","Links items to their respective vendors, including vendor details and item-specific purchasing attributes like base unit of measure and lead times. Facilitates procurement and supplier management."
item.itemwcs,"CREATE TABLE item.itemwcs (
    id varchar(40) NOT NULL,
    createddate timestamp,
    createdby varchar(250),
    modifieddate timestamp,
    modifiedby varchar(250),
    timeinepoch numeric,
    itembusinessunitid varchar(40),
    accountid varchar(40),
    iswcseligible bpchar,
    isputwallexempt bpchar,
    issortableatputwall bpchar,
    isdefault bpchar,
    isdeleted bpchar,
    isarchived bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    updatedlocation varchar(200),
    businessunitid varchar(50),
    warehouseid varchar(50),
    customerid varchar(50),
	CONSTRAINT itemwcs_id PRIMARY KEY (id)
);","Defines item-specific settings for Warehouse Control Systems (WCS), such as eligibility for WCS processing, putwall exemption, and sortability. Optimizes automated warehouse operations."
item.kitcomponent,"CREATE TABLE item.kitcomponent (
    id varchar(40) NOT NULL,
    accountid varchar(40),
    businessunitid varchar(40),
    warehouseid varchar(50),
    customerid varchar(50),
    createddate timestamp,
    createdby varchar(250),
    modifieddate timestamp,
    modifiedby varchar(250),
    timeinepoch numeric,
    itemkitmaintenanceid varchar(40),
    component varchar(50),
    quantity float8,
    componentsize float8,
    isdefault bpchar,
    isdeleted bpchar,
    isarchived bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    updatedlocation varchar(200),
    itembusinessunitid varchar(50),
    itemaccountid varchar(50),
    itemnumber varchar(40),
	CONSTRAINT kitcomponent_id PRIMARY KEY (id)
);","Details the individual components that make up a kitted item, including their quantities and sizes. Used to manage the bill of materials for assembled products."
item.productgroupactivityinfo,"CREATE TABLE item.productgroupactivityinfo (
    id varchar(40) NOT NULL,
    createddate timestamp,
    createdby varchar(250),
    modifieddate timestamp,
    modifiedby varchar(250),
    timeinepoch numeric,
    productgroupbusinessunitid varchar(40),
    accountid varchar(40),
    businessunitid varchar(50),
    warehouseid varchar(50),
    customerid varchar(50),
    status varchar(40),
    isdefault bpchar,
    isdeleted bpchar,
    isarchived bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    updatedlocation varchar(200),
	CONSTRAINT productgroupactivityinfo_id PRIMARY KEY (id)
);","Stores activity-related information for product groups, such as their current status within a business unit. Provides an overview of the product group's lifecycle and operational state."
item.productgroupbusinessunit,"CREATE TABLE item.productgroupbusinessunit (
    id varchar(40) NOT NULL,
    createddate timestamp,
    createdby varchar(250),
    modifieddate timestamp,
    modifiedby varchar(250),
    timeinepoch numeric,
    businessunitid varchar(40),
    description varchar(100),
    code varchar(40),
    rategroup varchar(40),
    shortdateoffset date,
    canshortdatetofirstofmonth bpchar,
    status varchar(40),
    isdefault bpchar,
    isdeleted bpchar,
    isarchived bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    updatedlocation varchar(200),
    accountid varchar(50),
    customerid varchar(50),
    warehouseid varchar(50),
    businessunitcode varchar(20),
	CONSTRAINT productgroupbusinessunit_id PRIMARY KEY (id)
);","Defines product groups within a business unit, including their description, code, and various settings like short date offsets and status. Used to categorize items for reporting and management."
item.productgroupcyclecountcapture,"CREATE TABLE item.productgroupcyclecountcapture (
    id varchar(40) NOT NULL,
    createddate timestamp,
    createdby varchar(250),
    modifieddate timestamp,
    modifiedby varchar(250),
    timeinepoch numeric,
    productgroupbusinessunitid varchar(40),
    accountid varchar(40),
    businessunitid varchar(50),
    warehouseid varchar(50),
    customerid varchar(50),
    captureserialnumber bpchar,
    capturecountryoforigin bpchar,
    capturemanufacturingdate bpchar,
    captureexpirationdate bpchar,
    isdefault bpchar,
    isdeleted bpchar,
    isarchived bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    updatedlocation varchar(200),
CONSTRAINT productgroupcyclecountcapture_id PRIMARY KEY (id)
);","This table stores the settings for capturing item attributes during a cycle count for a specific product group at a business unit. It details whether to capture serial numbers, country of origin, manufacturing dates, and expiration dates. It also contains standard tracking and metadata columns such as identifiers for the associated product group, business unit, account, warehouse, and customer, as well as creation and modification details, versioning, and location information. The table links to productgroupbusinessunit and includes flags for default, deleted, and archived status."
item.productgroupinbound,"CREATE TABLE item.productgroupinbound (
    id varchar(40) NOT NULL,
    createddate timestamp,
    createdby varchar(250),
    modifieddate timestamp,
    modifiedby varchar(250),
    timeinepoch numeric,
    productgroupbusinessunitid varchar(40),
    accountid varchar(40),
    allowdamageditems bpchar,
    enablecaseqrcode bpchar,
    enablelpdimcapture bpchar,
    outboundcatchweight varchar(40),
    putawayconfirmationmethod varchar(40),
    returnsdisposition varchar(40),
    allowsinglequantitylicenseplates bpchar,
    convert9dmfgdateatreceiving bpchar,
    maintainoldestmanufacturingdateatconsolidation bpchar,
    isvendortracked bpchar,
    overridekey varchar(40),
    overridevalue varchar(40),
    isdefault bpchar,
    isdeleted bpchar,
    isarchived bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    updatedlocation varchar(200),
    customerid varchar(50),
    warehouseid varchar(50),
    businessunitid varchar(50),
CONSTRAINT productgroupinbound_id PRIMARY KEY (id)
);","This table defines the inbound processing rules for a product group within a business unit. It includes various settings that govern how items are received and handled, such as whether to allow damaged items, enable QR code scanning for cases, and capture dimensional weight on license plates. The table also specifies policies for catch weight, putaway confirmation, returns disposition, and handling of single-quantity license plates. It further details date conversion and maintenance rules, vendor tracking, and override capabilities. Standard tracking and metadata columns for identifiers, creation/modification details, versioning, and location are also present."
item.productgroupinboundasncapture,"CREATE TABLE item.productgroupinboundasncapture (
    id varchar(40) NOT NULL,
    createddate timestamp,
    createdby varchar(250),
    modifieddate timestamp,
    modifiedby varchar(250),
    timeinepoch numeric,
    productgroupinboundid varchar(40),
    productgroupbusinessunitid varchar(40),
    captureasnserialnumber bpchar,
    isdefault bpchar,
    isdeleted bpchar,
    isarchived bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    updatedlocation varchar(200),
    accountid varchar(50),
    businessunitid varchar(50),
    customerid varchar(50),
    warehouseid varchar(50),
CONSTRAINT productgroupinboundasncapture_id PRIMARY KEY (id)
);","This table configures the capture of serial numbers from Advanced Shipping Notices (ASNs) for inbound product groups. It holds a foreign key to the productgroupinbound table and a flag to determine if the capture of ASN serial numbers is enabled. It includes identifiers for the associated product group, business unit, account, warehouse, and customer. Standard auditing and metadata columns such as creation and modification timestamps, user information, versioning, and location are also part of the table structure. Flags for default, deleted, and archived records are present to manage the record's lifecycle."
item.productgroupinboundcapture,"CREATE TABLE item.productgroupinboundcapture (
    id varchar(40) NOT NULL,
    createddate timestamp,
    createdby varchar(250),
    modifieddate timestamp,
    modifiedby varchar(250),
    timeinepoch numeric,
    productgroupinboundid varchar(40),
    isenabled bpchar,
    capturelotnumber varchar(40),
    captureserialnumber bpchar,
    capturecountryoforigin bpchar,
    capturemanufacturingdate bpchar,
    captureexpirationdate bpchar,
    canreceivedamagedproduct bpchar,
    isdefault bpchar,
    isdeleted bpchar,
    isarchived bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    updatedlocation varchar(200),
    accountid varchar(50),
    businessunitid varchar(50),
    customerid varchar(50),
    warehouseid varchar(50),
    productgroupbusinessunitid varchar(40),
CONSTRAINT productgroupinboundcapture_id PRIMARY KEY (id)
);","This table stores the configuration for capturing specific data points during the inbound process for a product group. It includes flags to enable or disable the capture of lot numbers, serial numbers, country of origin, manufacturing dates, and expiration dates. Additionally, it contains a setting to indicate if damaged products can be received. The table is linked to productgroupinbound and includes identifiers for the product group business unit, account, business unit, customer, and warehouse. It also contains standard auditing and metadata columns for tracking creation, modification, versioning, and location, as well as flags for default, deletion, and archival status."
item.productgroupinboundentryfieldparsing,"CREATE TABLE item.productgroupinboundentryfieldparsing (
    id varchar(40) NOT NULL,
    createddate timestamp,
    createdby varchar(250),
    modifieddate timestamp,
    modifiedby varchar(250),
    timeinepoch numeric,
    productgroupinboundid varchar(40),
    productgroupbusinessunitid varchar(40),
    parseruleaction bpchar,
    entryfield varchar(40),
    entryfieldparsingrule varchar(40),
    isdefault bpchar,
    isdeleted bpchar,
    isarchived bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    updatedlocation varchar(200),
    accountid varchar(50),
    businessunitid varchar(50),
    customerid varchar(50),
    warehouseid varchar(50),
CONSTRAINT productgroupinboundentryfieldparsing_id PRIMARY KEY (id)
);","This table defines the parsing rules for inbound entry fields related to a specific product group. It specifies the actions and rules to be applied to various entry fields during the inbound process, linking to the main productgroupinbound record. Key fields include parseruleaction, entryfield, and entryfieldparsingrule which together dictate how inbound data is to be interpreted and processed. The table also contains identifiers for the associated product group business unit, account, business unit, customer, and warehouse. Standard auditing, versioning, and location tracking columns are included, along with flags for managing the record's lifecycle."
item.productgroupmaintenance,"CREATE TABLE item.productgroupmaintenance (
    id varchar(40) NOT NULL,
    createddate timestamp,
    createdby varchar(250),
    modifieddate timestamp,
    modifiedby varchar(250),
    timeinepoch numeric,
    productgroupbusinessunitid varchar(40),
    accountid varchar(40),
    rategroup varchar(40),
    shortdatetofirstofnextmonth bpchar,
    cartongroup varchar(40),
    isdeleted bpchar,
    isarchived bpchar,
    isdefault bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    updatedlocation varchar(200),
    shortdateoffset numeric,
    businessunitid varchar(50),
CONSTRAINT productgroupmaintenance_id PRIMARY KEY (id)
);","This table stores maintenance settings for product groups within a business unit. It includes configurations for rate groups, carton groups, and rules for handling short dates, such as setting them to the first of the next month with a specified offset. This table is linked to the productgroupbusinessunit table and contains identifiers for the associated account and business unit. It also includes standard columns for auditing, versioning, and location tracking, as well as flags to indicate if a record is the default, has been deleted, or is archived."
item.productgroupoutbound,"CREATE TABLE item.productgroupoutbound (
    id varchar(40) NOT NULL,
    createddate timestamp,
    createdby varchar(250),
    modifieddate timestamp,
    modifiedby varchar(250),
    timeinepoch numeric,
    productgroupbusinessunitid varchar(40),
    accountid varchar(40),
    businessunitid varchar(40),
    warehouseid varchar(50),
    customerid varchar(50),
    backorderpolicy varchar(40),
    shipmentorderquantitytype varchar(40),
    includedefaultinventorystatusselection varchar(1),
    includedefaultinventoryclassselection varchar(1),
    allowsubstitution bpchar,
    platesubstitutionreasonrequired bpchar,
    weightcheckrequired bpchar,
    demandbasedcrossdockeligible bpchar,
    allowsinglequantitylicenseplates bpchar,
    fifowindow float8,
    countryoforiginfield varchar(40),
    countryoforiginvalue varchar(40),
    serialnumberfield varchar(40),
    serialnumbervalue varchar(40),
    pickunitofmeasure varchar(40),
    serialnumbercaptureunitofmeasure varchar(40),
    isdefault bpchar,
    isdeleted bpchar,
    isarchived bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    updatedlocation varchar(200),
CONSTRAINT productgroupoutbound_id PRIMARY KEY (id)
);","This table defines the outbound fulfillment policies for a product group within a business unit. It contains various settings that govern the shipping process, such as the backorder policy, the type of shipment order quantity, and whether to include default inventory status and class selections. The table also specifies rules for item substitution, weight checks, cross-docking eligibility, and the use of single-quantity license plates. It includes settings for the FIFO window, the fields and values for country of origin and serial numbers, and the units of measure for picking and serial number capture. Standard tracking and metadata columns are also present."
item.productgrouppickingcapture,"CREATE TABLE item.productgrouppickingcapture (
    id varchar(40) NOT NULL,
    createddate timestamp,
    createdby varchar(250),
    modifieddate timestamp,
    modifiedby varchar(250),
    timeinepoch numeric,
    accountid varchar(50),
    businessunitid varchar(50),
    warehouseid varchar(50),
    customerid varchar(50),
    capturetype varchar(40),
    capturefield varchar(40),
    capturefieldvalue varchar(40),
    isdefault bpchar,
    isdeleted bpchar,
    isarchived bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    updatedlocation varchar(200),
    productgroupbusinessunitid varchar(40),
    productgroupoutboundid varchar(40),
CONSTRAINT productgrouppickingcapture_id PRIMARY KEY (id)
);","This table stores the rules for capturing data during the picking process for a specific product group's outbound operations. It defines the type of capture, the field to be captured, and the expected value for that field. The table is linked to productgroupbusinessunit and productgroupoutbound and includes identifiers for the account, business unit, warehouse, and customer. It also contains standard columns for auditing, versioning, and location tracking, along with flags to manage the record's lifecycle."
item.productgroupreturn,"CREATE TABLE item.productgroupreturn (
    id varchar(40) NOT NULL,
    createddate timestamp,
    createdby varchar(250),
    modifieddate timestamp,
    modifiedby varchar(250),
    timeinepoch numeric,
    productgroupbusinessunitid varchar(40),
  _accountid varchar(40),
    businessunitid varchar(40),
    warehouseid varchar(50),
    customerid varchar(50),
    restockingfee numeric,
    contactname varchar(150),
    address1 varchar(200),
    address2 varchar(200),
    address3 varchar(200),
    city varchar(150),
    state varchar(150),
    zipcode varchar(15),
    country varchar(2),
    email varchar(200),
    phone varchar(20),
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    updatedlocation varchar(200),
    isdeleted bpchar,
    isarchived bpchar,
    disposition bpchar,
CONSTRAINT productgroupreturn_id PRIMARY KEY (id)
);","This table holds information about the return process for a product group within a business unit. It stores details such as the restocking fee, the return contact person, and the return address. It also specifies the final disposition of the returned items. The table is linked to the productgroupbusinessunit and includes identifiers for the associated account, business unit, warehouse, and customer. Standard auditing, versioning, and location tracking columns are also included, along with flags to manage the record's lifecycle."
item.vendorunitofmeasure,"CREATE TABLE item.vendorunitofmeasure (
    id varchar(40) NOT NULL,
    accountid varchar(40),
    businessunitid varchar(40),
    warehouseid varchar(50),
    customerid varchar(50),
    createddate timestamp,
    createdby varchar(250),
    modifieddate timestamp,
    modifiedby varchar(250),
    timeinepoch numeric,
    itemvendorid varchar(40),
    baseunitofmeasure varchar(4),
    weight float8,
    weightunitofmeasure varchar(4),
    unitvolume float8,
    averageweight float8,
    averageweightunitofmeasure varchar(4),
    weighttolerance float8,
    cubicinches float8,
    velocity varchar(4),
    height float8,
    length float8,
    width float8,
    isdefault bpchar,
    isdeleted bpchar,
    isarchived bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    updatedlocation varchar(200),
CONSTRAINT vendorunitofmeasure_id PRIMARY KEY (id)
);","This table stores the base unit of measure details for items from a specific vendor. It includes information such as weight, volume, dimensions, and velocity. The table also defines the average weight, weight tolerance, and the units for these measurements. It is linked to the itemvendor table and contains identifiers for the account, business unit, warehouse, and customer. Standard auditing, versioning, and location tracking columns are included, as well as flags to manage the record's lifecycle."
item.vendorunitofmeasureconversion,"CREATE TABLE item.vendorunitofmeasureconversion (
    id varchar(40) NOT NULL,
    accountid varchar(40),
    businessunitid varchar(40),
    itembusinessunitid varchar(40),
    warehouseid varchar(50),
    customerid varchar(50),
    createddate timestamp,
    createdby varchar(250),
    modifieddate timestamp,
    modifiedby varchar(250),
    timeinepoch numeric,
    itemvendorid varchar(40),
  _fromuom varchar(40),
    touom varchar(40),
    quantity float8,
    velocity varchar(40),
    cubicinches float8,
    height float8,
    length float8,
    width float8,
    weight float8,
    tareweight float8,
    isdefault bpchar,
    isdeleted bpchar,
    isarchived bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    updatedlocation varchar(200),
    picktotypecode varchar(40),
    containertypecode varchar(40),
    issingleskubuild bpchar,
    stackability float8,
    casesperlayer int4,
    layersperpallet int4,
    sequence int4,
    fromuomdescription varchar(40),
    touomdescription varchar(40),
    velocitydescription varchar(40),
CONSTRAINT vendorunitofmeasureconversion_id PRIMARY KEY (id)
);","This table defines the conversion factors between different units of measure for a vendor's item within a business unit. It details how to convert from a 'from' unit of measure to a 'to' unit of measure, including the conversion quantity and the associated dimensions, weight, and velocity. The table also contains information about packaging and handling, such as container type, pick to type, single SKU build, stackability, and pallet configuration. It is linked to the itemvendor and itembusinessunit tables and includes identifiers for the account, warehouse, and customer. Standard auditing, versioning, and location tracking columns are also part of the table."
orders.billing,"CREATE TABLE orders.billing (
    id varchar(40) NOT NULL,
    orderid varchar(50),
    shippingid varchar(40) NOT NULL,
    shipperaddresstype varchar(50),
    shipperidentifyingnumber varchar(50),
    shipperabbreviation varchar(50),
    shippercompany varchar(50),
    shippercontactfullname varchar(256),
    shippercontactfirstname varchar(50),
    shippercontactlastname varchar(50),
    shippercontactmiddlename varchar(50),
    shippercontactprefix varchar(50),
    shippercontactsuffix varchar(50),
    shipperaddresslines bytea,
    shipperaddressline1 varchar(100),
    shipperaddressline2 varchar(100),
    shipperaddressline3 varchar(100),
    shippercity varchar(50),
    shipperstate varchar(50),
    shipperpostalcode varchar(50),
    shippercountryid varchar(50),
    shippercountry varchar(100),
    shipperregion varchar(50),
    shipperemail varchar(320),
    shipperphone varchar(50),
    shipperfax varchar(50),
    shippermetafields bytea,
    billtoaddresstype varchar(50),
    billtoidentifyingnumber varchar(50),
    billtoabbreviation varchar(50),
    billtocompany varchar(50),
    billtocontactfullname varchar(256),
    billtocontactfirstname varchar(50),
    billtocontactlastname varchar(50),
    billtocontactmiddlename varchar(50),
    billtocontactprefix varchar(50),
    billtocontactsuffix varchar(50),
    billtoaddresslines bytea,
    billtoaddressline1 varchar(100),
    billtoaddressline2 varchar(100),
    billtoaddressline3 varchar(100),
    billtocity varchar(50),
    billtostate varchar(50),
    billtopostalcode varchar(50),
    billtocountryid varchar(50),
    billtocountry varchar(100),
    billtoregion varchar(50),
    billtoemail varchar(320),
    billtophone varchar(50),
    billtofax varchar(50),
    billtometafields bytea,
    soldtoaddresstype varchar(50),
    soldtoidentifyingnumber varchar(50),
    soldtoabbreviation varchar(50),
    soldtocompany varchar(50),
    soldtocontactfullname varchar(256),
    soldtocontactfirstname varchar(50),
    soldtocontactlastname varchar(50),
    soldtocontactmiddlename varchar(50),
    soldtocontactprefix varchar(50),
    soldtocontactsuffix varchar(50),
    soldtoaddresslines bytea,
    soldtoaddressline1 varchar(100),
    soldtoaddressline2 varchar(100),
    soldtoaddressline3 varchar(100),
    soldtocity varchar(50),
    soldtostate varchar(50),
    soldtopostalcode varchar(50),
    soldtocountryid varchar(50),
    soldtocountry varchar(100),
    soldtoregion varchar(50),
    soldtoemail varchar(320),
    soldtophone varchar(50),
    soldtofax varchar(50),
    soldtometafields bytea,
    remittoaddresstype varchar(50),
    remittoidentifyingnumber varchar(50),
    remittoabbreviation varchar(50),
    remittocompany varchar(50),
    remittocontactfullname varchar(256),
    remittocontactfirstname varchar(50),
    remittocontactlastname varchar(50),
    remittocontactmiddlename varchar(50),
    remittocontactprefix varchar(50),
    remittocontactsuffix varchar(50),
    remittoaddresslines bytea,
    remittoaddressline1 varchar(100),
    remittoaddressline2 varchar(100),
    remittoaddressline3 varchar(100),
    remittocity varchar(50),
    remittostate varchar(50),
    remittopostalcode varchar(50),
    remittocountryid varchar(50),
    remittocountry varchar(100),
    remittoregion varchar(50),
    remittoemail varchar(320),
    remittophone varchar(50),
    remittofax varchar(50),
    remittometafields bytea,
    paymentterms varchar(50),
    termsofsale varchar(50),
    billingaccount varchar(50),
    isthirdparty bool,
    thirdpartyaccount varchar(50),
    thirdpartyaddresstype varchar(50),
    thirdpartyidentifyingnumber varchar(50),
    thirdpartyabbreviation varchar(50),
    thirdpartycompany varchar(50),
    thirdpartycontactfullname varchar(256),
    thirdpartycontactfirstname varchar(50),
    thirdpartycontactlastname varchar(50),
    thirdpartycontactmiddlename varchar(50),
    thirdpartycontactprefix varchar(50),
    thirdpartycontactsuffix varchar(50),
    thirdpartyaddresslines bytea,
    thirdpartyaddressline1 varchar(100),
    thirdpartyaddressline2 varchar(100),
    thirdpartyaddressline3 varchar(100),
    thirdpartycity varchar(50),
    thirdpartystate varchar(50),
    thirdpartypostalcode varchar(50),
    thirdpartycountryid varchar(50),
    thirdpartycountry varchar(100),
    thirdpartyregion varchar(50),
    thirdpartyemail varchar(320),
    thirdpartyphone varchar(50),
    thirdpartyfax varchar(50),
    thirdpartymetafields bytea,
    consigneeaccount varchar(50),
    codmethod varchar(50),
    codpaymentmethod varchar(50),
    codamount numeric,
    codcurrencycode varchar(50),
    codreturnmethod varchar(50),
    codnumber varchar(50),
    iscod bool,
    codcashiercheckmoonly bool,
    codaddshipcharges bool,
    codcollectamount numeric,
    codcollectcurrencycode varchar(50),
    codconsigneebill bool,
    handlingchargeamount numeric,
    handlingchargecurrencycode varchar(50),
    freightraterequired bool,
    freightrateamount numeric,
    freightratecurrencycode varchar(50),
    codhandlingchargeamount numeric,
    codhandlingchargecurrencycode varchar(50),
    accountid varchar(50),
    businessunitid varchar(50),
    customerid varchar(50),
    warehouseid varchar(50),
    createdby varchar(320),
    createddate timestamp,
    modifiedby varchar(320),
    modifieddate timestamp,
    timeinepoch int4,
    isdeleted bpchar,
    isarchived bpchar,
    isdefault bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    modifiedlocation varchar(200),
    updatedlocation varchar(200),
    codcompanyname varchar(256),
    brokeraddresstype varchar(50),
    brokeridentifyingnumber varchar(50),
    brokerabbreviation varchar(50),
    brokercompany varchar(50),
    brokercontactfullname varchar(256),
    brokercontactfirstname varchar(50),
    brokercontactlastname varchar(50),
    brokercontactmiddlename varchar(50),
    brokercontactprefix varchar(50),
    brokercontactsuffix varchar(50),
    brokeraddresslines bytea,
    brokeraddressline1 varchar(100),
    brokeraddressline2 varchar(100),
    brokeraddressline3 varchar(100),
    brokercity varchar(50),
    brokerstate varchar(50),
    brokerpostalcode varchar(50),
    brokercountryid varchar(50),
    brokercountry varchar(50),
    brokerregion varchar(50),
    brokeremail varchar(320),
    brokerphone varchar(50),
    brokerfax varchar(50),
    brokermetafields bytea,
	CONSTRAINT billing_id PRIMARY KEY (id)
);","This table stores all the billing and shipping addresses associated with an order. It includes detailed information for the shipper, bill-to, sold-to, remit-to, third-party, and broker entities. It also contains financial details such as payment terms, terms of sale, Collect on Delivery (COD) information, handling charges, and freight rates."
orders.cartinformation,"CREATE TABLE orders.cartinformation (
    id varchar(40) NOT NULL,
    orderid varchar(40) NOT NULL,
    ordernumber varchar(50),
    ordername varchar(50),
    appid varchar(50),
    token varchar(1000),
    checkouttoken varchar(1000),
    landingsite varchar(500),
    note varchar(1000),
    test varchar(50),
    isvirtualflag varchar(50),
    amazonorderreferenceid varchar(50),
    accountid varchar(50),
    businessunitid varchar(50),
    customerid varchar(50),
    warehouseid varchar(50),
    createdby varchar(320),
    createddate timestamp,
    modifiedby varchar(320),
    modifieddate timestamp,
    timeinepoch int4,
    isdeleted bpchar,
    isarchived bpchar,
    isdefault bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    modifiedlocation varchar(200),
    updatedlocation varchar(200),
    confirmationnumber varchar(256),
	CONSTRAINT cartinformation_id PRIMARY KEY (id)
);","This table contains the primary details of a customer's shopping cart for a specific order. It includes unique identifiers for the order and cart, checkout tokens, the landing page from which the order was initiated, and any special notes. It serves as a central record linking to customer, account, and warehouse information."
orders.cartstatuses,"CREATE TABLE orders.cartstatuses (
    id varchar(40) NOT NULL,
    cartinformationid varchar(40) NOT NULL,
    cancelreason varchar(256),
    cancelledat timestamp,
    closedat timestamp,
    createdat timestamp,
    processedat timestamp,
    updatedat timestamp,
    status varchar(50),
    state varchar(50),
    accountid varchar(50),
    businessunitid varchar(50),
    customerid varchar(50),
    warehouseid varchar(50),
    createdby varchar(320),
    createddate timestamp,
    modifiedby varchar(320),
    modifieddate timestamp,
    timeinepoch int4,
    isdeleted bpchar,
    isarchived bpchar,
    isdefault bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    modifiedlocation varchar(200),
    updatedlocation varchar(200),
	CONSTRAINT cartstatuses_id PRIMARY KEY (id)
);","This table tracks the lifecycle and status of a shopping cart. It records key timestamps for events like creation, processing, cancellation, and closure. It holds the current status and state of the cart, along with the reason for cancellation if applicable, and is linked to a specific cart information record."
orders.customerinformation,"CREATE TABLE orders.customerinformation (
    id varchar(40) NOT NULL,
    cartinformationid varchar(40) NOT NULL,
    fullname varchar(256),
    firstname varchar(50),
    lastname varchar(50),
    middlename varchar(50),
    prefix varchar(50),
    suffix varchar(50),
    locale varchar(50),
    state varchar(50),
    email varchar(320),
    phone varchar(50),
    orderscount int4,
    totalspentamount numeric,
    totalspentcurrencycode varchar(50),
    note varchar(1000),
    verifiedemail varchar(320),
    taxexempt bool,
    tags varchar(256),
    ordername varchar(50),
    currency varchar(50),
    valueaddedtax varchar(50),
    isemailoptin bool,
    accountid varchar(50),
    businessunitid varchar(50),
    customerid varchar(50),
    warehouseid varchar(50),
    createdby varchar(320),
    createddate timestamp,
    modifiedby varchar(320),
    modifieddate timestamp,
    timeinepoch int4,
    isdeleted bpchar,
    isarchived bpchar,
    isdefault bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    modifiedlocation varchar(200),
    updatedlocation varchar(200),
	CONSTRAINT customerinformation_id PRIMARY KEY (id)
);","This table holds detailed information about the customer who placed the order. It includes personal contact details like name, email, and phone number, as well as customer-specific metrics such as their total number of orders and total amount spent. It also stores preferences and settings like tax-exempt status and email marketing opt-in."
orders.customerinformationaddresses,"CREATE TABLE orders.customerinformationaddresses (
    id varchar(40) NOT NULL,
    customerinformationid varchar(40) NOT NULL,
    addresstype varchar(50),
    identifyingnumber varchar(50),
    abbreviation varchar(50),
    company varchar(50),
    contactfullname varchar(256),
    contactfirstname varchar(50),
    contactlastname varchar(50),
    contactmiddlename varchar(50),
    contactprefix varchar(50),
    contactsuffix varchar(50),
    addresslines bytea,
    addressline1 varchar(100),
    addressline2 varchar(100),
    addressline3 varchar(100),
    city varchar(50),
    state varchar(50),
    postalcode varchar(50),
    countryid varchar(50),
    country varchar(100),
    region varchar(50),
    email varchar(320),
    phone varchar(50),
    fax varchar(50),
    metafields bytea,
    accountid varchar(50),
    businessunitid varchar(50),
    customerid varchar(50),
    warehouseid varchar(50),
    createdby varchar(320),
    createddate timestamp,
    modifiedby varchar(320),
    modifieddate timestamp,
    timeinepoch int4,
    isdeleted bpchar,
    isarchived bpchar,
    isdefault bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    modifiedlocation varchar(200),
    updatedlocation varchar(200),
	CONSTRAINT customerinformationaddresses_id PRIMARY KEY (id)
);","Customer address information for orders including shipping, billing, and contact addresses. Stores multiple address types per customer for order processing and delivery management."
orders.customerinformationdefaultaddresses,"CREATE TABLE orders.customerinformationdefaultaddresses (
    id varchar(40) NOT NULL,
    customerinformationid varchar(40) NOT NULL,
    addresstype varchar(50),
    identifyingnumber varchar(50),
    abbreviation varchar(50),
    company varchar(50),
    contactfullname varchar(256),
    contactfirstname varchar(50),
    contactlastname varchar(50),
    contactmiddlename varchar(50),
    contactprefix varchar(50),
    contactsuffix varchar(50),
    addresslines bytea,
    addressline1 varchar(100),
    addressline2 varchar(100),
    addressline3 varchar(100),
    city varchar(50),
    state varchar(50),
    postalcode varchar(50),
    countryid varchar(50),
    country varchar(100),
    region varchar(50),
    email varchar(320),
    phone varchar(50),
    fax varchar(50),
    metafields bytea,
    accountid varchar(50),
    businessunitid varchar(50),
    customerid varchar(50),
    warehouseid varchar(50),
    createdby varchar(320),
    createddate timestamp,
    modifiedby varchar(320),
    modifieddate timestamp,
    timeinepoch int4,
    isdeleted bpchar,
    isarchived bpchar,
    isdefault bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    modifiedlocation varchar(200),
    updatedlocation varchar(200),
	CONSTRAINT customerinformationdefaultaddresses_id PRIMARY KEY (id)
);","Default address preferences for customers defining primary shipping and billing addresses. Manages customer address defaults for streamlined order processing and address selection."
orders.deliveryoptions,"CREATE TABLE orders.deliveryoptions (
    id varchar(40) NOT NULL,
    shippingid varchar(40) NOT NULL,
    delivery varchar(50),
    deliveryrequesteddatetime timestamp,
    arrivaldate timestamp,
    expectedreceiptdate timestamp,
    cancelafter timestamp,
    cancelifnotdeliveredby timestamp,
    donotdeliverbefore timestamp,
    donotdeliverafter timestamp,
    holdatlocationaddresstype varchar(50),
    holdatlocationidentifyingnumber varchar(50),
    holdatlocationabbreviation varchar(50),
    holdatlocationcompany varchar(50),
    holdatlocationcontactfullname varchar(256),
    holdatlocationcontactfirstname varchar(50),
    holdatlocationcontactlastname varchar(50),
    holdatlocationcontactmiddlename varchar(50),
    holdatlocationcontactprefix varchar(50),
    holdatlocationcontactsuffix varchar(50),
    holdatlocationaddresslines bytea,
    holdatlocationaddressline1 varchar(100),
    holdatlocationaddressline2 varchar(100),
    holdatlocationaddressline3 varchar(100),
    holdatlocationcity varchar(50),
    holdatlocationstate varchar(50),
    holdatlocationpostalcode varchar(50),
    holdatlocationcountryid varchar(50),
    holdatlocationcountry varchar(100),
    holdatlocationregion varchar(50),
    holdatlocationemail varchar(256),
    holdatlocationphone varchar(50),
    holdatlocationfax varchar(50),
    holdatlocationmetafields bytea,
    holdatlocationid varchar(50),
    helperdelivery bool,
    forkliftdelivery bool,
    insidedelivery bool,
    liftgatedelivery bool,
    palletjackdelivery bool,
    stairdelivery bool,
    specialdelivery bool,
    holidaydelivery bool,
    holdatlocation bool,
    isresidential bool,
    noshorts bool,
    saturdaydelivery bool,
    areoveragesalerted bool,
    priority varchar(50),
    source varchar(256),
    deliveryappointmentconfirmationfullname varchar(50),
    deliveryappointmentconfirmationfirstname varchar(50),
    deliveryappointmentconfirmationlastname varchar(50),
    deliveryappointmentconfirmationmiddlename varchar(50),
    deliveryappointmentconfirmationprefix varchar(50),
    deliveryappointmentconfirmationsuffix varchar(50),
    shipno varchar(50),
    accountid varchar(50),
    businessunitid varchar(50),
    customerid varchar(50),
    warehouseid varchar(50),
    createdby varchar(320),
    createddate timestamp,
    modifiedby varchar(320),
    modifieddate timestamp,
    timeinepoch int4,
    isdeleted bpchar,
    isarchived bpchar,
    isdefault bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    modifiedlocation varchar(200),
    updatedlocation varchar(200),
    deliveryreference varchar(256),
    packaging varchar(256),
    incoterm varchar(256),
    incotermfacility varchar(256),
    markfor varchar(256),
    transplanowner varchar(256),
    callfordelivery varchar(256),
    language varchar(50),
    scheduleddayofweek numeric,
    deliverystart timestamp,
    deliveryend timestamp,
    postofficebox bool,
    nonmachineable bool,
    isguaranteed bool,
    signaturerequired bool,
	CONSTRAINT deliveryoptions_id PRIMARY KEY (id)
);","Delivery service options and requirements for orders including special handling, delivery preferences, and service level selections. Manages customer delivery choices and carrier service options."
orders.duties,"CREATE TABLE orders.duties (
    id varchar(40) NOT NULL,
    orderdetailid varchar(40) NOT NULL,
    harmonizedsystemcode varchar(50),
    countrycodeoforigin varchar(50),
    shopamount numeric,
    shopcurrencycode varchar(50),
    presentmentamount numeric,
    presentmentcurrencycode varchar(50),
    accountid varchar(50),
    businessunitid varchar(50),
    customerid varchar(50),
    warehouseid varchar(50),
    createdby varchar(320),
    createddate timestamp,
    modifiedby varchar(320),
    modifieddate timestamp,
    timeinepoch int4,
    isdeleted bpchar,
    isarchived bpchar,
    isdefault bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    modifiedlocation varchar(200),
    updatedlocation varchar(200),
	CONSTRAINT duties_id PRIMARY KEY (id)
);","Import duties and customs charges for international orders including duty rates, amounts, and classification codes. Manages customs compliance and duty calculations for cross-border shipments."
orders.dutytaxes,"CREATE TABLE orders.dutytaxes (
    id varchar(40) NOT NULL,
    dutyid varchar(40),
    accountid varchar(50),
    businessunitid varchar(50),
    customerid varchar(50),
    warehouseid varchar(50),
    name varchar(256),
    type varchar(50),
    taxamount numeric,
    taxcurrencycode varchar(50),
    presentmentamount numeric,
    presentmentcurrencycode varchar(50),
    baseamount numeric,
    basecurrencycode varchar(50),
    rates bytea,
    createdby varchar(320),
    createddate timestamp,
    modifiedby varchar(320),
    modifieddate timestamp,
    timeinepoch int4,
    isdeleted bpchar,
    isarchived bpchar,
    isdefault bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    modifiedlocation varchar(200),
    updatedlocation varchar(200),
	CONSTRAINT dutytaxes_id PRIMARY KEY (id)
);","Tax calculations applied to import duties including VAT on duties, additional customs taxes, and government charges. Manages complex tax calculations for international trade compliance."
orders.dutytaxrates,"CREATE TABLE orders.dutytaxrates (
    id varchar(40) NOT NULL,
    dutytaxid varchar(40),
    accountid varchar(50),
    businessunitid varchar(50),
    customerid varchar(50),
    warehouseid varchar(50),
    name varchar(50),
    rate varchar(50),
    createdby varchar(320),
    createddate timestamp,
    modifiedby varchar(320),
    modifieddate timestamp,
    timeinepoch int4,
    isdeleted bpchar,
    isarchived bpchar,
    isdefault bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    updatedlocation varchar(200),
	CONSTRAINT dutytaxrates_id PRIMARY KEY (id)
);","Tax rate definitions and structures for duty-related taxes including rate schedules, jurisdiction rules, and calculation methods. Maintains tax rate master data for international duty calculations."
orders.fees,"CREATE TABLE orders.fees (
    id varchar(40) NOT NULL,
    paymentinformationid varchar(40),
    accountid varchar(50),
    businessunitid varchar(50),
    customerid varchar(50),
    warehouseid varchar(50),
    name varchar(50) NOT NULL,
    taxclass varchar(50),
    taxstatus varchar(50),
    totalfeesshopamount numeric,
    totalfeesshopcurrencycode varchar(50),
    totalfeespresentmentamount numeric,
    totalfeespresentmentcurrencycode varchar(50),
    totalfeestaxshopamount numeric,
    totalfeestaxshopcurrencycode varchar(50),
    totalfeestaxpresentmentamount numeric,
    totalfeestaxpresentmentcurrencycode varchar(50),
    createdby varchar(320),
    createddate timestamp,
    modifiedby varchar(320),
    modifieddate timestamp,
    timeinepoch int4,
    isdeleted bpchar,
    isarchived bpchar,
    isdefault bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    modifiedlocation varchar(200),
    updatedlocation varchar(200),
	CONSTRAINT fees_id PRIMARY KEY (id)
);","Additional fees and charges applied to orders including processing fees, handling charges, and service fees. Manages miscellaneous charges beyond product costs, shipping, and taxes."
orders.feetaxes,"CREATE TABLE orders.feetaxes (
    id varchar(40) NOT NULL,
    feeid varchar(40),
    accountid varchar(50),
    businessunitid varchar(50),
    customerid varchar(50),
    warehouseid varchar(50),
    name varchar(256),
    type varchar(50),
    amount numeric,
    amountcurrencycode varchar(50),
    presentmentamount numeric,
    presentmentcurrencycode varchar(50),
    baseamount numeric,
    baseamountcurrencycode varchar(50),
    rates bytea,
    createdby varchar(320),
    createddate timestamp,
    modifiedby varchar(320),
    modifieddate timestamp,
    timeinepoch int4,
    isdeleted bpchar,
    isarchived bpchar,
    isdefault bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    modifiedlocation varchar(200),
    updatedlocation varchar(200),
	CONSTRAINT feetaxes_id PRIMARY KEY (id)
);","Tax calculations applied to additional fees and charges including sales tax on processing fees and service charges. Ensures tax compliance for all fee-based charges on orders."
orders.fulfillmentdeliveryoptions,"CREATE TABLE orders.fulfillmentdeliveryoptions (
    id varchar(40) NOT NULL,
    fulfillmentid varchar(40) NOT NULL,
    accountid varchar(50),
    businessunitid varchar(50),
    customerid varchar(50),
    warehouseid varchar(50),
    delivery varchar(50),
    deliveryrequesteddatetime timestamp,
    arrivaldate timestamp,
    expectedreceiptdate timestamp,
    cancelafter timestamp,
    cancelifnotdeliveredby timestamp,
    donotdeliverbefore timestamp,
    donotdeliverafter timestamp,
    holdatlocationaddresstype varchar(50),
    holdatlocationidentifyingnumber varchar(50),
    holdatlocationabbreviation varchar(50),
    holdatlocationcompany varchar(50),
    holdatlocationcontactfullname varchar(256),
    holdatlocationcontactfirstname varchar(50),
    holdatlocationcontactlastname varchar(50),
    holdatlocationcontactmiddlename varchar(50),
    holdatlocationcontactprefix varchar(50),
    holdatlocationcontactsuffix varchar(50),
    holdatlocationaddresslines bytea,
    holdatlocationaddressline1 varchar(100),
    holdatlocationaddressline2 varchar(100),
    holdatlocationaddressline3 varchar(100),
    holdatlocationcity varchar(50),
    holdatlocationstate varchar(50),
    holdatlocationpostalcode varchar(50),
    holdatlocationcountryid varchar(50),
    holdatlocationcountry varchar(100),
    holdatlocationregion varchar(50),
    holdatlocationemail varchar(256),
    holdatlocationphone varchar(50),
    holdatlocationfax varchar(50),
    holdatlocationmetafields bytea,
    holdatlocationid varchar(50),
    helperdelivery bool,
    forkliftdelivery bool,
    insidedelivery bool,
    liftgatedelivery bool,
    palletjackdelivery bool,
    stairdelivery bool,
    specialdelivery bool,
    holidaydelivery bool,
    holdatlocation bool,
    isresidential bool,
    noshorts bool,
    saturdaydelivery bool,
    areoveragesalerted bool,
    priority varchar(50),
    source varchar(50),
    deliveryappointmentconfirmationfullname varchar(256),
    deliveryappointmentconfirmationfirstname varchar(50),
    deliveryappointmentconfirmationlastname varchar(50),
    deliveryappointmentconfirmationmiddlename varchar(50),
    deliveryappointmentconfirmationprefix varchar(50),
    deliveryappointmentconfirmationsuffix varchar(50),
    shipno varchar(50),
    createdby varchar(320),
    createddate timestamp,
    modifiedby varchar(320),
    modifieddate timestamp,
    timeinepoch int4,
    isdeleted bpchar,
    isarchived bpchar,
    isdefault bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    modifiedlocation varchar(200),
    updatedlocation varchar(200),
	CONSTRAINT fulfillmentdeliveryoptions_id PRIMARY KEY (id)
);","This table defines the specific delivery options and requirements for an order fulfillment. It includes requested delivery dates, time windows, and flags for special handling needs such as liftgate, inside delivery, or Saturday delivery. It also contains details for holding a shipment at a specific location and contact information for delivery appointments."
orders.fulfillmentduties,"CREATE TABLE orders.fulfillmentduties (
    id varchar(40) NOT NULL,
    fulfillmentorderdetailid varchar(40) NOT NULL,
    accountid varchar(50),
    businessunitid varchar(50),
    customerid varchar(50),
    warehouseid varchar(50),
    harmonizedsystemcode varchar(50),
    countrycodeoforigin varchar(50),
    shopamount numeric,
    shopcurrencycode varchar(50),
    presentmentamount numeric,
    presentmentcurrencycode varchar(50),
    createdby varchar(320),
    createddate timestamp,
    modifiedby varchar(320),
    modifieddate timestamp,
    timeinepoch int4,
    isdeleted bpchar,
    isarchived bpchar,
    isdefault bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    modifiedlocation varchar(200),
    updatedlocation varchar(200),
	CONSTRAINT fulfillmentduties_id PRIMARY KEY (id)
);","This table stores information about customs duties for internationally shipped items within a fulfillment. It includes the harmonized system (HS) code, country of origin, and the calculated duty amounts in both the shop's and the customer's currencies."
orders.fulfillmentdutytaxes,"CREATE TABLE orders.fulfillmentdutytaxes (
    id varchar(40) NOT NULL,
    fulfillmentdutyid varchar(40) NOT NULL,
    accountid varchar(50),
    businessunitid varchar(50),
    customerid varchar(50),
    warehouseid varchar(50),
    name varchar(256),
    type varchar(50),
    taxamount numeric,
    taxcurrencycode varchar(50),
    presentmentamount numeric,
    presentmentcurrencycode varchar(50),
    baseamount numeric,
    basecurrencycode varchar(50),
    rates bytea,
    createdby varchar(320),
    createddate timestamp,
    modifiedby varchar(320),
    modifieddate timestamp,
    timeinepoch int4,
    isdeleted bpchar,
    isarchived bpchar,
    isdefault bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    modifiedlocation varchar(200),
    updatedlocation varchar(200),
	CONSTRAINT fulfillmentdutytaxes_id PRIMARY KEY (id)
);","This table contains the breakdown of specific taxes applied to the customs duties for a fulfillment. Each record details a specific tax, including its name, type, and the calculated tax amount in various currencies, linking back to a specific duty record."
orders.fulfillmentdutytaxrates,"CREATE TABLE orders.fulfillmentdutytaxrates (
    id varchar(40) NOT NULL,
    fulfillmentdutytaxid varchar(40),
    accountid varchar(50),
    businessunitid varchar(50),
    customerid varchar(50),
    warehouseid varchar(50),
    name varchar(50),
    rate varchar(50),
    createdby varchar(320),
    createddate timestamp,
    modifiedby varchar(320),
    modifieddate timestamp,
    timeinepoch int4,
    isdeleted bpchar,
    isarchived bpchar,
    isdefault bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    updatedlocation varchar(200),
	CONSTRAINT fulfillmentdutytaxrates_id PRIMARY KEY (id)
);","This table stores the specific rates for taxes that are applied to customs duties. It provides a granular view of the tax calculation by linking a named rate to a specific duty tax record, showing how the final tax amount was derived."
orders.fulfillmentgiftcertificates,"CREATE TABLE orders.fulfillmentgiftcertificates (
    id varchar(40) NOT NULL,
    fulfillmentorderdetailid varchar(40),
    accountid varchar(50),
    businessunitid varchar(50),
    customerid varchar(50),
    warehouseid varchar(50),
    fullname varchar(256),
    firstname varchar(50),
    lastname varchar(50),
    middlename varchar(50),
    prefix varchar(50),
    suffix varchar(50),
    amount numeric,
    currencycode varchar(50),
    giftcertificatetype varchar(50),
    createdby varchar(320),
    createddate timestamp,
    modifiedby varchar(320),
    modifieddate timestamp,
    timeinepoch int4,
    isdeleted bpchar,
    isarchived bpchar,
    isdefault bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    modifiedlocation varchar(200),
    updatedlocation varchar(200),
	CONSTRAINT fulfillmentgiftcertificates_id PRIMARY KEY (id)
);","This table records the details of gift certificates that have been applied to an order fulfillment. It includes information such as the recipient's name, the value and currency of the certificate, and the type of gift certificate used."
orders.fulfillmentgiftwraps,"CREATE TABLE orders.fulfillmentgiftwraps (
    id varchar(40) NOT NULL,
    fulfillmentorderdetailoptionid varchar(40),
    accountid varchar(50),
    businessunitid varchar(50),
    customerid varchar(50),
    warehouseid varchar(50),
    fullname varchar(256),
    firstname varchar(50),
    lastname varchar(50),
    middlename varchar(50),
    prefix varchar(50),
    suffix varchar(50),
    message varchar(1000),
    amount numeric,
    currencycode varchar(50),
    createdby varchar(320),
    createddate timestamp,
    modifiedby varchar(320),
    modifieddate timestamp,
    timeinepoch int4,
    isdeleted bpchar,
    isarchived bpchar,
    isdefault bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    modifiedlocation varchar(200),
    updatedlocation varchar(200),
	CONSTRAINT fulfillmentgiftwraps_id PRIMARY KEY (id)
);","This table stores information related to gift wrapping services for an item in a fulfillment. It includes the recipient's name, a personalized message for the gift, and the cost associated with the gift wrapping service."
orders.fulfillmentinformation,"CREATE TABLE orders.fulfillmentinformation (
    id varchar(40) NOT NULL,
    fulfillmentid varchar(40),
    accountid varchar(50),
    businessunitid varchar(50),
    customerid varchar(50),
    warehouseid varchar(50),
    status varchar(50),
    canshippartiallyflag numeric,
    canshippartiallyitemflag numeric,
    deliveryservice varchar(50),
    trackingurls bytea,
    ismultipiece bool,
    returnlabel bool,
    labelrotation numeric,
    lossprotectionmethod varchar(50),
    lossprotectiondeclaredamount numeric,
    lossprotectiondeclaredcurrencycode varchar(50),
    signaturetype varchar(50),
    carrier varchar(50),
    createdby varchar(320),
    createddate timestamp,
    modifiedby varchar(320),
    modifieddate timestamp,
    timeinepoch int4,
    isdeleted bpchar,
    isarchived bpchar,
    isdefault bpchar,
    sealnumber varchar(256),
    disposition varchar(256),
    fulfillments bytea,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    modifiedlocation varchar(200),
    updatedlocation varchar(200),
    receivedat timestamp,
    deliveredat timestamp,
	CONSTRAINT fulfillmentinformation_id PRIMARY KEY (id)
);","Detailed fulfillment information including processing status, warehouse location, and fulfillment method details. Provides comprehensive fulfillment tracking and operational data for order processing."
orders.fulfillmentorderdetaildiscountamounts,"CREATE TABLE orders.fulfillmentorderdetaildiscountamounts (
    id varchar(40) NOT NULL,
    fulfillmentorderdetailid varchar(40),
    accountid varchar(50),
    businessunitid varchar(50),
    customerid varchar(50),
    warehouseid varchar(50),
    orderdetaildiscountamount numeric,
    orderdetaildiscountcurrencycode varchar(50),
    invoicedamount numeric,
    invoicedcurrencycode varchar(50),
    percentamount numeric,
    percentcurrencycode varchar(50),
    refundedamount numeric,
    refundedcurrencycode varchar(50),
    presentmentamount numeric,
    presentmentcurrencycode varchar(50),
    createdby varchar(320),
    createddate timestamp,
    modifiedby varchar(320),
    modifieddate timestamp,
    timeinepoch int4,
    isdeleted bpchar,
    isarchived bpchar,
    isdefault bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    modifiedlocation varchar(200),
    updatedlocation varchar(200),
	CONSTRAINT fulfillmentorderdetaildiscountamounts_id PRIMARY KEY (id)
);","This table itemizes the discount amounts applied to a specific detail line within a fulfillment. It records various financial figures related to discounts, including the original discount, invoiced amount, percentage value, and any refunded amounts, in multiple currencies."
orders.fulfillmentorderdetailmessages,"CREATE TABLE orders.fulfillmentorderdetailmessages (
    id varchar(40) NOT NULL,
    fulfillmentorderdetailid varchar(40),
    accountid varchar(50),
    businessunitid varchar(50),
    customerid varchar(50),
    warehouseid varchar(50),
    senderfullname varchar(256),
    senderfirstname varchar(50),
    senderlastname varchar(50),
    sendermiddlename varchar(50),
    senderprefix varchar(50),
    sendersuffix varchar(50),
    recipientfullname varchar(256),
    recipientfirstname varchar(50),
    recipientlastname varchar(50),
    recipientmiddlename varchar(50),
    recipientprefix varchar(50),
    recipientsuffix varchar(50),
    message varchar(1000),
    createdby varchar(320),
    createddate timestamp,
    modifiedby varchar(320),
    modifieddate timestamp,
    timeinepoch int4,
    isdeleted bpchar,
    isarchived bpchar,
    isdefault bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    modifiedlocation varchar(200),
    updatedlocation varchar(200),
	CONSTRAINT fulfillmentorderdetailmessages_id PRIMARY KEY (id)
);","Stores personalized messages associated with a specific item (order detail) within a fulfillment. This includes the sender's name, the recipient's name, and the text of the message, typically used for gifts."
orders.fulfillmentorderdetailoptions,"CREATE TABLE orders.fulfillmentorderdetailoptions (
    id varchar(40) NOT NULL,
    fulfillmentorderdetailid varchar(40),
    accountid varchar(50),
    businessunitid varchar(50),
    customerid varchar(50),
    warehouseid varchar(50),
    tariffconversion varchar(50),
    tariffunitofmeasure varchar(50),
    nodiscountflag varchar(50),
    isfreeshippingflag varchar(50),
    addedbypromotion bool,
    physicalordigitaltype varchar(50),
    taxable bool,
    pastatus varchar(50),
    giftcard bool,
    fulfillmentstatus varchar(50),
    fulfillmentservice varchar(50),
    htc varchar(50),
    backorder bool,
    rfautodisplay bool,
    allowmultipleitems bool,
    accumulatelinequantity bool,
    requiresshipping bool,
    createdby varchar(320),
    createddate timestamp,
    modifiedby varchar(320),
    modifieddate timestamp,
    timeinepoch int4,
    isdeleted bpchar,
    isarchived bpchar,
    isdefault bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    modifiedlocation varchar(200),
    updatedlocation varchar(200),
    pallettype varchar(50),
    serialnumberrequired bool,
    allowrevivalreceiptallocation bool,
    segmentname varchar(256),
    effectiverank varchar(50),
    preorderitem varchar(50),
    isgift bool,
    optionsetid numeric,
    isrefunded bool,
	CONSTRAINT fulfillmentorderdetailoptions_id PRIMARY KEY (id)
);","Contains various options and flags for a specific item (order detail) in a fulfillment. This includes settings like whether the item is taxable, requires shipping, is on backorder, is a gift, or requires a serial number, controlling how the item is processed."
orders.fulfillmentorderdetailpriceamounts,"CREATE TABLE orders.fulfillmentorderdetailpriceamounts (
    id varchar(40) NOT NULL,
    fulfillmentorderdetailid varchar(40),
    accountid varchar(50),
    businessunitid varchar(50),
    customerid varchar(50),
    warehouseid varchar(50),
    listpriceamount numeric,
    listpricecurrencycode varchar(50),
    presentmentamount numeric,
    presentmentcurrencycode varchar(50),
    salepriceamount numeric,
    salepricecurrencycode varchar(50),
    priceamount numeric,
    pricecurrencycode varchar(50),
    includingtaxamount numeric,
    includingtaxcurrencycode varchar(50),
    createdby varchar(320),
    createddate timestamp,
    modifiedby varchar(320),
    modifieddate timestamp,
    timeinepoch int4,
    isdeleted bpchar,
    isarchived bpchar,
    isdefault bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    modifiedlocation varchar(200),
    updatedlocation varchar(200),
	CONSTRAINT fulfillmentorderdetailpriceamounts_id PRIMARY KEY (id)
);","Provides a detailed breakdown of the per-unit price for a single item (order detail) in a fulfillment. It stores various price points such as the list price, sale price, final price, and price including tax, each with its corresponding currency."
orders.fulfillmentorderdetailrowamounts,"CREATE TABLE orders.fulfillmentorderdetailrowamounts (
    id varchar(40) NOT NULL,
    fulfillmentorderdetailid varchar(40),
    accountid varchar(50),
    businessunitid varchar(50),
    customerid varchar(50),
    warehouseid varchar(50),
    invoicedamount numeric,
    invoicedcurrencycode varchar(50),
    totalamount numeric,
    totalcurrencycode varchar(50),
    totalincludingtaxamount numeric,
    totalincludingtaxcurrencycode varchar(50),
    createdby varchar(320),
    createddate timestamp,
    modifiedby varchar(320),
    modifieddate timestamp,
    timeinepoch int4,
    isdeleted bpchar,
    isarchived bpchar,
    isdefault bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    modifiedlocation varchar(200),
    updatedlocation varchar(200),
	CONSTRAINT fulfillmentorderdetailrowamounts_id PRIMARY KEY (id)
);","Stores the total calculated amounts for an entire line item (order detail row) in a fulfillment. This includes the total invoiced amount, the total row amount before tax, and the total row amount including tax, representing the aggregate value for the quantity of that item."
orders.fulfillmentorderdetails,"CREATE TABLE orders.fulfillmentorderdetails (
    id varchar(40) NOT NULL,
    fulfillmentid varchar(40) NOT NULL,
    sku varchar(256) NOT NULL,
    lotnumber varchar(256),
    totalcouponshopamount numeric,
    totalcouponshopcurrencycode varchar(50),
    totalcouponpresentmentamount numeric,
    totalcouponpresentmentcurrencycode varchar(50),
    subtotalshopamount numeric,
    subtotalshopcurrencycode varchar(50),
    subtotalpresentmentamount numeric,
    subtotalpresentmentcurrencycode varchar(50),
    subtotaltaxshopamount numeric,
    subtotaltaxshopcurrencycode varchar(50),
    subtotaltaxpresentmentamount numeric,
    subtotaltaxpresentmentcurrencycode varchar(50),
    totalsaleshopamount numeric,
    totalsaleshopcurrencycode varchar(50),
    totalsalepresentmentamount numeric,
    totalsalepresentmentcurrencycode varchar(50),
    discountnames bytea,
    discounts bytea,
    linenumber varchar(50),
    applineid varchar(50),
    purchaseordernumber varchar(50),
    inventorystatuscode varchar(50),
    inventorystatusindicator varchar(50),
    inventorystatusreceived varchar(50),
    inventorystatusto varchar(50),
    inventoryclasscode varchar(50),
    inventoryclassindicator varchar(50),
    toinventoryclass varchar(50),
    instruction varchar(1000),
    billofladingcomment varchar(1000),
    originlocationaddresstype varchar(50),
    originlocationidentifyingnumber varchar(50),
    originlocationabbreviation varchar(50),
    originlocationcompany varchar(50),
    originlocationcontactfullname varchar(256),
    originlocationcontactfirstname varchar(50),
    originlocationcontactlastname varchar(50),
    originlocationcontactmiddlename varchar(50),
    originlocationcontactprefix varchar(50),
    originlocationcontactsuffix varchar(50),
    originlocationaddresslines bytea,
    originlocationaddressline1 varchar(100),
    originlocationaddressline2 varchar(100),
    originlocationaddressline3 varchar(100),
    originlocationcity varchar(50),
    originlocationstate varchar(50),
    originlocationpostalcode varchar(50),
    originlocationcountryid varchar(50),
    originlocationcountry varchar(100),
    originlocationregion varchar(50),
    originlocationemail varchar(100),
    originlocationphone varchar(50),
    originlocationfax varchar(50),
    originlocationmetafields bytea,
    totallistshopamount numeric,
    totallistshopcurrencycode varchar(50),
    totallistpresentmentamount numeric,
    totallistpresentmentcurrencycode varchar(50),
    unitofmeasure varchar(50),
    ordered numeric,
    backordered numeric,
    canceled numeric,
    invoiced numeric,
    refunded numeric,
    returned numeric,
    shipped numeric,
    amount numeric,
    fulfillablequantity numeric,
    mindaystoexpiration numeric,
    accountid varchar(50),
    businessunitid varchar(50),
    customerid varchar(50),
    warehouseid varchar(50),
    createdby varchar(320),
    createddate timestamp,
    modifiedby varchar(320),
    modifieddate timestamp,
    timeinepoch int4,
    isdeleted bpchar,
    isarchived bpchar,
    isdefault bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    modifiedlocation varchar(200),
    updatedlocation varchar(200),
    transactionid varchar(50),
    itemid varchar(40),
    weight numeric,
    weightunitofmeasure varchar(50),
    received numeric,
    purchaseorderlinenumber varchar(50),
    exportpriceamount numeric,
    exportpricecurrencycode varchar(50),
    batchnumber varchar(50),
    unitweight numeric,
    unitweightunitofmeasure varchar(50),
    unitvolume numeric,
    unitvolumeunitofmeasure varchar(50),
    unitpriceamount numeric,
    unitpricecurrencycode varchar(50),
    unittaxamount numeric,
    unittaxcurrencycode varchar(50),
    unitcostamount numeric,
    unitcostcurrencycode varchar(50),
    freightclass varchar(3),
    declaredvalueamount numeric,
    declaredvaluecurrencycode varchar(50),
    refundrequested numeric,
    consigneesku varchar(256),
    serialnumber varchar(256),
	CONSTRAINT fulfillmentorderdetails_id PRIMARY KEY (id)
);","Fulfillment details for individual order line items including quantities fulfilled, inventory allocation, and processing status. Links order details to actual fulfillment operations and inventory movements."
orders.fulfillmentorderdetailtaxamounts,"CREATE TABLE orders.fulfillmentorderdetailtaxamounts (
    id varchar(40) NOT NULL,
    fulfillmentorderdetailid varchar(40),
    accountid varchar(50),
    businessunitid varchar(50),
    customerid varchar(50),
    warehouseid varchar(50),
    vertextaxcodes bytea,
    orderdetailtax numeric,
    amountcurrencycode varchar(50),
    beforediscountamount numeric,
    beforediscountcurrencycode varchar(50),
    canceledamount numeric,
    canceledcurrencycode varchar(50),
    invoicedamount numeric,
    invoicedcurrencycode varchar(50),
    percentamount numeric,
    percentcurrencycode varchar(50),
    refundedamount numeric,
    refundedcurrencycode varchar(50),
    createdby varchar(320),
    createddate timestamp,
    modifiedby varchar(320),
    modifieddate timestamp,
    timeinepoch int4,
    isdeleted bpchar,
    isarchived bpchar,
    isdefault bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    modifiedlocation varchar(200),
    updatedlocation varchar(200),
	CONSTRAINT fulfillmentorderdetailtaxamounts_id PRIMARY KEY (id)
);","Contains a summary of various tax amounts calculated for a specific item (order detail) in a fulfillment. It tracks the total tax before discounts, as well as the invoiced, canceled, and refunded tax amounts for the line item."
orders.fulfillmentorderdetailtaxes,"CREATE TABLE orders.fulfillmentorderdetailtaxes (
    id varchar(40) NOT NULL,
    fulfillmentorderdetailid varchar(40),
    accountid varchar(50),
    businessunitid varchar(50),
    customerid varchar(50),
    warehouseid varchar(50),
    name varchar(256),
    type varchar(50),
    taxamount numeric,
    taxcurrencycode varchar(50),
    presentmentamount numeric,
    presentmentcurrencycode varchar(50),
    baseamount numeric,
    basecurrencycode varchar(50),
    rates bytea,
    createdby varchar(320),
    createddate timestamp,
    modifiedby varchar(320),
    modifieddate timestamp,
    timeinepoch int4,
    isdeleted bpchar,
    isarchived bpchar,
    isdefault bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    modifiedlocation varchar(200),
    updatedlocation varchar(200),
	CONSTRAINT fulfillmentorderdetailtaxes_id PRIMARY KEY (id)
);","Provides a granular breakdown of each individual tax applied to a specific item (order detail) in a fulfillment. For each tax, it stores the name, type, rate, base amount, and the final calculated tax amount."
orders.fulfillments,"CREATE TABLE orders.fulfillments (
    id varchar(40) NOT NULL,
    orderid varchar(40) NOT NULL,
    sourcefulfillmentid varchar(50),
    sourcefulfillmentnumber varchar(50),
    fulfillmentnumber varchar(50),
    sourceorderid varchar(50),
    routestatus varchar(50),
    routedto varchar(50),
    orderstatus varchar(30),
    fulfillmentstatus varchar(50),
    shipfromaddresstype varchar(50),
    shipfromidentifyingnumber varchar(50),
    shipfromabbreviation varchar(50),
    shipfromcompany varchar(50),
    shipfromcontactfullname varchar(256),
    shipfromcontactfirstname varchar(50),
    shipfromcontactlastname varchar(50),
    shipfromcontactmiddlename varchar(50),
    shipfromcontactprefix varchar(50),
    shipfromcontactsuffix varchar(50),
    shipfromaddresslines bytea,
    shipfromaddressline1 varchar(100),
    shipfromaddressline2 varchar(100),
    shipfromaddressline3 varchar(100),
    shipfromcity varchar(50),
    shipfromstate varchar(50),
    shipfrompostalcode varchar(50),
    shipfromcountryid varchar(50),
    shipfromcountry varchar(100),
    shipfromregion varchar(50),
    shipfromemail varchar(320),
    shipfromphone varchar(50),
    shipfromfax varchar(50),
    shipfrommetafields bytea,
    shiptoaddresstype varchar(50),
    shiptoidentifyingnumber varchar(50),
    shiptoabbreviation varchar(50),
    shiptocompany varchar(50),
    shiptocontactfullname varchar(256),
    shiptocontactfirstname varchar(50),
    shiptocontactlastname varchar(50),
    shiptocontactmiddlename varchar(50),
    shiptocontactprefix varchar(50),
    shiptocontactsuffix varchar(50),
    shiptoaddresslines bytea,
    shiptoaddressline1 varchar(100),
    shiptoaddressline2 varchar(100),
    shiptoaddressline3 varchar(100),
    shiptocity varchar(50),
    shiptostate varchar(50),
    shiptopostalcode varchar(50),
    shiptocountryid varchar(50),
    shiptocountry varchar(100),
    shiptoregion varchar(50),
    shiptoemail varchar(320),
    shiptophone varchar(50),
    shiptofax varchar(50),
    shiptometafields bytea,
    accountid varchar(50),
    businessunitid varchar(50),
    customerid varchar(50),
    warehouseid varchar(50),
    createdby varchar(320),
    createddate timestamp,
    modifiedby varchar(320),
    modifieddate timestamp,
    timeinepoch int4,
    isdeleted bpchar,
    isarchived bpchar,
    isdefault bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    modifiedlocation varchar(200),
    updatedlocation varchar(200),
	CONSTRAINT fulfillments_id PRIMARY KEY (id)
);","Order fulfillment records tracking the processing and completion of orders including status, tracking, and delivery information. Central table for managing order fulfillment lifecycle from processing to delivery."
orders.giftcertificates,"CREATE TABLE orders.giftcertificates (
    id varchar(40) NOT NULL,
    orderdetailid varchar(40),
    accountid varchar(50),
    businessunitid varchar(50),
    customerid varchar(50),
    warehouseid varchar(50),
    fullname varchar(256),
    firstname varchar(50),
    lastname varchar(50),
    middlename varchar(50),
    prefix varchar(50),
    suffix varchar(50),
    amount numeric,
    currencycode varchar(50),
    giftcertificatetype varchar(50),
    createdby varchar(320),
    createddate timestamp,
    modifiedby varchar(320),
    modifieddate timestamp,
    timeinepoch int4,
    isdeleted bpchar,
    isarchived bpchar,
    isdefault bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    modifiedlocation varchar(200),
    updatedlocation varchar(200),
	CONSTRAINT giftcertificates_id PRIMARY KEY (id)
);","Stores details for gift certificates that are purchased as line items within an order. It records the recipient's name, the certificate's value and currency, and its type, linking it to a specific order detail."
orders.giftwraps,"CREATE TABLE orders.giftwraps (
    id varchar(40) NOT NULL,
    orderdetailoptionid varchar(40),
    fullname varchar(256),
    firstname varchar(50),
    lastname varchar(50),
    middlename varchar(50),
    prefix varchar(50),
    suffix varchar(50),
    message varchar(1000),
    amount numeric,
    currencycode varchar(50),
    wrappingname varchar(50),
    accountid varchar(50),
    businessunitid varchar(50),
    customerid varchar(50),
    warehouseid varchar(50),
    createdby varchar(320),
    createddate timestamp,
    modifiedby varchar(320),
    modifieddate timestamp,
    timeinepoch int4,
    isdeleted bpchar,
    isarchived bpchar,
    isdefault bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    modifiedlocation varchar(200),
    updatedlocation varchar(200),
	CONSTRAINT giftwraps_id PRIMARY KEY (id)
);","Gift wrapping services and charges for orders including wrapping options, costs, and special instructions. Manages gift presentation services and associated charges for customer orders."
orders.handling,"CREATE TABLE orders.handling (
    id varchar(40) NOT NULL,
    shippingid varchar(40),
    additionalhandling bool,
    additionalhardcopydocumentation bool,
    carriermonitoring bool,
    chainofsignature bool,
    healthinsurance bool,
    largepackage bool,
    security bool,
    signaturerelease bool,
    unpack bool,
    accountid varchar(50),
    businessunitid varchar(50),
    customerid varchar(50),
    warehouseid varchar(50),
    createdby varchar(320),
    createddate timestamp,
    modifiedby varchar(320),
    modifieddate timestamp,
    timeinepoch int4,
    isdeleted bpchar,
    isarchived bpchar,
    isdefault bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    modifiedlocation varchar(200),
    updatedlocation varchar(200),
	CONSTRAINT handling_id PRIMARY KEY (id)
);","Specifies the special handling services required for a shipment. It contains a series of flags to indicate options such as additional handling, large package surcharge, security, signature requirements, or unpacking services."
orders.messages,"CREATE TABLE orders.messages (
    id varchar(40) NOT NULL,
    cartinformationid varchar(40),
    senderfullname varchar(256),
    senderfirstname varchar(50),
    senderlastname varchar(50),
    sendermiddlename varchar(50),
    senderprefix varchar(50),
    sendersuffix varchar(50),
    recipientfullname varchar(256),
    recipientfirstname varchar(50),
    recipientlastname varchar(50),
    recipientmiddlename varchar(50),
    recipientprefix varchar(50),
    recipientsuffix varchar(50),
    message varchar(1000),
    accountid varchar(50),
    businessunitid varchar(50),
    customerid varchar(50),
    warehouseid varchar(50),
    createdby varchar(320),
    createddate timestamp,
    modifiedby varchar(320),
    modifieddate timestamp,
    timeinepoch int4,
    isdeleted bpchar,
    isarchived bpchar,
    isdefault bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    modifiedlocation varchar(200),
    updatedlocation varchar(200),
	CONSTRAINT messages_id PRIMARY KEY (id)
);","Order-level messages and communications including customer notes, special instructions, and internal comments. Manages communication and messaging related to order processing and customer service."
orders.orderdetaildiscountamounts,"CREATE TABLE orders.orderdetaildiscountamounts (
    id varchar(40) NOT NULL,
    orderdetailid varchar(40),
    orderdetaildiscountamount numeric,
    orderdetaildiscountcurrencycode varchar(50),
    invoicedamount numeric,
    invoicedcurrencycode varchar(50),
    percentamount numeric,
    percentcurrencycode varchar(50),
    refundedamount numeric,
    refundedcurrencycode varchar(50),
    presentmentamount numeric,
    presentmentcurrencycode varchar(50),
    couponname varchar(50),
    couponcode varchar(50),
    target varchar(50),
    accountid varchar(50),
    businessunitid varchar(50),
    customerid varchar(50),
    warehouseid varchar(50),
    createdby varchar(320),
    createddate timestamp,
    modifiedby varchar(320),
    modifieddate timestamp,
    timeinepoch int4,
    isdeleted bpchar,
    isarchived bpchar,
    isdefault bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    modifiedlocation varchar(200),
    updatedlocation varchar(200),
	CONSTRAINT orderdetaildiscountamounts_id PRIMARY KEY (id)
);","Discount amounts applied to individual order line items including promotional discounts, coupons, and price adjustments. Tracks discount details and amounts for order detail-level pricing calculations."
orders.orderdetailmessages,"CREATE TABLE orders.orderdetailmessages (
    id varchar(40) NOT NULL,
    orderdetailid varchar(40),
    senderfullname varchar(256),
    senderfirstname varchar(50),
    senderlastname varchar(50),
    sendermiddlename varchar(50),
    senderprefix varchar(50),
    sendersuffix varchar(50),
    recipientfullname varchar(256),
    recipientfirstname varchar(50),
    recipientlastname varchar(50),
    recipientmiddlename varchar(50),
    recipientprefix varchar(50),
    recipientsuffix varchar(50),
    message varchar(1000),
    accountid varchar(50),
    businessunitid varchar(50),
    customerid varchar(50),
    warehouseid varchar(50),
    createdby varchar(320),
    createddate timestamp,
    modifiedby varchar(320),
    modifieddate timestamp,
    timeinepoch int4,
    isdeleted bpchar,
    isarchived bpchar,
    isdefault bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    modifiedlocation varchar(200),
    updatedlocation varchar(200),
	CONSTRAINT orderdetailmessages_id PRIMARY KEY (id)
);","Line item-specific messages and instructions including product customization notes, special handling requirements, and item-specific customer communications. Manages detailed messaging for individual order items."
orders.orderdetailoptions,"CREATE TABLE orders.orderdetailoptions (
    id varchar(40) NOT NULL,
    orderdetailid varchar(40),
    tariffconversion varchar(50),
    tariffunitofmeasure varchar(50),
    nodiscountflag varchar(50),
    isfreeshippingflag varchar(50),
    addedbypromotion bool,
    physicalordigitaltype varchar(50),
    taxable bool,
    pastatus varchar(50),
    giftcard bool,
    fulfillmentstatus varchar(50),
    fulfillmentservice varchar(50),
    htc varchar(50),
    backorder bool,
    rfautodisplay bool,
    allowmultipleitems bool,
    accumulatelinequantity bool,
    requiresshipping bool,
    isrefunded bool,
    optionsetid numeric,
    accountid varchar(50),
    businessunitid varchar(50),
    customerid varchar(50),
    warehouseid varchar(50),
    createdby varchar(320),
    createddate timestamp,
    modifiedby varchar(320),
    modifieddate timestamp,
    timeinepoch int4,
    isdeleted bpchar,
    isarchived bpchar,
    isdefault bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    modifiedlocation varchar(200),
    updatedlocation varchar(200),
    pallettype varchar(50),
    serialnumberrequired bool,
    allowrevivalreceiptallocation bool,
    segmentname varchar(256),
    effectiverank varchar(50),
    preorderitem varchar(50),
    isgift bool,
	CONSTRAINT orderdetailoptions_id PRIMARY KEY (id)
);","Product options and customizations for individual order line items including size, color, personalization, and configuration choices. Manages product variants and customization details for specific items."
orders.orderdetailpriceamounts,"CREATE TABLE orders.orderdetailpriceamounts (
    id varchar(40) NOT NULL,
    orderdetailid varchar(40),
    listpriceamount numeric,
    listpricecurrencycode varchar(50),
    presentmentamount numeric,
    presentmentcurrencycode varchar(50),
    salepriceamount numeric,
    salepricecurrencycode varchar(50),
    priceamount numeric,
    pricecurrencycode varchar(50),
    includingtaxamount numeric,
    includingtaxcurrencycode varchar(50),
    accountid varchar(50),
    businessunitid varchar(50),
    customerid varchar(50),
    warehouseid varchar(50),
    createdby varchar(320),
    createddate timestamp,
    modifiedby varchar(320),
    modifieddate timestamp,
    timeinepoch int4,
    isdeleted bpchar,
    isarchived bpchar,
    isdefault bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    modifiedlocation varchar(200),
    updatedlocation varchar(200),
	CONSTRAINT orderdetailpriceamounts_id PRIMARY KEY (id)
);","Detailed pricing information for order line items including unit prices, extended amounts, and currency details. Tracks pricing components for individual order items across different currencies and pricing models."
orders.orderdetailrowamounts,"CREATE TABLE orders.orderdetailrowamounts (
    id varchar(40) NOT NULL,
    orderdetailid varchar(40),
    invoicedamount numeric,
    invoicedcurrencycode varchar(50),
    totalamount numeric,
    totalcurrencycode varchar(50),
    totalincludingtaxamount numeric,
    totalincludingtaxcurrencycode varchar(50),
    accountid varchar(50),
    businessunitid varchar(50),
    customerid varchar(50),
    warehouseid varchar(50),
    createdby varchar(320),
    createddate timestamp,
    modifiedby varchar(320),
    modifieddate timestamp,
    timeinepoch int4,
    isdeleted bpchar,
    isarchived bpchar,
    isdefault bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    modifiedlocation varchar(200),
    updatedlocation varchar(200),
	CONSTRAINT orderdetailrowamounts_id PRIMARY KEY (id)
);","Row-level amount calculations for order line items including totals, discounts, and net amounts. Provides comprehensive financial breakdown for each order detail line with calculated totals and adjustments."
orders.orderdetails,"CREATE TABLE orders.orderdetails (
    id varchar(40) NOT NULL,
    orderid varchar(40) NOT NULL,
    sku varchar(256) NOT NULL,
    lotnumber varchar(256),
    totalcouponshopamount numeric,
    totalcouponshopcurrencycode varchar(50),
    totalcouponpresentmentamount numeric,
    totalcouponpresentmentcurrencycode varchar(50),
    subtotalshopamount numeric,
    subtotalshopcurrencycode varchar(50),
    subtotalpresentmentamount numeric,
    subtotalpresentmentcurrencycode varchar(50),
    subtotaltaxshopamount numeric,
    subtotaltaxshopcurrencycode varchar(50),
    subtotaltaxpresentmentamount numeric,
    subtotaltaxpresentmentcurrencycode varchar(50),
    totalsaleshopamount numeric,
    totalsaleshopcurrencycode varchar(50),
    totalsalepresentmentamount numeric,
    totalsalepresentmentcurrencycode varchar(50),
    discountnames bytea,
    discounts bytea,
    linenumber varchar(50),
    applineid varchar(50),
    purchaseordernumber varchar(50),
    inventorystatuscode varchar(50),
    inventorystatusindicator varchar(50),
    inventorystatusreceived varchar(50),
    inventorystatusto varchar(50),
    inventoryclasscode varchar(50),
    inventoryclassindicator varchar(50),
    toinventoryclass varchar(50),
    instruction varchar(1000),
    billofladingcomment varchar(1000),
    originlocationaddresstype varchar(50),
    originlocationidentifyingnumber varchar(50),
    originlocationabbreviation varchar(50),
    originlocationcompany varchar(50),
    originlocationcontactfullname varchar(256),
    originlocationcontactfirstname varchar(50),
    originlocationcontactlastname varchar(50),
    originlocationcontactmiddlename varchar(50),
    originlocationcontactprefix varchar(50),
    originlocationcontactsuffix varchar(50),
    originlocationaddresslines bytea,
    originlocationaddressline1 varchar(100),
    originlocationaddressline2 varchar(100),
    originlocationaddressline3 varchar(100),
    originlocationcity varchar(50),
    originlocationstate varchar(50),
    originlocationpostalcode varchar(50),
    originlocationcountryid varchar(50),
    originlocationcountry varchar(100),
    originlocationregion varchar(50),
    originlocationemail varchar(320),
    originlocationphone varchar(50),
    originlocationfax varchar(50),
    originlocationmetafields bytea,
    totallistshopamount numeric,
    totallistshopcurrencycode varchar(50),
    totallistpresentmentamount numeric,
    totallistpresentmentcurrencycode varchar(50),
    unitofmeasure varchar(50),
    ordered numeric,
    backordered numeric,
    canceled numeric,
    invoiced numeric,
    refunded numeric,
    returned numeric,
    shipped numeric,
    amount numeric,
    fulfillablequantity numeric,
    weight numeric,
    weightunitofmeasure varchar(50),
    transactionid varchar(50),
    mindaystoexpiration numeric,
    createdby varchar(320),
    createddate timestamp,
    modifiedby varchar(320),
    modifieddate timestamp,
    timeinepoch int4,
    isdeleted bpchar,
    isarchived bpchar,
    isdefault bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    modifiedlocation varchar(200),
    updatedlocation varchar(200),
    itemid varchar(40),
    received numeric,
    purchaseorderlinenumber varchar(50),
    exportpriceamount numeric,
    exportpricecurrencycode varchar(50),
    batchnumber varchar(50),
    unitweight numeric,
    unitweightunitofmeasure varchar(50),
    unitvolume numeric,
    unitvolumeunitofmeasure varchar(50),
    unitpriceamount numeric,
    unitpricecurrencycode varchar(50),
    unittaxamount numeric,
    unittaxcurrencycode varchar(50),
    unitcostamount numeric,
    unitcostcurrencycode varchar(50),
    freightclass varchar(3),
    declaredvalueamount numeric,
    declaredvaluecurrencycode varchar(50),
    refundrequested numeric,
    consigneesku varchar(256),
    serialnumber varchar(256),
    accountid varchar(50),
    businessunitid varchar(50),
    customerid varchar(50),
    warehouseid varchar(50),
	CONSTRAINT orderdetails_id PRIMARY KEY (id)
);","Order line item details containing product information, quantities, pricing, and fulfillment data. Core table for individual items within orders including SKUs, descriptions, costs, and inventory specifications."
orders.orderdetailtaxamounts,"CREATE TABLE orders.orderdetailtaxamounts (
    id varchar(40) NOT NULL,
    orderdetailid varchar(40),
    vertextaxcodes bytea,
    orderdetailtax numeric,
    amountcurrencycode varchar(50),
    beforediscountamount numeric,
    beforediscountcurrencycode varchar(50),
    canceledamount numeric,
    canceledcurrencycode varchar(50),
    invoicedamount numeric,
    invoicedcurrencycode varchar(50),
    percentamount numeric,
    percentcurrencycode varchar(50),
    refundedamount numeric,
    refundedcurrencycode varchar(50),
    accountid varchar(50),
    businessunitid varchar(50),
    customerid varchar(50),
    warehouseid varchar(50),
    createdby varchar(320),
    createddate timestamp,
    modifiedby varchar(320),
    modifieddate timestamp,
    timeinepoch int4,
    isdeleted bpchar,
    isarchived bpchar,
    isdefault bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    modifiedlocation varchar(200),
    updatedlocation varchar(200),
	CONSTRAINT orderdetailtaxamounts_id PRIMARY KEY (id)
);","Order detail tax amounts with currency information and financial breakdown. Tracks tax calculations for individual order line items including various currency representations and tax components."
orders.orderdetailtaxes,"CREATE TABLE orders.orderdetailtaxes (
    id varchar(40) NOT NULL,
    orderdetailid varchar(40),
    name varchar(256),
    type varchar(50),
    taxamount numeric,
    taxcurrencycode varchar(50),
    presentmentamount numeric,
    presentmentcurrencycode varchar(50),
    baseamount numeric,
    basecurrencycode varchar(50),
    rates bytea,
    accountid varchar(50),
    businessunitid varchar(50),
    customerid varchar(50),
    warehouseid varchar(50),
    createdby varchar(320),
    createddate timestamp,
    modifiedby varchar(320),
    modifieddate timestamp,
    timeinepoch int4,
    isdeleted bpchar,
    isarchived bpchar,
    isdefault bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    modifiedlocation varchar(200),
    updatedlocation varchar(200),
	CONSTRAINT orderdetailtaxes_id PRIMARY KEY (id)
);","Order detail tax information with rates and calculations. Provides detailed tax breakdown for specific shipping services and carrier charges including tax rates, amounts, and jurisdiction information."
orders.orderdiscountamounts,"CREATE TABLE orders.orderdiscountamounts (
    id varchar(40) NOT NULL,
    paymentinformationid varchar(40),
    description varchar(50),
    amount numeric,
    amountcurrencycode varchar(50),
    presentmentamount numeric,
    presentmentcurrencycode varchar(50),
    canceledamount numeric,
    canceledcurrencycode varchar(50),
    invoicedamount numeric,
    invoicedcurrencycode varchar(50),
    refundedamount numeric,
    refundedcurrencycode varchar(50),
    createdby varchar(320),
    createddate timestamp,
    modifiedby varchar(320),
    modifieddate timestamp,
    timeinepoch int4,
    isdeleted bpchar,
    isarchived bpchar,
    isdefault bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    modifiedlocation varchar(200),
    updatedlocation varchar(200),
    accountid varchar(50),
    businessunitid varchar(50),
    customerid varchar(50),
    warehouseid varchar(50),
	CONSTRAINT orderdiscountamounts_id PRIMARY KEY (id)
);","Order-level discount amounts including promotional codes, customer discounts, and order-wide price adjustments. Manages discounts applied to entire orders rather than individual line items."
orders.orderoptions,"CREATE TABLE orders.orderoptions (
    id varchar(40) NOT NULL,
    shippingid varchar(40),
    preplaneligibility bool,
    allowmultipleitems bool,
    trackimport bool,
    rfautodisplay bool,
    version varchar(50),
    replacementorder bool,
    explodedkitorder bool,
    createdby varchar(320),
    createddate timestamp,
    modifiedby varchar(320),
    modifieddate timestamp,
    timeinepoch int4,
    isdeleted bpchar,
    isarchived bpchar,
    isdefault bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    modifiedlocation varchar(200),
    updatedlocation varchar(200),
    exchangeorder bool,
    wmsorder bool,
    perishable bool,
    allowprebilling bool,
    multistop bool,
    kitorder bool,
    offerorname varchar(256),
    labeltrigger varchar(256),
    oracleorder varchar(256),
    packlisttrigger varchar(256),
    licenseplatetype varchar(50),
    orderclassification varchar(50),
    packlistid varchar(50),
    preorder varchar(50),
    carrierretention varchar(50),
    dropshipcode varchar(50),
    accountid varchar(50),
    businessunitid varchar(50),
    customerid varchar(50),
    warehouseid varchar(50),
	CONSTRAINT orderoptions_id PRIMARY KEY (id)
);","Order-level options and preferences including delivery preferences, packaging options, and service selections. Manages customer choices and preferences that apply to the entire order."
orders.orders,"CREATE TABLE orders.orders (
    id varchar(40) NOT NULL,
    ordertype varchar(30),
    ordernumber varchar(40),
    action varchar(40),
    enteredat timestamp,
    wmagent varchar(40),
    businessunitid varchar(40),
    accountid varchar(40),
    customerid varchar(50),
    warehouseid varchar(50),
    referencenumber varchar(30),
    purchaseordernumber varchar(30),
    billoflading varchar(30),
    appointmentnumber varchar(30),
    hawb varchar(30),
    pronumber varchar(30),
    vendor varchar(40),
    aosordernumber varchar(40),
    sourceorderid varchar(40),
    channel varchar(256),
    routestatus varchar(50),
    routedto varchar(50),
    createdby varchar(320),
    createddate timestamp,
    modifiedby varchar(320),
    modifieddate timestamp,
    timeinepoch int4,
    isdeleted bpchar,
    isarchived bpchar,
    isdefault bpchar,
    ebayorderid varchar(50),
    externalid varchar(50),
    externalmerchantid varchar(50),
    channelid numeric,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    modifiedlocation varchar(200),
    updatedlocation varchar(200),
    status varchar(30),
    globallocationnumber varchar(256),
    exportdeclarationformnumber varchar(256),
    departmentnumber varchar(256),
    departmentdescription varchar(256),
    channelnumber varchar(256),
    documentpurchaseordernumber varchar(30),
    purchaseorderedat timestamp,
	CONSTRAINT orders_id PRIMARY KEY (id)
);","Master order records containing core order information, reference numbers, and routing details. Central table for order management linking to order details, shipping, billing, and fulfillment processes."
orders.ordershippingamounts,"CREATE TABLE orders.ordershippingamounts (
    id varchar(40) NOT NULL,
    paymentinformationid varchar(40),
    description varchar(50),
    amount numeric,
    amountcurrencycode varchar(50),
    presentmentamount numeric,
    presentmentcurrencycode varchar(50),
    canceledamount numeric,
    canceledcurrencycode varchar(50),
    discountamount numeric,
    discountcurrencycode varchar(50),
    discounttaxcompensationamount numeric,
    discounttaxcompensationcurrencycode varchar(50),
    includingtaxamount numeric,
    includingtaxcurrencycode varchar(50),
    invoicedamount numeric,
    invoicedcurrencycode varchar(50),
    refundedamount numeric,
    refundedcurrencycode varchar(50),
    taxamount numeric,
    taxcurrencycode varchar(50),
    taxrefundedamount numeric,
    taxrefundedcurrencycode varchar(50),
    beforediscountamount numeric,
    beforediscountcurrencycode varchar(50),
    createdby varchar(320),
    createddate timestamp,
    modifiedby varchar(320),
    modifieddate timestamp,
    timeinepoch int4,
    isdeleted bpchar,
    isarchived bpchar,
    isdefault bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    modifiedlocation varchar(200),
    updatedlocation varchar(200),
    accountid varchar(50),
    businessunitid varchar(50),
    customerid varchar(50),
    warehouseid varchar(50),
	CONSTRAINT ordershippingamounts_id PRIMARY KEY (id)
);","Shipping cost amounts for orders including base shipping charges, discounts, taxes, and adjustments. Tracks shipping-related financial components for order pricing and billing calculations."
orders.ordershippingchargeamounts,"CREATE TABLE orders.ordershippingchargeamounts (
    id varchar(40) NOT NULL,
    paymentinformationid varchar(40),
    method varchar(50) NOT NULL,
    totalamount numeric,
    totalcurrencycode varchar(50),
    totaltaxamount numeric,
    totaltaxcurrencycode varchar(50),
    canceledamount numeric,
    canceledcurrencycode varchar(50),
    discountamount numeric,
    discountcurrencycode varchar(50),
    discounttaxcompensationamount numeric,
    discounttaxcompensationcurrencycode varchar(50),
    includingtaxamount numeric,
    includingtaxcurrencycode varchar(50),
    invoicedamount numeric,
    invoicedcurrencycode varchar(50),
    refundedamount numeric,
    refundedcurrencycode varchar(50),
    taxrefundedamount numeric,
    taxrefundedcurrencycode varchar(50),
    baseamount numeric,
    baseamountcurrencycode varchar(50),
    basecanceledamount numeric,
    basecanceledcurrencycode varchar(50),
    basediscountamount numeric,
    basediscountcurrencycode varchar(50),
    basediscounttaxcompensationamount numeric,
    basediscounttaxcompensationcurrencycode varchar(50),
    baseincludingtaxamount numeric,
    baseincludingtaxcurrencycode varchar(50),
    baseinvoicedamount numeric,
    baseinvoicedcurrencycode varchar(50),
    baserefundedamount numeric,
    baserefundedcurrencycode varchar(50),
    basetaxamount numeric,
    basetaxcurrencycode varchar(50),
    basetaxrefundedamount numeric,
    basetaxrefundedcurrencycode varchar(50),
    createdby varchar(320),
    createddate timestamp,
    modifiedby varchar(320),
    modifieddate timestamp,
    timeinepoch int4,
    isdeleted bpchar,
    isarchived bpchar,
    isdefault bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    modifiedlocation varchar(200),
    updatedlocation varchar(200),
    accountid varchar(50),
    businessunitid varchar(50),
    customerid varchar(50),
    warehouseid varchar(50),
	CONSTRAINT ordershippingchargeamounts_id PRIMARY KEY (id)
);","Detailed shipping charge breakdowns by method including carrier-specific charges, surcharges, and fee components. Provides granular shipping cost analysis for different shipping methods and carriers."
orders.ordersubtotalamounts,"CREATE TABLE orders.ordersubtotalamounts (
    id varchar(40) NOT NULL,
    paymentinformationid varchar(40),
    amount numeric,
    amountcurrencycode varchar(50),
    presentmentamount numeric,
    presentmentcurrencycode varchar(50),
    canceledamount numeric,
    canceledcurrencycode varchar(50),
    includingtaxamount numeric,
    includingtaxcurrencycode varchar(50),
    invoicedamount numeric,
    invoicedcurrencycode varchar(50),
    refundedamount numeric,
    refundedcurrencycode varchar(50),
    createdby varchar(320),
    createddate timestamp,
    modifiedby varchar(320),
    modifieddate timestamp,
    timeinepoch int4,
    isdeleted bpchar,
    isarchived bpchar,
    isdefault bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    modifiedlocation varchar(200),
    updatedlocation varchar(200),
    accountid varchar(50),
    businessunitid varchar(50),
    customerid varchar(50),
    warehouseid varchar(50),
	CONSTRAINT ordersubtotalamounts_id PRIMARY KEY (id)
);","Order subtotal calculations before taxes, shipping, and final adjustments. Provides intermediate totals for order pricing calculations and financial reporting across multiple currencies."
orders.ordertotalamounts,"CREATE TABLE orders.ordertotalamounts (
    id varchar(40) NOT NULL,
    paymentinformationid varchar(40),
    amount numeric,
    amountcurrencycode varchar(50),
    presentmentamount numeric,
    presentmentcurrencycode varchar(50),
    canceledamount numeric,
    canceledcurrencycode varchar(50),
    dueamount numeric,
    duecurrencycode varchar(50),
    invoicedamount numeric,
    invoicedcurrencycode varchar(50),
    offlinerefundedamount numeric,
    offlinerefundedcurrencycode varchar(50),
    onlinerefundedamount numeric,
    onlinerefundedcurrencycode varchar(50),
    paidamount numeric,
    paidcurrencycode varchar(50),
    refundedamount numeric,
    refundedcurrencycode varchar(50),
    storecreditamount numeric,
    storecreditcurrencycode varchar(50),
    giftcertificateamount numeric,
    giftcertificatecurrencycode varchar(50),
    coupondiscountamount numeric,
    coupondiscountcurrencycode varchar(50),
    createdby varchar(320),
    createddate timestamp,
    modifiedby varchar(320),
    modifieddate timestamp,
    timeinepoch int4,
    isdeleted bpchar,
    isarchived bpchar,
    isdefault bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    modifiedlocation varchar(200),
    updatedlocation varchar(200),
    accountid varchar(50),
    businessunitid varchar(50),
    customerid varchar(50),
    warehouseid varchar(50),
	CONSTRAINT ordertotalamounts_id PRIMARY KEY (id)
);","Final order total amounts including all charges, taxes, discounts, and payments. Comprehensive financial summary showing total due, paid amounts, refunds, and outstanding balances for complete order accounting."
orders.ordertotaltaxamounts,"CREATE TABLE orders.ordertotaltaxamounts (
    id varchar(40) NOT NULL,
    paymentinformationid varchar(40),
    amount numeric,
    amountcurrencycode varchar(50),
    presentmentamount numeric,
    presentmentcurrencycode varchar(50),
    canceledamount numeric,
    canceledcurrencycode varchar(50),
    invoicedamount numeric,
    invoicedcurrencycode varchar(50),
    refundedamount numeric,
    refundedcurrencycode varchar(50),
    createdby varchar(320),
    createddate timestamp,
    modifiedby varchar(320),
    modifieddate timestamp,
    timeinepoch int4,
    isdeleted bpchar,
    isarchived bpchar,
    isdefault bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    modifiedlocation varchar(200),
    updatedlocation varchar(200),
    accountid varchar(50),
    businessunitid varchar(50),
    customerid varchar(50),
    warehouseid varchar(50),
	CONSTRAINT ordertotaltaxamounts_id PRIMARY KEY (id)
);","Total tax amounts for complete orders including all applicable taxes, duties, and government charges. Aggregates all tax calculations for comprehensive order tax reporting and compliance."
orders.paymentinformation,"CREATE TABLE orders.paymentinformation (
    id varchar(40) NOT NULL,
    orderid varchar(40) NOT NULL,
    financialstatus varchar(50),
    locationid varchar(50),
    shopnumber int4,
    gatewaynames bytea,
    processingmethod varchar(50),
    taxesincluded bool,
    totaltipreceived numeric,
    totaldutiesshopamount numeric,
    totaldutiesshopcurrencycode varchar(50),
    totaldutiespresentmentamount numeric,
    totaldutiespresentmentcurrencycode varchar(50),
    lineitemtotalshopamount numeric,
    lineitemtotalshopcurrencycode varchar(50),
    lineitemtotalpresentmentamount numeric,
    lineitemtotalpresentmentcurrencycode varchar(50),
    ordershopamount numeric,
    ordershopcurrencycode varchar(50),
    orderpresentmentamount numeric,
    orderpresentmentcurrencycode varchar(50),
    giftwrapshopamount numeric,
    giftwrapshopcurrencycode varchar(50),
    giftwrappresentmentamount numeric,
    giftwrappresentmentcurrencycode varchar(50),
    shippingbeforediscountshopamount numeric,
    shippingbeforediscountshopcurrencycode varchar(50),
    shippingbeforediscountpresentmentamount numeric,
    shippingbeforediscountpresentmentcurrencycode varchar(50),
    handlingshopamount numeric,
    handlingshopcurrencycode varchar(50),
    handlingpresentmentamount numeric,
    handlingpresentmentcurrencycode varchar(50),
    discounttaxshopamount numeric,
    discounttaxshopcurrencycode varchar(50),
    discounttaxpresentmentamount numeric,
    discounttaxpresentmentcurrencycode varchar(50),
    discountshippingtaxshopamount numeric,
    discountshippingtaxshopcurrencycode varchar(50),
    discountshippingtaxpresentmentamount numeric,
    discountshippingtaxpresentmentcurrencycode varchar(50),
    negativeamount numeric,
    negativecurrencycode varchar(50),
    positiveamount numeric,
    positivecurrencycode varchar(50),
    createdby varchar(320),
    createddate timestamp,
    modifiedby varchar(320),
    modifieddate timestamp,
    timeinepoch int4,
    isdeleted bpchar,
    isarchived bpchar,
    isdefault bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    modifiedlocation varchar(200),
    updatedlocation varchar(200),
    taxregistrationnumber varchar(256),
    totaltipreceivedamount numeric,
    totaltipreceivedcurrencycode varchar(50),
    accountid varchar(50),
    businessunitid varchar(50),
    customerid varchar(50),
    warehouseid varchar(50),
	CONSTRAINT paymentinformation_id PRIMARY KEY (id)
);","Payment processing information for orders including financial status, payment methods, taxes, and amounts. Central table for managing payment details, gateways, and financial transactions related to orders."
orders.paymentinformationshippingchargetaxes,"CREATE TABLE orders.paymentinformationshippingchargetaxes (
    id varchar(40) NOT NULL,
    paymentinformationid varchar(40),
    name varchar(256),
    type varchar(50),
    amount numeric,
    amountcurrencycode varchar(50),
    presentmentamount numeric,
    presentmentcurrencycode varchar(50),
    baseamount numeric,
    baseamountcurrencycode varchar(50),
    rates bytea,
    createdby varchar(320),
    createddate timestamp,
    modifiedby varchar(320),
    modifieddate timestamp,
    timeinepoch int4,
    isdeleted bpchar,
    isarchived bpchar,
    isdefault bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    modifiedlocation varchar(200),
    updatedlocation varchar(200),
    accountid varchar(50),
    businessunitid varchar(50),
    customerid varchar(50),
    warehouseid varchar(50),
	CONSTRAINT paymentinformationshippingchargetaxes_id PRIMARY KEY (id)
);","Tax information for shipping charges within payment processing including tax rates, amounts, and calculation details. Links shipping tax calculations to payment processing and financial reporting."
orders.paymentinformationtaxes,"CREATE TABLE orders.paymentinformationtaxes (
    id varchar(40) NOT NULL,
    paymentinformationid varchar(40),
    name varchar(256),
    type varchar(50),
    amount numeric,
    amountcurrencycode varchar(50),
    presentmentamount numeric,
    presentmentcurrencycode varchar(50),
    baseamount numeric,
    baseamountcurrencycode varchar(50),
    rates bytea,
    createdby varchar(320),
    createddate timestamp,
    modifiedby varchar(320),
    modifieddate timestamp,
    timeinepoch int4,
    isdeleted bpchar,
    isarchived bpchar,
    isdefault bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    modifiedlocation varchar(200),
    updatedlocation varchar(200),
    accountid varchar(50),
    businessunitid varchar(50),
    customerid varchar(50),
    warehouseid varchar(50),
	CONSTRAINT paymentinformationtaxes_id PRIMARY KEY (id)
);","Tax details associated with payment processing including all applicable taxes, rates, and amounts. Comprehensive tax information for payment reconciliation and financial reporting requirements."
orders.pickupoptions,"CREATE TABLE orders.pickupoptions (
    id varchar(40) NOT NULL,
    shippingid varchar(40),
    helperpickup bool,
    forkliftpickup bool,
    insidepickup bool,
    liftgatepickup bool,
    palletjackpickup bool,
    stairpickup bool,
    createdby varchar(320),
    createddate timestamp,
    modifiedby varchar(320),
    modifieddate timestamp,
    timeinepoch int4,
    isdeleted bpchar,
    isarchived bpchar,
    isdefault bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    modifiedlocation varchar(200),
    updatedlocation varchar(200),
    pickupreference varchar(50),
    pickupstart timestamp,
    pickupend timestamp,
    customerpickup bool,
    scheduledpickup bool,
    accountid varchar(50),
    businessunitid varchar(50),
    customerid varchar(50),
    warehouseid varchar(50),
	CONSTRAINT pickupoptions_id PRIMARY KEY (id)
);","Pickup service options and requirements for orders including equipment needs, scheduling, and special handling requirements. Manages customer pickup preferences and carrier pickup service configurations."
orders.shipments,"CREATE TABLE orders.shipments (
    id varchar(40) NOT NULL,
    expecteddeliverydate timestamp,
    timeintransit numeric,
    totalcostamount numeric,
    totalcostcurrencycode varchar(50),
    carrier varchar(50),
    service varchar(50),
    shipdate timestamp,
    totalsellcostamount numeric,
    totalsellcostcurrencycode varchar(50),
    createdby varchar(320),
    createddate timestamp,
    modifiedby varchar(320),
    modifieddate timestamp,
    timeinepoch int4,
    isdeleted bpchar,
    isarchived bpchar,
    isdefault bpchar,
    fulfillmentid varchar(40),
    isreturn bool,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    modifiedlocation varchar(200),
    updatedlocation varchar(200),
    deliverydate timestamp,
    accountid varchar(50),
    businessunitid varchar(50),
    customerid varchar(50),
    warehouseid varchar(50),
	CONSTRAINT shipments_id PRIMARY KEY (id)
);","Order shipment records tracking carrier information, costs, delivery dates, and fulfillment details. Links orders to actual shipments with carrier services, tracking, and delivery management."
orders.shipping,"CREATE TABLE orders.shipping (
    id varchar(40) NOT NULL,
    orderid varchar(40),
    shipdate timestamp,
    carrier varchar(50),
    service varchar(50),
    trackingnumber varchar(40),
    shipmentrequesteddate timestamp,
    shipnolaterthan timestamp,
    shipnotbefore timestamp,
    mailingtype varchar(50),
    shipfromaddresstype varchar(50),
    shipfromidentifyingnumber varchar(50),
    shipfromabbreviation varchar(50),
    shipfromcompany varchar(50),
    shipfromcontactfullname varchar(256),
    shipfromcontactfirstname varchar(50),
    shipfromcontactlastname varchar(50),
    shipfromcontactmiddlename varchar(50),
    shipfromcontactprefix varchar(50),
    shipfromcontactsuffix varchar(50),
    shipfromaddresslines bytea,
    shipfromaddressline1 varchar(100),
    shipfromaddressline2 varchar(100),
    shipfromaddressline3 varchar(100),
    shipfromcity varchar(50),
    shipfromstate varchar(50),
    shipfrompostalcode varchar(50),
    shipfromcountryid varchar(50),
    shipfromcountry varchar(100),
    shipfromregion varchar(50),
    shipfromemail varchar(320),
    shipfromphone varchar(50),
    shipfromfax varchar(50),
    shipfrommetafields bytea,
    shiptoaddresstype varchar(50),
    shiptoidentifyingnumber varchar(50),
    shiptoabbreviation varchar(50),
    shiptocompany varchar(50),
    shiptocontactfullname varchar(256),
    shiptocontactfirstname varchar(50),
    shiptocontactlastname varchar(50),
    shiptocontactmiddlename varchar(50),
    shiptocontactprefix varchar(50),
    shiptocontactsuffix varchar(50),
    shiptoaddresslines bytea,
    shiptoaddressline1 varchar(100),
    shiptoaddressline2 varchar(100),
    shiptoaddressline3 varchar(100),
    shiptocity varchar(50),
    shiptostate varchar(50),
    shiptopostalcode varchar(50),
    shiptocountryid varchar(50),
    shiptocountry varchar(100),
    shiptoregion varchar(50),
    shiptoemail varchar(320),
    shiptophone varchar(50),
    shiptofax varchar(50),
    shiptometafields bytea,
    fromfacilityaddresstype varchar(50),
    fromfacilityidentifyingnumber varchar(50),
    fromfacilityabbreviation varchar(50),
    fromfacilitycompany varchar(50),
    fromfacilitycontactfullname varchar(256),
    fromfacilitycontactfirstname varchar(50),
    fromfacilitycontactlastname varchar(50),
    fromfacilitycontactmiddlename varchar(50),
    fromfacilitycontactprefix varchar(50),
    fromfacilitycontactsuffix varchar(50),
    fromfacilityaddresslines bytea,
    fromfacilityaddressline1 varchar(100),
    fromfacilityaddressline2 varchar(100),
    fromfacilityaddressline3 varchar(100),
    fromfacilitycity varchar(50),
    fromfacilitystate varchar(50),
    fromfacilitypostalcode varchar(50),
    fromfacilitycountryid varchar(50),
    fromfacilitycountry varchar(100),
    fromfacilityregion varchar(50),
    fromfacilityemail varchar(320),
    fromfacilityphone varchar(50),
    fromfacilityfax varchar(50),
    fromfacilitymetafields bytea,
    finaldestinationaddresstype varchar(50),
    finaldestinationidentifyingnumber varchar(50),
    finaldestinationabbreviation varchar(50),
    finaldestinationcompany varchar(50),
    finaldestinationcontactfullname varchar(256),
    finaldestinationcontactfirstname varchar(50),
    finaldestinationcontactlastname varchar(50),
    finaldestinationcontactmiddlename varchar(50),
    finaldestinationcontactprefix varchar(50),
    finaldestinationcontactsuffix varchar(50),
    finaldestinationaddresslines bytea,
    finaldestinationaddressline1 varchar(100),
    finaldestinationaddressline2 varchar(100),
    finaldestinationaddressline3 varchar(100),
    finaldestinationcity varchar(50),
    finaldestinationstate varchar(50),
    finaldestinationpostalcode varchar(50),
    finaldestinationcountryid varchar(50),
    finaldestinationcountry varchar(100),
    finaldestinationregion varchar(50),
    finaldestinationemail varchar(320),
    finaldestinationphone varchar(50),
    finaldestinationfax varchar(50),
    finaldestinationmetafields bytea,
    fulfillmentstatus varchar(50),
    canshippartiallyflag varchar(50),
    canshippartiallyitemflag varchar(50),
    fulfillments bytea,
    createdby varchar(320),
    createddate timestamp,
    modifiedby varchar(320),
    modifieddate timestamp,
    timeinepoch int4,
    isdeleted bpchar,
    isarchived bpchar,
    isdefault bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    modifiedlocation varchar(200),
    updatedlocation varchar(200),
    shiptype varchar(20),
    accountid varchar(50),
    businessunitid varchar(50),
    customerid varchar(50),
    warehouseid varchar(50),
	CONSTRAINT shipping_id PRIMARY KEY (id)
);","Comprehensive shipping information for orders including carrier details, service levels, addresses, and delivery requirements. Central table for managing order shipping with multiple address types and carrier configurations."
orders.shippingchargetaxes,"CREATE TABLE orders.shippingchargetaxes (
    id varchar(40) NOT NULL,
    ordershippingchargeamountid varchar(40),
    name varchar(256),
    type varchar(50),
    amount numeric,
    amountcurrencycode varchar(50),
    presentmentamount numeric,
    presentmentcurrencycode varchar(50),
    baseamount numeric,
    baseamountcurrencycode varchar(50),
    rates bytea,
    createdby varchar(320),
    createddate timestamp,
    modifiedby varchar(320),
    modifieddate timestamp,
    timeinepoch int4,
    isdeleted bpchar,
    isarchived bpchar,
    isdefault bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    modifiedlocation varchar(200),
    updatedlocation varchar(200),
    accountid varchar(50),
    businessunitid varchar(50),
    customerid varchar(50),
    warehouseid varchar(50),
	CONSTRAINT shippingchargetaxes_id PRIMARY KEY (id)
);","Tax calculations applied to shipping charges including sales tax, VAT, and other shipping-related taxes. Manages tax compliance for shipping costs across different jurisdictions and tax authorities."
orders.shippinglines,"CREATE TABLE orders.shippinglines (
    id varchar(40) NOT NULL,
    shippingid varchar(40),
    carrieridentifier varchar(50),
    code varchar(50),
    source varchar(50),
    title varchar(50),
    instructions varchar(256),
    carrieraccount varchar(50),
    shopamount numeric,
    shopcurrencycode varchar(50),
    presentmentamount numeric,
    presentmentcurrencycode varchar(50),
    discountedshopamount numeric,
    discountedshopcurrencycode varchar(50),
    discountedpresentmentamount numeric,
    discountedpresentmentcurrencycode varchar(50),
    sealnumber varchar(256),
    shippingzoneid numeric,
    shippingzonename varchar(50),
    createdby varchar(320),
    createddate timestamp,
    modifiedby varchar(320),
    modifieddate timestamp,
    timeinepoch int4,
    isdeleted bpchar,
    isarchived bpchar,
    isdefault bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    modifiedlocation varchar(200),
    updatedlocation varchar(200),
    revenueamount numeric,
    revenuecurrencycode varchar(50),
    originalbudgetedamount numeric,
    originalbudgetedcurrencycode varchar(50),
    budgetedamount numeric,
    budgetedcurrencycode varchar(50),
    actualamount numeric,
    actualcurrencycode varchar(50),
    accountid varchar(50),
    businessunitid varchar(50),
    customerid varchar(50),
    warehouseid varchar(50),
	CONSTRAINT shippinglines_id PRIMARY KEY (id)
);","Individual shipping service line items with carrier details, costs, and service specifications. Breaks down shipping into specific service components with pricing and carrier account information."
orders.shippinglinetaxes,"CREATE TABLE orders.shippinglinetaxes (
    id varchar(40) NOT NULL,
    shippinglineid varchar(40),
    taxamount numeric,
    taxcurrencycode varchar(50),
    presentmentamount numeric,
    presentmentcurrencycode varchar(50),
    createdby varchar(320),
    createddate timestamp,
    modifiedby varchar(320),
    modifieddate timestamp,
    timeinepoch int4,
    isdeleted bpchar,
    isarchived bpchar,
    isdefault bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    modifiedlocation varchar(200),
    updatedlocation varchar(200),
    accountid varchar(50),
    businessunitid varchar(50),
    customerid varchar(50),
    warehouseid varchar(50),
	CONSTRAINT shippinglinetaxes_id PRIMARY KEY (id)
);","Tax details for individual shipping line items including tax rates, amounts, and jurisdiction information. Provides detailed tax breakdown for specific shipping services and carrier charges."
orders.tags,"CREATE TABLE orders.tags (
    id varchar(40) NOT NULL,
    entityid varchar(40) NOT NULL,
    entityname varchar(256) NOT NULL,
    attributename varchar(256),
    inputtemplate varchar(1000),
    outputtemplate varchar(1000),
    createdby varchar(320),
    createddate timestamp,
    modifiedby varchar(320),
    modifieddate timestamp,
    timeinepoch int4,
    isdeleted bpchar,
    isarchived bpchar,
    isdefault bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    modifiedlocation varchar(200),
    updatedlocation varchar(200),
    hashkey varchar(256),
    accountid varchar(50),
    businessunitid varchar(50),
    customerid varchar(50),
    warehouseid varchar(50),
	CONSTRAINT tags_id PRIMARY KEY (id)
);","Order classification tags and labels for categorization, reporting, and processing rules. Enables flexible order categorization for business intelligence, routing, and automated processing workflows."
orders.tagvalues,"CREATE TABLE orders.tagvalues (
    id varchar(40) NOT NULL,
    tagid varchar(40) NOT NULL,
    entityid varchar(40) NOT NULL,
    entityname varchar(256) NOT NULL,
    attributename varchar(256),
    value varchar(256) NOT NULL,
    createdby varchar(320),
    createddate timestamp,
    modifiedby varchar(320),
    modifieddate timestamp,
    timeinepoch int4,
    isdeleted bpchar,
    isarchived bpchar,
    isdefault bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    modifiedlocation varchar(200),
    updatedlocation varchar(200),
    hashkey varchar(256),
    accountid varchar(50),
    businessunitid varchar(50),
    customerid varchar(50),
    warehouseid varchar(50),
	CONSTRAINT tagvalues_id PRIMARY KEY (id, id, id)
);","Tag value assignments linking specific tag values to orders for detailed classification and filtering. Provides flexible metadata association for orders enabling complex categorization and business rules."
picking.pickingplate,"CREATE TABLE picking.pickingplate (
    id varchar(40) NOT NULL,
    warehouseid varchar(40),
    accountid varchar(40),
    businessunitid varchar(40),
    warehousecode varchar(50),
    customerid varchar(40),
    accountcode varchar(50),
    businessunitcode varchar(50),
    platenumber varchar(40),
    parentlicenseplatenumber varchar(50),
    productgroup varchar(50),
    itemcode varchar(50),
    locationcode varchar(50),
    unitofmeasuredescription varchar(50),
    ordernumber varchar(50),
    fulfillmentnumber varchar(50),
    orderprioritycode numeric,
    statuscodedescription varchar(50),
    typecodedescription varchar(50),
    inventorystatusdescription varchar(50),
    inventoryclassdescription varchar(50),
    pickcartcode varchar(50),
    locationtypedescription varchar(50),
    lastsyncdate timestamp,
    fromplatenumber varchar(40),
    fromplateid varchar(50),
    parentid varchar(40),
    taskid varchar(40),
    itemid varchar(40),
    lotnumber varchar(40),
    locationid varchar(40),
    quantity numeric,
    length numeric,
    width numeric,
    height numeric,
    tierweight numeric,
    weight numeric,
    unitofmeasurecode varchar(4),
    orderid varchar(40),
    status varchar(4),
    type varchar(4),
    dropsequence numeric,
    loadnumber varchar(40),
    stopnumber varchar(40),
    fulfillmentid varchar(40),
    inventorystatuscode varchar(50),
    inventoryclasscode varchar(50),
    trackingnumber varchar(40),
    expirationdate timestamp,
    manufacturedate timestamp,
    vendorcode varchar(50),
    owner varchar(40),
    countryoforigin varchar(40),
    serialnumber varchar(40),
    locationtypecode varchar(50),
    pickingactivityid varchar(40),
    cartposition numeric,
    frompickingplateid varchar(40),
    containertypecode varchar(50),
    containersequence numeric,
    createdby varchar(320),
    createddate timestamp,
    modifiedby varchar(320),
    modifieddate timestamp,
    timeinepoch numeric,
    isdeleted bpchar,
    isarchived bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    updatedlocation varchar(200),
    taskidentifier varchar(50),
    vendorid varchar(50),
    containertypedescription varchar(50),
	CONSTRAINT pickingplate_id PRIMARY KEY (id)
);","Represents a picking plate used in warehouse operations, tracking items, quantities, and their associated details during the picking process. It details the physical plate, its contents, origin, destination, and status within a picking task."
picking.pickingplateitem,"CREATE TABLE picking.pickingplateitem (
    id varchar(40) NOT NULL,
    plateid varchar(40),
    subtaskid varchar(40),
    taskid varchar(40),
    picktaskidentifier varchar(50),
    productgroup varchar(50),
    itemcode varchar(50),
    itemdescription varchar(50),
    accountcode varchar(50),
    businessunitcode varchar(50),
    ordernumber varchar(50),
    fromlocationcode varchar(50),
    fromlocationtype varchar(4),
    fromlocationtypedescription varchar(50),
    fulfillmentnumber varchar(50),
    unitofmeasuredescription varchar(50),
    inventorystatusdescription varchar(50),
    inventoryclassdescription varchar(50),
    countryoforigindescription varchar(50),
    itemid varchar(40),
    warehouseid varchar(50),
    accountid varchar(40),
    businessunitid varchar(40),
    customerid varchar(40),
    pickquantity numeric,
    fromplatenumber varchar(40),
    serialnumber varchar(80),
    lotnumber varchar(80),
    orderid varchar(40),
    weight numeric,
    fromlocationid varchar(40),
    fromplateid varchar(50),
    unitofmeasure varchar(40),
    fulfillmentid varchar(50),
    inventorystatuscode varchar(10),
    inventoryclasscode varchar(10),
    expirationdate timestamp,
    manufacturedate timestamp,
    vendorcode varchar(50),
    ownercode varchar(50),
    countryoforigincode varchar(50),
    createdby varchar(320),
    createddate timestamp,
    modifiedby varchar(320),
    modifieddate timestamp,
    timeinepoch numeric,
    isdeleted bpchar,
    isarchived bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    updatedlocation varchar(200),
    lastsyncdate timestamp,
    platenumber varchar(50),
	CONSTRAINT pickingplateitem_id PRIMARY KEY (id)
);","Stores details about individual items on a picking plate, including product codes, descriptions, quantities, and their origin locations. This table links specific items to the picking plate they are associated with during the picking process."
picking.pickingplatetags,"CREATE TABLE picking.pickingplatetags (
    id varchar(50) NOT NULL,
    pickingplateid varchar(50),
    tagtype varchar(20),
    tagvalue varchar(50),
    createdby varchar(320),
    createddate timestamp,
    modifiedby varchar(320),
    modifieddate timestamp,
    timeinepoch numeric,
    isdeleted bpchar,
    isarchived bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    updatedlocation varchar(200),
    lastsyncdate timestamp,
	CONSTRAINT pickingplatetags_id PRIMARY KEY (id)
);","Contains tags or labels associated with picking plates, categorizing them by type and value for enhanced organization and tracking. This table provides additional metadata for picking plates."
picking.picklist,"CREATE TABLE picking.picklist (
    id varchar(40) NOT NULL,
    picklistidentifier varchar(40),
    picktaskidentifier varchar(50),
    subtaskid varchar(40),
    picktaskid varchar(40),
    warehouseid varchar(40),
    accountcode varchar(20),
    warehousecode varchar(20),
    statusdescription varchar(20),
    itemdescription varchar(50),
    vendorcode varchar(20),
    fromlocationcode varchar(20),
    locationtype varchar(4),
    locationtypedescription varchar(20),
    stagelocationcode varchar(20),
    shippingtypedescription varchar(50),
    picktotypedescription varchar(20),
    lastsyncdate timestamp,
    accountid varchar(40),
    businessunitid varchar(40),
    customerid varchar(40),
    itemid varchar(40),
    fromlicenseplate varchar(40),
    pickunitofmeasure varchar(4),
    orderlot varchar(80),
    orderitemid varchar(40),
    orderid varchar(40),
    ordernumber varchar(20),
    fulfillmentid varchar(40),
    fulfillmentnumber varchar(40),
    pickingplatenumber varchar(40),
    shippingtype varchar(20),
    pickquantity numeric,
    fromlocationid varchar(40),
    stagelocationid varchar(40),
    picktotype varchar(20),
    loadnumber varchar(40),
    stopnumber varchar(40),
    cartonid varchar(40),
    cartonsequence numeric,
    batchid varchar(40),
    cartposition numeric,
    minimumdaystoexpiration numeric,
    labelcount numeric,
    capturecount numeric,
    oldestexpirationdate timestamp,
    isoldestexpirationdateenabled bpchar,
    isfelenabled bpchar,
    isgs1barcoded bpchar,
    ispartialcapture bpchar,
    isltlfelorder bpchar,
    isconnectshiporder bpchar,
    isfullpick bpchar,
    isadditionalcapturerequired bpchar,
    needcontainerconfirmation bpchar,
    needlocationconfirmation bpchar,
    needitemconfirmation bpchar,
    needcountryoforiginconfirmation bpchar,
    needlicenseplateconfirmation bpchar,
    picksequence numeric,
    pickingzone varchar(40),
    status numeric,
    basepickquantity numeric,
    baseunitofmeasure varchar(4),
    aisle varchar(40),
    shortpickreasoncode varchar(40),
    createdby varchar(320),
    createddate timestamp,
    modifiedby varchar(320),
    modifieddate timestamp,
    timeinepoch numeric,
    isdeleted bpchar,
    isarchived bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    updatedlocation varchar(200),
    lotnumber varchar(80),
    fromlicenseplatenumber varchar(20),
    businessunitnumber varchar(20),
    customercode varchar(20),
    zcoordinate int4,
    xcoordinate int4,
    ycoordinate int4,
	CONSTRAINT picklist_id PRIMARY KEY (id)
);","Contains details for individual picklist items, including quantities, source and staging locations, and various flags for validation and confirmation during picking. This table is a core component for managing the specific actions required for each pick."
picking.picksettings,"CREATE TABLE picking.picksettings (
    id varchar(40) NOT NULL,
    accountid varchar(40),
    accountcode varchar(50),
    businessunitcode varchar(50),
    lastsyncdate timestamp,
    businessunitid varchar(40),
    customerid varchar(40),
    enableforceaudit bool,
    disableltlfellpoverride bool,
    entermindatetoexpire bool,
    enforceoldestexpirationdateatpickfront bool,
    isdisallowmasterfullpallet bool,
    allowmixlotmultipalletpick bool,
    pickbylinenumber bool,
    islockstagelocations bool,
    iscartontypestaged bool,
    isallowcartonalias bool,
    iscartontypeforfel bool,
    enforcevalidlot bool,
    isallowshortshipping bool,
    canskipaisle bool,
    createdby varchar(320),
    createddate timestamp,
    modifiedby varchar(320),
    modifieddate timestamp,
    timeinepoch numeric,
    isdeleted bpchar,
    isarchived bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    updatedlocation varchar(200),
    warehouseid varchar(40),
	CONSTRAINT picksettings_id PRIMARY KEY (id)
);","Defines various picking-related settings and configurations for a warehouse, such as audit requirements, expiration date enforcement, and rules for pallet and carton handling. This table controls the behavior and constraints of the picking process."
picking.picktask,"CREATE TABLE picking.picktask (
    id varchar(40) NOT NULL,
    taskidentifier varchar(40),
    statuscode numeric,
    previousstatuscode numeric,
    prioritycode numeric,
    warehouseid varchar(40),
    locationid varchar(40),
    accountid varchar(40),
    businessunitid varchar(40),
    customerid varchar(40),
    assigneduser varchar(40),
    orderid varchar(40),
    fulfillmentid varchar(40),
    waveid varchar(40),
    referencenumber numeric,
    equipmentprofileid varchar(40),
    picksequence numeric,
    activeuser varchar(40),
    batchid varchar(40),
    taskvolume numeric,
    parenttaskid varchar(50),
    worktype varchar(4),
    aisle varchar(40),
    previousprioritycode numeric,
    previoustaskid varchar(40),
    nexttaskid varchar(40),
    taskweight numeric,
    swaptaskid varchar(40),
    fixedstagelocationid varchar(50),
    createdby varchar(320),
    createddate timestamp,
    modifiedby varchar(320),
    modifieddate timestamp,
    statusdescription varchar(50),
    previousstatusdescription varchar(50),
    accountcode varchar(20),
    businessunitcode varchar(20),
    warehousecode varchar(20),
    prioritydescription varchar(50),
    worktypedescription varchar(50),
    nextlocationcode varchar(20),
    nextlocationtype varchar(4),
    zoneid varchar(50),
    areaid varchar(50),
    fixedstagelocationcode varchar(20),
    wavenumber varchar(20),
    wavereleaseddate timestamp,
    orderprioritycode numeric,
    fulfillmentnumber varchar(20),
    zonecode varchar(20),
    cartassigned varchar(50),
    cartassignedtype varchar(4),
    cartassignedstatus numeric,
    cartassignedtypedescription varchar(50),
    cartassignedstatusdescription varchar(50),
    lastsyncdate timestamp,
    timeinepoch numeric,
    isdeleted bpchar,
    isarchived bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    updatedlocation varchar(200),
    traceid varchar(40),
    spanid varchar(40),
    taskstarted timestamp,
    taskended timestamp,
    taskstartuser varchar(320),
    previousprioritydescription varchar(50),
    preassigneduser varchar(20),
    nextlocationid varchar(50),
    nextlocationsequence numeric,
    ordernumber varchar(20),
    aislecode varchar(20),
    totalvolume numeric,
    activityid varchar(50),
    parenttaskidentifier varchar(20),
	CONSTRAINT picktask_id PRIMARY KEY (id)
);","Central table for managing picking tasks in a warehouse, including task status, priority, assigned users, and associated order/fulfillment details. It orchestrates the overall picking workflow."
picking.picktaskerrors,"CREATE TABLE picking.picktaskerrors (
    id varchar(50) NOT NULL,
    picktaskid varchar(50),
    picktaskidentifier varchar(50),
    lastsyncdate timestamp,
    pickingplateid varchar(50),
    waveid varchar(50),
    fulfillmentid varchar(50),
    customerid varchar(40),
    errorcode varchar(15),
    errormessage varchar(10000),
    createdby varchar(320),
    createddate timestamp,
    modifiedby varchar(320),
    modifieddate timestamp,
    timeinepoch numeric,
    isdeleted bpchar,
    isarchived bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    updatedlocation varchar(200),
    warehouseid varchar(40),
    accountid varchar(40),
    businessunitid varchar(40),
	CONSTRAINT picktaskerrors_id PRIMARY KEY (id)
);","Records errors encountered during picking tasks, providing error codes and detailed messages to help diagnose and resolve issues in the picking process. This table is crucial for error tracking and operational improvement."
picking.picktasktags,"CREATE TABLE picking.picktasktags (
    id varchar(50) NOT NULL,
    picktaskid varchar(50),
    tagtype varchar(20),
    tagvalue varchar(50),
    createdby varchar(320),
    createddate timestamp,
    modifiedby varchar(320),
    modifieddate timestamp,
    timeinepoch numeric,
    isdeleted bpchar,
    isarchived bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    updatedlocation varchar(200),
    lastsyncdate timestamp,
	CONSTRAINT picktasktags_id PRIMARY KEY (id)
);","Stores tags associated with picking tasks, allowing for flexible categorization and filtering of tasks based on various criteria. This table enhances the ability to manage and group picking tasks with custom attributes."
picking.subtask,"CREATE TABLE picking.subtask (
    id varchar(40) NOT NULL,
    picktaskid varchar(40),
    fulfillmentnumber varchar(50),
    picktaskidentifier varchar(50),
    picklistid varchar(50),
    picklistidentifier varchar(50),
    lotnumber varchar(20),
    statusdescription varchar(50),
    itemcode varchar(20),
    itemdescription varchar(50),
    unitofmeasuredescription varchar(50),
    confirmedpickuomdescription varchar(50),
    pickingplateid varchar(50),
    fromlocationcode varchar(20),
    fromlocationtype varchar(4),
    fromlocationsequence varchar(50),
    pickingzoneid varchar(50),
    fromlocationtypedescription varchar(50),
    pickingzonecode varchar(20),
    pickstatuscodedescription varchar(50),
    lastsyncdate timestamp,
    warehouseid varchar(40),
    accountid varchar(40),
    businessunitid varchar(40),
    customerid varchar(50),
    fromlocationid varchar(40),
    fromplatenumber varchar(40),
    itemid varchar(40),
    pickquantity numeric,
    pickunitofmeasure varchar(4),
    pickingplatenumber varchar(40),
    tolocationid varchar(40),
    statuscode varchar(4),
    orderid varchar(40),
    fulfillmentid varchar(40),
    createdby varchar(320),
    createddate timestamp,
    modifiedby varchar(320),
    modifieddate timestamp,
    timeinepoch numeric,
    isdeleted bpchar,
    isarchived bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    updatedlocation varchar(200),
    taskstarted timestamp,
    taskended timestamp,
    taskstartuser varchar(320),
    confirmedpickuom varchar(20),
    pickstatuscode numeric,
    pickcompleted timestamp,
	CONSTRAINT subtask_id PRIMARY KEY (id)
);","Breaks down picking tasks into smaller, manageable units, detailing specific items to be picked, their quantities, source locations, and associated picking plates. This table enables granular tracking of the picking process."
picking.tagvalues,"CREATE TABLE picking.tagvalues (
    id varchar(40) NOT NULL,
    tagid varchar(40),
    entityid varchar(40),
    entityname varchar(256),
    attributename varchar(256),
    warehouseid varchar(40),
    accountid varchar(40),
    businessunitid varchar(40),
    customerid varchar(50),
    value varchar(256),
    createdby varchar(320),
    lastsyncdate timestamp,
    createddate timestamp,
    modifiedby varchar(320),
    modifieddate timestamp,
    timeinepoch int4,
    isdeleted bpchar,
    isarchived bpchar,
    isdefault bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    updatedlocation varchar(200),
CONSTRAINT tagvalues_id PRIMARY KEY (id, id, id)
);","Stores tag values associated with various entities, including information about the tag itself, the entity it's linked to, and audit details."
returns.return,"CREATE TABLE returns.return (
    id varchar(40) NOT NULL,
    accountid varchar(40),
    accountcode varchar(20),
    businessunitid varchar(40),
    businessunitnumber varchar(20),
    orderid varchar(40),
    ordertype varchar(50),
    sourceorderid varchar(50),
    referencenumber varchar(100),
    purchaseordernumber varchar(100),
    ordernumber varchar(50),
    status varchar(50),
    languagecode varchar(10),
    rmanumber varchar(40),
    returntype varchar(25),
    returnnoticenumber varchar(40),
    returndate timestamp,
    returneligibledate timestamp,
    returnexpirydate timestamp,
    returncancelleddate timestamp,
    referencenumber1 varchar(50),
    referencenumber2 varchar(50),
    referencenumber3 varchar(50),
    customerid varchar(50),
    warehouseid varchar(50),
    returnid varchar(40),
    enteredat timestamp,
    languagedescription varchar(10),
    returnorderid varchar(40),
    exchangeorderid varchar(40),
    returnstatus varchar(50),
    returnlabelid varchar(40),
    shipdate timestamp,
    senderfullname varchar(256),
    senderfirstname varchar(50),
    senderlastname varchar(50),
    sendermiddlename varchar(50),
    senderprefix varchar(50),
    sendersuffix varchar(50),
    senderaddressline1 varchar(100),
    senderaddressline2 varchar(100),
    senderaddressline3 varchar(100),
    sendercity varchar(50),
    senderstatedescription varchar(50),
    senderpostalcode varchar(50),
    sendercountrydescription varchar(100),
    senderemail varchar(256),
    senderphone varchar(50),
    receiverfullname varchar(256),
    receiverfirstname varchar(50),
    receiverlastname varchar(50),
    receivermiddlename varchar(50),
    receiverprefix varchar(50),
    receiversuffix varchar(50),
    receiveraddressline1 varchar(100),
    receiveraddressline2 varchar(100),
    receiveraddressline3 varchar(100),
    receivercity varchar(50),
    receiverstatedescription varchar(50),
    receiverpostalcode varchar(50),
    receivercountrydescription varchar(100),
    receiveremail varchar(256),
    receiverphone varchar(50),
    carrierdescription varchar(50),
    deliveryservicedescription varchar(50),
    rateshopcode varchar(50),
    rateshopdescription varchar(50),
    parcelid varchar(40),
    shipmentid varchar(40),
    packageidentifier varchar(40),
    reprinturl varchar(500),
    returnlabelurl varchar(500),
    mastertrackingnumber varchar(40),
    trackingnumber varchar(40),
    actualrefundeddiscountamount varchar(50),
    createdby varchar(320),
    createddate timestamp,
    modifiedby varchar(320),
    modifieddate timestamp,
    timeinepoch numeric,
    iscreationaltagcompleted bpchar,
    isdeleted bpchar,
    isarchived bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    updatedlocation varchar(200),
    dropoffaddressline1 varchar(100),
    dropoffaddressline2 varchar(100),
    dropoffaddressline3 varchar(100),
    dropoffcity varchar(50),
    dropoffstate varchar(50),
    dropoffpostalcode varchar(50),
    dropoffcountry varchar(50),
    emailcc varchar(300),
    senderstate varchar(50),
    sendercountry varchar(100),
    receiverstate varchar(50),
    receivercountry varchar(100),
    carrier varchar(50),
    deliveryservice varchar(50),
CONSTRAINT return_id PRIMARY KEY (id)
);","Holds comprehensive details for each return, including order information, sender/receiver addresses, return status, and associated shipping details."
returns.returnitem,"CREATE TABLE returns.returnitem (
    id varchar(40) NOT NULL,
    returnid varchar(40),
    fulfillmentid varchar(40),
    fulfillmentorderdetailsid varchar(40),
    applineid varchar(50),
    sku varchar(256),
    description varchar(256),
    exchangesku varchar(256),
    exchangeitemdescription varchar(256),
    returnquantityuomdescription varchar(50),
    linenumber varchar(50),
    lotnumber varchar(256),
    status varchar(50),
    refundstatus varchar(50),
    ordered numeric,
    canceled numeric,
    invoiced numeric,
    refunded numeric,
    returned numeric,
    shipped numeric,
    returnorderid varchar(40),
    exchangeorderid varchar(40),
    refundinitiateddate timestamp,
    exchangeinitiateddate timestamp,
    errormessage varchar(200),
    exchangeorderstatus varchar(50),
    returnquantity numeric,
    returnquantityuom varchar(50),
    listpriceamount numeric,
    listpricecurrencycode varchar(50),
    omsisapproved bpchar,
    omsisdenied bpchar,
    omsdenialmessage varchar(200),
    returncomments varchar(500),
    refundtype varchar(50),
    refundamount numeric,
    refundcurrencycode varchar(50),
    refunddiscountamount numeric,
    refundtaxamount numeric,
    refundshippingamount numeric,
    actualrefundamount numeric,
    actualrefundcurrencycode varchar(50),
    actualrefunddiscountamount numeric,
    actualrefundtaxamount numeric,
    actualrefundshippingamount numeric,
    refundedsubtotal numeric,
    returnitemcondition varchar(50),
    returnitemconditioncomment varchar(200),
    refundamountrecommendation varchar(50),
    actualreturnquantity numeric,
    createdby varchar(320),
    createddate timestamp,
    modifiedby varchar(320),
    modifieddate timestamp,
    timeinepoch numeric,
    isdeleted bpchar,
    isarchived bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    updatedlocation varchar(200),
    exchangeordershippeddate timestamp,
    exchangeorderreceiveddate timestamp,
    exchangeorderdelivereddate timestamp,
    carrier varchar(40),
    trackingnumber varchar(50),
    returnreferencenumber varchar(50),
    exchangereferencenumber varchar(50),
    restockingfee numeric,
    returnsallowed bpchar,
    exchangeallowed bpchar,
    returndisposition bpchar,
    itembusinessunitid varchar(40),
    returnreasoncode varchar(50),
CONSTRAINT returnitem_id PRIMARY KEY (id)
);","Contains details for each item within a return, including SKU, quantities, refund status, exchange information, and item condition."
returns.returnsdefaultpolicy,"CREATE TABLE returns.returnsdefaultpolicy (
    id varchar(40) NOT NULL,
    returnssettingid varchar(40),
    languagecode varchar(10),
    languagedescription varchar(10),
    policytemplate text,
    createdby varchar(320),
    createddate timestamp,
    modifiedby varchar(320),
    modifieddate timestamp,
    timeinepoch numeric,
    isactive bpchar,
    isdeleted bpchar,
    isarchived bpchar,
    isdefault bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    updatedlocation varchar(200),
    accountid varchar(50),
    accountcode varchar(20),
    businessunitid varchar(50),
    businessunitnumber varchar(20),
    customerid varchar(50),
    warehouseid varchar(50),
CONSTRAINT returnsdefaultpolicy_id PRIMARY KEY (id)
);","Defines default return policies, including the policy template and language, applicable to specific accounts, business units, customers, or warehouses."
returns.returnsemailtemplate,"CREATE TABLE returns.returnsemailtemplate (
    id varchar(40) NOT NULL,
    returnssettingid varchar(40),
    languagecode varchar(10),
    languagedescription varchar(10),
    emailtypeid varchar(40),
    emailtypename varchar(50),
    emailcc varchar(500),
    emailbcc varchar(500),
    emailsubject varchar(500),
    template text,
    createdby varchar(320),
    createddate timestamp,
    modifiedby varchar(320),
    modifieddate timestamp,
    timeinepoch numeric,
    isactive bpchar,
    isdeleted bpchar,
    isarchived bpchar,
    isdefault bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    updatedlocation varchar(200),
    templateurl varchar(500),
    accountid varchar(50),
    accountcode varchar(20),
    businessunitid varchar(50),
    businessunitnumber varchar(20),
    customerid varchar(50),
    warehouseid varchar(50),
CONSTRAINT returnsemailtemplate_id PRIMARY KEY (id)
);","Manages email templates for returns, including subject, body, CC/BCC, and associated settings for different email types and organizational units."
returns.returnsmailinginstruction,"CREATE TABLE returns.returnsmailinginstruction (
    id varchar(40) NOT NULL,
    returnssettingid varchar(40),
    languagecode varchar(10),
    languagedescription varchar(10),
    mailinginstruction text,
    returndisclaimer varchar(1000),
    createdby varchar(320),
    createddate timestamp,
    modifiedby varchar(320),
    modifieddate timestamp,
    timeinepoch numeric,
    isdeleted bpchar,
    isarchived bpchar,
    isdefault bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    updatedlocation varchar(200),
    accountid varchar(50),
    accountcode varchar(20),
    businessunitid varchar(50),
    businessunitnumber varchar(20),
    customerid varchar(50),
    warehouseid varchar(50),
CONSTRAINT returnsmailinginstruction_id PRIMARY KEY (id)
);","Stores mailing instructions and return disclaimers, customizable by language and applicable to various organizational levels."
returns.returnsreason,"CREATE TABLE returns.returnsreason (
    id varchar(40) NOT NULL,
    returnssettingid varchar(40),
    languagecode varchar(10),
    languagedescription varchar(10),
    reasontype varchar(10),
    reasoncode varchar(50),
    reasondescription varchar(100),
    accountid varchar(50),
    businessunitid varchar(50),
    businessunitnumber varchar(20),
    customerid varchar(50),
    warehouseid varchar(50),
    accountcode varchar(20),
    createdby varchar(320),
    createddate timestamp,
    modifiedby varchar(320),
    modifieddate timestamp,
    timeinepoch numeric,
    isdeleted bpchar,
    isarchived bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    updatedlocation varchar(200),
    isdefault bpchar,
CONSTRAINT returnsreason_id PRIMARY KEY (id)
);","Catalogs various return reasons with their codes, descriptions, and types, allowing for customization per account, business unit, customer, or warehouse."
returns.returnssetting,"CREATE TABLE returns.returnssetting (
    id varchar(40) NOT NULL,
    accountid varchar(40),
    accountcode varchar(20),
    businessunitid varchar(40),
    businessunitnumber varchar(20),
    title varchar(60),
    tagline varchar(100),
    iscancelenabled bpchar,
    isexchangeenabled bpchar,
    isbrandedtrackingenabled bpchar,
    logo text,
    returnwindow numeric,
    refundtrigger varchar(25),
    exchangeordertrigger varchar(25),
    csrreturnwindow numeric,
    labelexpirydays numeric,
    returnshippingwindow numeric,
    shippingdefaultheight numeric,
    shippingdefaultlength numeric,
    shippingdefaultwidth numeric,
    shippingdefaultdimensionuom varchar(10),
    shippingdefaultweight numeric,
    shippingdefaultweightuom varchar(10),
    bccemail varchar(200),
    supportphone varchar(100),
    supportemail varchar(200),
    customerid varchar(50),
    warehouseid varchar(50),
    shippingdefaultdimensionuomcode varchar(10),
    shippingdefaultdimensionuomdescription varchar(10),
    shippingdefaultweightuomcode varchar(10),
    shippingdefaultweightuomdescription varchar(10),
    isdefault bpchar,
    createdby varchar(320),
    createddate timestamp,
    modifiedby varchar(320),
    modifieddate timestamp,
    timeinepoch numeric,
    isdeleted bpchar,
    isarchived bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    updatedlocation varchar(200),
CONSTRAINT returnssetting_id PRIMARY KEY (id)
);","Configures overall return settings, including return windows, refund/exchange triggers, shipping defaults, and branding options."
returns.returnsubtask,"CREATE TABLE returns.returnsubtask (
    id varchar(40) NOT NULL,
    returnsubtaskid varchar(40),
    createddate timestamp,
    createdby varchar(320),
    modifieddate timestamp,
    modifiedby varchar(320),
    timeinepoch numeric,
    accountid varchar(50),
    businessunitid varchar(50),
    warehouseid varchar(50),
    customerid varchar(50),
    returntaskid varchar(40),
    fulfillmentid varchar(50),
    errorcode varchar(15),
    errormessage text,
    fulfillmentorderdetailsid varchar(40),
    sku varchar(256),
    applineid varchar(50),
    returnquantity numeric,
    returnquantityuom varchar(50),
    exchangesku varchar(256),
    returncomments varchar(500),
    step varchar(50),
    status varchar(25),
    returnitemid varchar(40),
    isdeleted bpchar,
    isarchived bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    updatedlocation varchar(200),
    returnreasoncode varchar(50),
    returnsubtaskerrorid varchar(40),
CONSTRAINT returnsubtask_id PRIMARY KEY (id)
);","Tracks individual subtasks within a return process, detailing fulfillment, item information, quantities, and any associated errors or comments."
returns.returntask,"CREATE TABLE returns.returntask (
    id varchar(50) NOT NULL,
    returntaskerrorid varchar(50),
    createddate timestamp,
    createdby varchar(320),
    modifieddate timestamp,
    modifiedby varchar(320),
    timeinepoch numeric,
    accountid varchar(50),
    businessunitid varchar(50),
    warehouseid varchar(50),
    customerid varchar(50),
    orderid varchar(40),
    referencenumber varchar(50),
    status varchar(25),
    returntype varchar(25),
    languagecode varchar(10),
    returnid varchar(40),
    rmanumber varchar(40),
    cancelreason varchar(100),
    errorcode varchar(15),
    errormessage text,
    isdeleted bpchar,
    isarchived bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    updatedlocation varchar(200),
    carrier varchar(50),
    deliveryservice varchar(50),
    returnrateshopcode varchar(50),
    emailcc varchar(300),
    account varchar(20),
    businessunit varchar(20),
    isdefault bpchar,
CONSTRAINT returntask_id PRIMARY KEY (id)
);","Records high-level return tasks, including order and return identifiers, status, type, and any errors encountered during the process."
returns.tagvalues,"CREATE TABLE returns.tagvalues (
    id varchar(40) NOT NULL,
    tagid varchar(40),
    entityid varchar(40),
    accountid varchar(50),
    businessunitid varchar(50),
    warehouseid varchar(50),
    customerid varchar(50),
    entityname varchar(256),
    attributename varchar(256),
    value varchar(256),
    createdby varchar(320),
    createddate timestamp,
    modifiedby varchar(320),
    modifieddate timestamp,
    timeinepoch int4,
    isdeleted bpchar,
    isarchived bpchar,
    isdefault bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    modifiedlocation varchar(200),
    updatedlocation varchar(200),
    hashkey varchar(256),
CONSTRAINT tagvalues_id PRIMARY KEY (id, id, id)
);","Stores tag values specifically for returns, linking tags to entities within the returns module and providing audit trail information."
returnsmanagement.plate,"CREATE TABLE returnsmanagement.plate (
    id varchar(50) NOT NULL,
    businessunitid varchar(50),
    accountid varchar(50),
    licenseplatenumber varchar(50),
    parent varchar(50),
    platetype varchar(50),
    locationid varchar(50),
    disposition varchar(50),
    returneditemid varchar(50),
    quantity int4,
    status varchar(40),
    createdtime timestamp,
    cancelledtime timestamp,
    closedtime timestamp,
    shippedtime timestamp,
    consigneeid varchar(50),
    label text,
    createdby varchar(320),
    createddate timestamp,
    modifiedby varchar(320),
    modifieddate timestamp,
    timeinepoch numeric,
    isdeleted bpchar,
    isarchived bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    updatedlocation varchar(200),
    rmanumber varchar(50),
    itemnumber varchar(40),
    itemdescription varchar(250),
    locationname varchar(40),
    dispositionid varchar(50),
    statusdescription varchar(200),
CONSTRAINT plate_id PRIMARY KEY (id)
);","Manages license plate information for returned items, including their status, location, disposition, and associated item details."
returnsmanagement.returneditemimages,"CREATE TABLE returnsmanagement.returneditemimages (
    id varchar(50) NOT NULL,
    returneditemid varchar(50),
    returnorderfulfillmentid varchar(50),
    imageurl varchar(500),
    sequence int4,
    timeinepoch numeric,
    createdby varchar(50),
    createddate timestamp,
    modifiedby varchar(320),
    modifieddate timestamp,
    isdeleted bpchar,
    isarchived bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    updatedlocation varchar(200),
    lastsyncdate timestamp,
CONSTRAINT returneditemimages_id PRIMARY KEY (id)
);","Stores URLs and metadata for images associated with returned items, linking them to specific returned items and fulfillment records."
returnsmanagement.returneditems,"CREATE TABLE returnsmanagement.returneditems (
    id varchar(50) NOT NULL,
    returnorderfulfillmentid varchar(50),
    status varchar(50),
    itemid varchar(50),
    productgroupid varchar(50),
    quantity int4,
    trackingnumber varchar(50),
    disposition varchar(50),
    plateid varchar(50),
    parentplateid varchar(50),
    licenseplatenumber varchar(50),
    timeinepoch numeric,
    createdby varchar(320),
    createddate timestamp,
    modifieddate timestamp,
    modifiedby varchar(320),
    isdeleted bpchar,
    isarchived bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    updatedlocation varchar(200),
    reviewnotes text,
    nextquestionid varchar(50),
    nextreviewrulequestionid varchar(50),
    reviewimagesaddressed bpchar,
    reviewnotesaddressed bpchar,
    itemnumber varchar(40),
    statusdescription varchar(200),
    dispositiondate timestamp,
    dispositionby varchar(320),
    businessunitid varchar(50),
    accountid varchar(50),
    palletstatus varchar(40),
    itemdescription varchar(250),
    locationid int4,
    locationname varchar(40),
    returnreasondescription varchar(100),
    unitofmeasure varchar(4),
    lotnumber varchar(40),
    weight float8,
    returnreasoncode varchar(50),
    parentlicenseplatenumber varchar(50),
    parentplatelocationid varchar(50),
CONSTRAINT returneditems_id PRIMARY KEY (id)
);","Details individual returned items, including their status, quantity, disposition, associated license plate information, and review notes."
returnsmanagement.returnorderfulfillment,"CREATE TABLE returnsmanagement.returnorderfulfillment (
    id varchar(50) NOT NULL,
    status varchar(50),
    statusdescription varchar(200),
    businessunitid varchar(50),
    accountid varchar(50),
    rmanumber varchar(50),
    fulfillmentid varchar(50),
    returnid varchar(50),
    ordernumber varchar(40),
    referencenumber varchar(30),
    synapsecustomerid varchar(40),
    synapsefacility varchar(40),
    createdby varchar(320),
    createddate timestamp,
    modifieddate timestamp,
    modifiedby varchar(320),
    timeinepoch numeric,
    isdeleted bpchar,
    isarchived bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    updatedlocation varchar(200),
    preholdstatus varchar(50),
    preholdstatusdescription varchar(200),
CONSTRAINT returnorderfulfillment_id PRIMARY KEY (id)
);","Oversees the fulfillment process for return orders, tracking status, associated IDs, and relevant customer and facility information."
returnsmanagement.reviewresponses,"CREATE TABLE returnsmanagement.reviewresponses (
    id varchar(50) NOT NULL,
    returneditemid varchar(50),
    reviewrulequestionid varchar(50),
    question varchar(500),
    responseoption varchar(50),
    parentreviewresponseid varchar(50),
    disposition varchar(50),
    responseid varchar(50),
    createdby varchar(320),
    createddate timestamp,
    modifiedby varchar(320),
    modifieddate timestamp,
    timeinepoch numeric,
    isdeleted bpchar,
    isarchived bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    updatedlocation varchar(200),
    reworkinstruction text,
    reworkinstructionpriority int2,
    response text,
CONSTRAINT reviewresponses_id PRIMARY KEY (id)
);","Records responses to review questions for returned items, including the question, selected option, and any resulting disposition or rework instructions."
shipping.auditlog,"CREATE TABLE shipping.auditlog (
    id varchar(50) NOT NULL,
    createddate timestamp,
    createdby varchar(320),
    modifieddate timestamp,
    modifiedby varchar(320),
    timeinepoch numeric,
    auditsource varchar(10),
    parcelid varchar(50),
    isdeleted bpchar,
    isarchived bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    updatedlocation varchar(200),
    accountid varchar(50),
    businessunitid varchar(50),
    customerid varchar(50),
    warehouseid varchar(50),
    partitionkey varchar(10),
CONSTRAINT auditlog_id PRIMARY KEY (id, id)
);","Logs audit trails for shipping-related activities, tracking changes and events related to parcels with associated user and timestamp information."
shipping.carrieraccounts,"CREATE TABLE shipping.carrieraccounts (
    id varchar(50) NOT NULL,
    carriercode varchar(50),
    carrierdescription varchar(150),
    createddate timestamp,
    createdby varchar(320),
    modifieddate timestamp,
    modifiedby varchar(320),
    timeinepoch numeric,
    carrierid varchar(50),
    shipperid varchar(50),
    accountname varchar(50),
    accountnumber varchar(100),
    accountid varchar(50),
    businessunitid varchar(50),
    warehouseid varchar(50),
    customerid varchar(50),
    apikey varchar(300),
    username varchar(100),
    password varchar(100),
    shipper varchar(100),
    referenceid varchar(100),
    referencekey varchar(100),
    token varchar(300),
    isdeleted bpchar,
    isarchived bpchar,
    isdefault bpchar,
    instance varchar(100),
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    updatedlocation varchar(200),
    fsmsserver varchar(50),
    prefix varchar(50),
    allowcustomrates bpchar,
    allowcustomtimeintransit bpchar,
    partitionkey varchar(10),
CONSTRAINT carrieraccounts_id PRIMARY KEY (id, id)
);","Stores details for carrier accounts, including credentials, account numbers, and associated carrier services for shipping."
shipping.carrierservices,"CREATE TABLE shipping.carrierservices (
    id varchar(50) NOT NULL,
    description varchar(150),
    servicecode varchar(40),
    servicedescription varchar(100),
    carriercode varchar(40),
    carrierdescription varchar(100),
    accountid varchar(50),
    businessunitid varchar(50),
    warehouseid varchar(50),
    customerid varchar(50),
    createddate timestamp,
    createdby varchar(320),
    modifieddate timestamp,
    modifiedby varchar(320),
    timeinepoch numeric,
    carrierid varchar(50),
    serviceid varchar(50),
    carrieraccountid varchar(50),
    isdeleted bpchar,
    isarchived bpchar,
    isdefault bpchar,
    shipmenttype varchar(100),
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    updatedlocation varchar(200),
    partitionkey varchar(10),
CONSTRAINT carrierservices_id PRIMARY KEY (id, id)
);","Defines available carrier services, linking them to specific carriers and accounts, and providing details like service codes and descriptions."
shipping.commodityitems,"CREATE TABLE shipping.commodityitems (
    id varchar(50) NOT NULL,
    createddate timestamp,
    createdby varchar(320),
    modifieddate timestamp,
    modifiedby varchar(320),
    timeinepoch numeric,
    packageid varchar(50),
    itemid varchar(50),
    productcode varchar(25),
    itemdescription varchar(250),
    itemquantity numeric,
    harmonizedtariff varchar(25),
    itemunitweight numeric,
    itemunitweightunit varchar(5),
    itemunitmeasure varchar(10),
    itemunitvalue numeric,
    itemunitvalueunit varchar(5),
    countryoforigin varchar(2),
    ishazmat bpchar,
    isdeleted bpchar,
    isarchived bpchar,
    isdefault bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    updatedlocation varchar(200),
    accountid varchar(50),
    businessunitid varchar(50),
    customerid varchar(50),
    warehouseid varchar(50),
    partitionkey varchar(10) NOT NULL,
CONSTRAINT commodityitems_id PRIMARY KEY (id, id, partitionkey)
);","Lists commodity items included in packages, providing details such as quantity, weight, value, country of origin, and hazmat status."
shipping.commodityitems_default,"CREATE TABLE shipping.commodityitems_default (
    id varchar(50) NOT NULL,
    createddate timestamp,
    createdby varchar(320),
    modifieddate timestamp,
    modifiedby varchar(320),
    timeinepoch numeric,
    packageid varchar(50),
    itemid varchar(50),
    productcode varchar(25),
    itemdescription varchar(250),
    itemquantity numeric,
    harmonizedtariff varchar(25),
    itemunitweight numeric,
    itemunitweightunit varchar(5),
    itemunitmeasure varchar(10),
    itemunitvalue numeric,
    itemunitvalueunit varchar(5),
    countryoforigin varchar(2),
    ishazmat bpchar,
    isdeleted bpchar,
    isarchived bpchar,
    isdefault bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    updatedlocation varchar(200),
    accountid varchar(50),
    businessunitid varchar(50),
    customerid varchar(50),
    warehouseid varchar(50),
    partitionkey varchar(10) NOT NULL,
CONSTRAINT commodityitems_default_id PRIMARY KEY (id, partitionkey)
);","Default settings for commodity items, offering a baseline for common product attributes and international shipping requirements."
shipping.hazmatitems,"CREATE TABLE shipping.hazmatitems (
    id varchar(50) NOT NULL,
    createddate timestamp,
    createdby varchar(320),
    modifieddate timestamp,
    modifiedby varchar(320),
    timeinepoch numeric,
    packageid varchar(50),
    propershippingname varchar(255),
    hazmatclass varchar(25),
    unnumber varchar(50),
    technicalname varchar(250),
    emergencycontact varchar(150),
    emergencyphone varchar(50),
    quantity int4,
    packagingtype varchar(150),
    packaginggroup varchar(25),
    packaging varchar(100),
    weight numeric,
    weightunit varchar(5),
    regulation varchar(20),
    regulationset varchar(20),
    propershippingname2 varchar(150),
    naergnumber varchar(100),
    primarychemcode varchar(100),
    isdeleted bpchar,
    isarchived bpchar,
    isdefault bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    updatedlocation varchar(200),
    packinginstructions varchar(150),
    limitedquantity bpchar,
    nonhazardouslithiumbatteries bpchar,
    ishazardous bpchar,
    hazmatcode varchar(50),
    batterycount int4,
    cellcount int4,
    accountid varchar(50),
    businessunitid varchar(50),
    customerid varchar(50),
    warehouseid varchar(50),
    itemid varchar(50),
    partitionkey varchar(10) NOT NULL,
CONSTRAINT hazmatitems_id PRIMARY KEY (id, id, partitionkey)
);","Details hazardous materials included in shipments, including proper shipping names, hazard class, UN numbers, and emergency contact information."
shipping.hazmatitems_default,"CREATE TABLE shipping.hazmatitems_default (
    id varchar(50) NOT NULL,
    createddate timestamp,
    createdby varchar(320),
    modifieddate timestamp,
    modifiedby varchar(320),
    timeinepoch numeric,
    packageid varchar(50),
    propershippingname varchar(255),
    hazmatclass varchar(25),
    unnumber varchar(50),
    technicalname varchar(250),
    emergencycontact varchar(150),
    emergencyphone varchar(50),
    quantity int4,
    packagingtype varchar(150),
    packaginggroup varchar(25),
    packaging varchar(100),
    weight numeric,
    weightunit varchar(5),
    regulation varchar(20),
    regulationset varchar(20),
    propershippingname2 varchar(150),
    naergnumber varchar(100),
    primarychemcode varchar(100),
    isdeleted bpchar,
    isarchived bpchar,
    isdefault bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    updatedlocation varchar(200),
    packinginstructions varchar(150),
    limitedquantity bpchar,
    nonhazardouslithiumbatteries bpchar,
    ishazardous bpchar,
    hazmatcode varchar(50),
    batterycount int4,
    cellcount int4,
    accountid varchar(50),
    businessunitid varchar(50),
    customerid varchar(50),
    warehouseid varchar(50),
    itemid varchar(50),
    partitionkey varchar(10) NOT NULL,
CONSTRAINT hazmatitems_default_id PRIMARY KEY (id, partitionkey)
);","Default settings for hazardous materials, providing a baseline for common hazmat attributes and regulatory information for shipping."
shipping.manifestdocuments,"CREATE TABLE shipping.manifestdocuments (
    id varchar(50) NOT NULL,
    createddate timestamp,
    createdby varchar(320),
    modifieddate timestamp,
    modifiedby varchar(320),
    timeinepoch numeric,
    manifestid varchar(50),
    content text,
    documentformat varchar(50),
    documenttype varchar(200),
    isdeleted bpchar,
    isarchived bpchar,
    isdefault bpchar,
    documenturl varchar(150),
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    updatedlocation varchar(200),
    accountid varchar(50),
    businessunitid varchar(50),
    customerid varchar(50),
    warehouseid varchar(50),
    partitionkey varchar(10) NOT NULL,
CONSTRAINT manifestdocuments_id PRIMARY KEY (id, id, partitionkey)
);","Stores documents associated with shipping manifests, including content, format, and type, along with a URL for retrieval."
shipping.manifests,"CREATE TABLE shipping.manifests (
    id varchar(50) NOT NULL,
    createddate timestamp,
    carrieraccountname varchar(50),
    accountid varchar(40),
    businessunitid varchar(40),
    warehouseid varchar(50),
    createdby varchar(320),
    modifieddate timestamp,
    modifiedby varchar(320),
    timezone varchar(50),
    timeinepoch numeric,
    carrieraccountsid varchar(50),
    carriercode varchar(50),
    closeoutgroupcode varchar(50),
    shipdate timestamp,
    status varchar(15),
    hasdocuments bpchar,
    isdeleted bpchar,
    isarchived bpchar,
    isdefault bpchar,
    closeoutdatetime timestamp,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    updatedlocation varchar(200),
    baseurl varchar(150),
    customerid varchar(50),
    servicecode varchar(50),
    statusreason text,
    carriermessage varchar(500),
    partitionkey varchar(10) NOT NULL,
CONSTRAINT manifests_id PRIMARY KEY (id, id, partitionkey)
);","Manages shipping manifests, tracking details like carrier account, ship date, status, and associated documents."
shipping.manifeststages,"CREATE TABLE shipping.manifeststages (
    id varchar(50) NOT NULL,
    createddate timestamp,
    createdby varchar(320),
    modifieddate timestamp,
    modifiedby varchar(320),
    timeinepoch numeric,
    manifestid varchar(50),
    stage varchar(30),
    status varchar(50),
    errormessage text,
    isdeleted bpchar,
    isarchived bpchar,
    isdefault bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    updatedlocation varchar(200),
    accountid varchar(50),
    businessunitid varchar(50),
    customerid varchar(50),
    warehouseid varchar(50),
    partitionkey varchar(10) NOT NULL,
CONSTRAINT manifeststages_id PRIMARY KEY (id, id, partitionkey)
);","Tracks the different stages of a shipping manifest, including status and any error messages encountered at each stage."
shipping.packageaudit,"CREATE TABLE shipping.packageaudit (
    id varchar(50) NOT NULL,
    createddate timestamp,
    createdby varchar(320),
    modifieddate timestamp,
    modifiedby varchar(320),
    timeinepoch numeric,
    shipmentauditid varchar(50),
    packageidentifier varchar(100),
    orderid varchar(100),
    length numeric,
    width numeric,
    height numeric,
    dimensionunit varchar(5),
    weight numeric,
    weightunit varchar(5),
    packagetype varchar(25),
    description varchar(250),
    isdeleted bpchar,
    isarchived bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    updatedlocation varchar(200),
    shipmentcost numeric,
    sellcost numeric,
    shipmentcostunit varchar(5),
    sellcostunit varchar(5),
    mastertrackingnumber varchar(150),
    trackingnumber varchar(150),
    trackingnumber2 varchar(150),
    accountid varchar(50),
    businessunitid varchar(50),
    customerid varchar(50),
    warehouseid varchar(50),
    partitionkey varchar(10),
CONSTRAINT packageaudit_id PRIMARY KEY (id, id)
);","Performs audits on packages, logging details such as dimensions, weight, costs, and tracking numbers for each package within a shipment audit."
shipping.packagelabels,"CREATE TABLE shipping.packagelabels (
    id varchar(50) NOT NULL,
    createddate timestamp,
    createdby varchar(320),
    modifieddate timestamp,
    modifiedby varchar(320),
    licenseplateid varchar(100),
    timeinepoch numeric,
    packageid varchar(50),
    manifestid varchar(50),
    contenttype varchar(10),
    shipmentid varchar(50),
    accountid varchar(50),
    businessunitid varchar(50),
    customerid varchar(50),
    warehouseid varchar(50),
    labeltype varchar(10),
    content bytea,
    voided bpchar,
    isdeleted bpchar,
    isarchived bpchar,
    isdefault bpchar,
    expirydate timestamp,
    labelurl varchar(150),
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    updatedlocation varchar(200),
    labelheight numeric,
    labelwidth numeric,
    labeldpi numeric,
    labelrotation numeric,
    labeltypedescription varchar(150),
    labeltypesequencenumber int4,
    partitionkey varchar(10),
	CONSTRAINT packagelabels_id PRIMARY KEY (id, id)
);","Stores information about generated shipping labels, including unique identifiers, creation/modification details, associated package/shipment/manifest IDs, content type, label type (e.g., PDF, ZPL), actual label content or URL, void status, expiry date, dimensions, and organizational identifiers."
shipping.packages,"CREATE TABLE shipping.packages (
    id varchar(50) NOT NULL,
    createddate timestamp,
    createdby varchar(320),
    modifieddate timestamp,
    modifiedby varchar(320),
    timeinepoch numeric,
    shipmentid varchar(50),
    packageidentifier varchar(100),
    fulfillmentid varchar(100),
    status varchar(25),
    orderid varchar(100),
    orderdate timestamp,
    purchaseordernumber varchar(100),
    description varchar(250),
    carrierinstructions varchar(2000),
    length numeric,
    width numeric,
    height numeric,
    dimensionunit varchar(5),
    weight numeric,
    weightunit varchar(5),
    packagetype varchar(25),
    lossprotectionmethod varchar(100),
    lossprotectioncost numeric,
    lossprotectionunit varchar(5),
    reference1 varchar(100),
    reference2 varchar(100),
    reference3 varchar(100),
    reference4 varchar(100),
    reference5 varchar(100),
    dryiceweight numeric,
    dryiceweightunit varchar(5),
    dryicepurpose varchar(50),
    signatorytitle varchar(150),
    signatoryname varchar(150),
    signatorycompany varchar(150),
    signatoryaddress1 varchar(200),
    signatoryaddress2 varchar(150),
    signatoryaddress3 varchar(150),
    signatorycity varchar(150),
    signatorystate varchar(250),
    signatoryzipcode varchar(15),
    signatorycountry varchar(2),
    signatoryphone varchar(20),
    signatoryemail varchar(150),
    signatorydestinationtype varchar(20),
    reasonofexport varchar(100),
    ioraccountnumber varchar(25),
    portofentry varchar(150),
    destinationcountry varchar(2),
    commercialinvoicemethod varchar(50),
    declaredvalue numeric,
    declaredvalueunit varchar(5),
    iorname varchar(150),
    iorcompany varchar(150),
    ioraddress1 varchar(200),
    ioraddress2 varchar(150),
    ioraddress3 varchar(150),
    iorcity varchar(150),
    iorstate varchar(250),
    iorzipcode varchar(15),
    iorcountry varchar(2),
    iorphone varchar(20),
    ioremail varchar(150),
    iordestinationtype varchar(20),
    customsbrokername varchar(150),
    customsbrokercompany varchar(150),
    customsbrokeraddress1 varchar(200),
    customsbrokeraddress2 varchar(150),
    customsbrokeraddress3 varchar(150),
    customsbrokercity varchar(150),
    customsbrokerstate varchar(250),
    customsbrokerzipcode varchar(15),
    customsbrokercountry varchar(2),
    customsbrokerphone varchar(20),
    customsbrokeremail varchar(150),
    customsbrokerdestinationtype varchar(20),
    customstransactionnumber varchar(100),
    shipmentcost numeric,
    sellcost numeric,
    shipmentcostunit varchar(5),
    sellcostunit varchar(5),
    carriershipmentid varchar(150),
    mastertrackingnumber varchar(150),
    trackingnumber varchar(150),
    trackingnumber2 varchar(150),
    ismachinable bpchar,
    isdeleted bpchar,
    isarchived bpchar,
    isdefault bpchar,
    trackingnumber3 varchar(150),
    trackingnumber4 varchar(150),
    trackingnumber5 varchar(150),
    templatedatasource varchar(600),
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    updatedlocation varchar(200),
    hazmatpackagingtype varchar(150),
    emergencycontact varchar(150),
    emergencyphone varchar(50),
    signatoryisothreecountry varchar(3),
    iorisothreecountry varchar(3),
    customsbrokerisothreecountry varchar(3),
    ismanual bpchar,
    accountid varchar(50),
    businessunitid varchar(50),
    warehouseid varchar(50),
    customerid varchar(50),
    ondelivery bpchar,
    onestimateddelivery bpchar,
    onexception bpchar,
    onshipment bpchar,
    ontender bpchar,
    partitionkey varchar(10) NOT NULL,
	CONSTRAINT packages_id PRIMARY KEY (id, id, partitionkey)
);","Comprehensive details about individual packages within a shipment, including their physical attributes, contents, associated order and fulfillment information, international shipping declarations, tracking numbers, and relevant organizational identifiers. It also specifies notification preferences for various package events."
shipping.rateshopresultaudit,"CREATE TABLE shipping.rateshopresultaudit (
    id varchar(50) NOT NULL,
    createddate timestamp,
    createdby varchar(320),
    modifieddate timestamp,
    modifiedby varchar(320),
    timeinepoch numeric,
    auditlogid varchar(50),
    timeintransit numeric,
    shipmentcost numeric,
    shipmentcostunit varchar(5),
    shipmentsellcost numeric,
    shipmentsellcostunit varchar(5),
    ratedcarrier varchar(50),
    ratedservice varchar(50),
    ratedcarrieraccount varchar(50),
    expecteddeliverydate timestamp,
    ratedshipdate timestamp,
    errormessage text,
    isdeleted bpchar,
    isarchived bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    updatedlocation varchar(200),
    isselectedservice bpchar,
    isdefaultservice bpchar,
    ratedcarrieraccountid varchar(50),
    isshipped bpchar,
    accountid varchar(50),
    businessunitid varchar(50),
    customerid varchar(50),
    warehouseid varchar(50),
    partitionkey varchar(10),
	CONSTRAINT rateshopresultaudit_id PRIMARY KEY (id, id)
);","Logs the results of various rateshopping attempts for shipments, capturing the costs, estimated transit times, carrier and service details, and any errors encountered during the rate quotation process. It also indicates whether a service was selected or set as default."
shipping.shipmentaudit,"CREATE TABLE shipping.shipmentaudit (
    id varchar(50) NOT NULL,
    accountid varchar(50),
    createddate timestamp,
    createdby varchar(320),
    modifieddate timestamp,
    modifiedby varchar(320),
    timeinepoch numeric,
    auditlogid varchar(50),
    recievername varchar(150),
    recievercompany varchar(150),
    recieveraddress1 varchar(200),
    recieveraddress2 varchar(150),
    recieveraddress3 varchar(150),
    recievercity varchar(150),
    recieverstate varchar(250),
    recieverzipcode varchar(15),
    recievercountry varchar(2),
    recieverphone varchar(20),
    recieveremail varchar(150),
    recieverdestinationtype varchar(20),
    recieverisresidential bpchar,
    sendername varchar(150),
    sendercompany varchar(150),
    senderaddress1 varchar(200),
    senderaddress2 varchar(150),
    senderaddress3 varchar(150),
    sendercity varchar(150),
    senderstate varchar(250),
    senderzipcode varchar(15),
    sendercountry varchar(2),
    senderphone varchar(20),
    senderemail varchar(150),
    senderdestinationtype varchar(20),
    carriercode varchar(50),
    servicecode varchar(50),
    paymenttermscode varchar(50),
    termsofsalecode varchar(50),
    billtoaccount varchar(50),
    billtoname varchar(150),
    billtocompany varchar(150),
    billtoaddress1 varchar(200),
    billtoaddress2 varchar(150),
    billtoaddress3 varchar(150),
    billtocity varchar(150),
    billtostate varchar(250),
    billtozipcode varchar(15),
    billtocountry varchar(2),
    billtophone varchar(20),
    billtoemail varchar(150),
    billtodestinationtype varchar(20),
    isdeleted bpchar,
    isarchived bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    updatedlocation varchar(200),
    shipmentcost numeric,
    sellcost numeric,
    shipmentcostunit varchar(5),
    sellcostunit varchar(5),
    expecteddeliverydate timestamp,
    timeintransit numeric,
    rateshopcode varchar(40),
    carrieraccountsid varchar(50),
    errormessage text,
    accountcode varchar(20),
    businessunitnumber varchar(20),
    shipperid varchar(50),
    carrieraccountname varchar(50),
    senderisothreecountry varchar(3),
    recieverisothreecountry varchar(3),
    billtoisothreecountry varchar(3),
    shipdate timestamp,
    businessunitid varchar(50),
    customerid varchar(50),
    warehouseid varchar(50),
    partitionkey varchar(10),
	CONSTRAINT shipmentaudit_id PRIMARY KEY (id, id)
);","Records detailed audit information for shipments, including comprehensive sender, receiver, and bill-to address details, carrier and service codes, payment terms, cost breakdown, and expected delivery information at the time of an audit event."
shipping.shipments,"CREATE TABLE shipping.shipments (
    id varchar(50) NOT NULL,
    createddate timestamp,
    createdby varchar(320),
    modifieddate timestamp,
    modifiedby varchar(320),
    accountid varchar(50),
    deliverytype varchar(50),
    deliverydatetime timestamp,
    pickuptype varchar(50),
    deliveryconfirmation varchar(50),
    holdatname varchar(150),
    holdataddress1 varchar(200),
    holdataddress2 varchar(150),
    holdataddress3 varchar(150),
    holdatcompany varchar(150),
    holdatstate varchar(2),
    holdatcity varchar(150),
    holdatzipcode varchar(15),
    holdatcountry varchar(2),
    holdatphone varchar(20),
    holdatemail varchar(150),
    holdatdestinationtype varchar(20),
    isadditionalhandling bpchar,
    accountcode varchar(40),
    businessunitid varchar(50),
    customerid varchar(50),
    businessunitcode varchar(40),
    carrierid varchar,
    carrierdescription varchar(150),
    carrieraccountname varchar,
    carrieraccountnumber varchar,
    closeoutgroupcode varchar,
    closeoutgroupdescription varchar,
    servicedescription varchar(150),
    warehouseid varchar(50),
    shipby varchar(40),
    voidby varchar(40),
    timeinepoch numeric,
    parcelid varchar(50),
    shipmenttype varchar(15),
    sendername varchar(150),
    sendercompany varchar(150),
    senderaddress1 varchar(200),
    senderaddress2 varchar(150),
    senderaddress3 varchar(150),
    sendercity varchar(150),
    senderstate varchar(250),
    senderzipcode varchar(15),
    sendercountry varchar(2),
    senderphone varchar(20),
    senderemail varchar(150),
    senderdestinationtype varchar(20),
    receivername varchar(150),
    receivercompany varchar(150),
    receiveraddress1 varchar(200),
    receiveraddress2 varchar(150),
    receiveraddress3 varchar(150),
    receivercity varchar(150),
    receiverstate varchar(250),
    receiverzipcode varchar(15),
    receivercountry varchar(2),
    receiverphone varchar(20),
    receiveremail varchar(150),
    receiverdestinationtype varchar(20),
    receiverisresidential bpchar,
    returntoname varchar(150),
    returntocompany varchar(150),
    returntoaddress1 varchar(200),
    returntoaddress2 varchar(150),
    returntoaddress3 varchar(150),
    returntocity varchar(150),
    returntostate varchar(250),
    returntozipcode varchar(15),
    returntocountry varchar(2),
    returntophone varchar(20),
    returntoemail varchar(150),
    returntodestinationtype varchar(20),
    carriercode varchar(50),
    servicecode varchar(50),
    reference1 varchar(100),
    reference2 varchar(100),
    reference3 varchar(100),
    reference4 varchar(100),
    reference5 varchar(100),
    billtoname varchar(150),
    billtocompany varchar(150),
    billtoaddress1 varchar(200),
    billtoaddress2 varchar(150),
    billtoaddress3 varchar(150),
    billtocity varchar(150),
    billtostate varchar(250),
    billtozipcode varchar(15),
    billtocountry varchar(2),
    billtophone varchar(20),
    billtoemail varchar(150),
    billtodestinationtype varchar(20),
    billtoaccount varchar(50),
    paymenttermscode varchar(50),
    termsofsalecode varchar(50),
    dutiestaxespaymentaccount varchar(50),
    parcelweight numeric,
    parcelweightunit varchar(5),
    shipdate timestamp,
    expecteddeliverydate timestamp,
    carriershipmentid varchar(100),
    carrieraccountsid varchar(50),
    shipmentcost numeric,
    sellcost numeric,
    shipmentcostunit varchar(5),
    sellcostunit varchar(5),
    timeintransit numeric,
    labelrotation varchar(3),
    manifestid varchar(50),
    orderdate timestamp,
    commitmentdate timestamp,
    rateshoptype varchar(50),
    rateshopcode varchar(40),
    voided bpchar,
    voidmessage varchar(500),
    closed bpchar,
    isdeleted bpchar,
    isarchived bpchar,
    isdefault bpchar,
    processeddatetime timestamp,
    voideddatetime timestamp,
    timezone varchar(50),
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    updatedlocation varchar(200),
    ordertype varchar(50),
    senderisothreecountry varchar(3),
    receiverisothreecountry varchar(3),
    returntoisothreecountry varchar(3),
    billtoisothreecountry varchar(3),
    carriersymbol varchar(50),
    orderid varchar(50),
    isreturnonlyshipment bpchar,
    rateshopflag bpchar,
    rateshopgroup varchar(100),
    cidocumenturl varchar(300),
    ratedaddressgroup varchar(50),
    ratedshipmentgroup varchar(50),
    partitionkey varchar(10) NOT NULL,
	CONSTRAINT shipments_id PRIMARY KEY (id, id, partitionkey)
);","Comprehensive shipment records containing sender, receiver, billing, and return addresses along with carrier information, costs, and delivery details. Central table for managing shipment lifecycle from creation to delivery."
tracking.carriertracking,"CREATE TABLE tracking.carriertracking (
    id varchar(40) NOT NULL,
    trackingnumber varchar(40),
    carrier varchar(40),
    shippernumber varchar(40),
    deliveryservice varchar(100),
    isdelivered bpchar,
    createdby varchar(320),
    createddate timestamp,
    modifiedby varchar(320),
    modifieddate timestamp,
    timeinepoch numeric,
    isdeleted bpchar,
    isarchived bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    updatedlocation varchar(200),
    missedscheduleddelivery bpchar,
    accountid varchar(40),
    businessunitid varchar(40),
    orderid varchar(40),
    shipmentid varchar(50),
    fulfillmentid varchar(100),
    packageidentifier varchar(100),
    rmanumber varchar(100),
    shippername varchar(100),
    shipperaddressline1 varchar(100),
    shipperaddressline2 varchar(100),
    shipperaddressline3 varchar(100),
    shipperaddressbuildingname varchar(100),
    shipperaddresssuite varchar(100),
    shipperaddressstate varchar(100),
    shipperaddresscountry varchar(100),
    shipperaddresscity varchar(50),
    shipperaddresspostalcode varchar(40),
    shipperaddresslatitude varchar(40),
    shipperaddresslongitude varchar(40),
    shiptoname varchar(100),
    shiptoaddressline1 varchar(100),
    shiptoaddressline2 varchar(100),
    shiptoaddressline3 varchar(100),
    shiptoaddressbuildingname varchar(100),
    shiptoaddresssuite varchar(100),
    shiptoaddressstate varchar(100),
    shiptoaddresscountry varchar(100),
    shiptoaddresscity varchar(50),
    shiptoaddresspostalcode varchar(40),
    shiptoaddresslatitude varchar(40),
    shiptoaddresslongitude varchar(40),
    packageweight float4,
    packageweightuom varchar(20),
    packagelength float4,
    packagewidth float4,
    packageheight float4,
    packagedimensionuom varchar(20),
    packagedimensionalweight float4,
    packagedimensionalweightuom varchar(20),
    packagedescription varchar(200),
    packagecount int4,
    scheduleddeliverydate varchar(10),
    scheduleddeliverytime varchar(10),
    scheduleddeliverytimezone varchar(10),
    initialscheduleddeliverydate varchar(10),
    initialscheduleddeliverytime varchar(10),
    initialscheduleddeliverytimezone varchar(10),
    shipperphone varchar(40),
    referencenumber varchar(100),
    deliveredtime varchar(10),
    deliveredtimezone varchar(10),
    omsreferencenumber varchar(100),
    parcelid varchar(50),
    purchaseordernumber varchar(50),
    returnid varchar(50),
    scheduleddeliverytimestamp timestamp,
    initialscheduleddeliverytimestamp timestamp,
    deliveredtimestamp timestamp,
    viteactivitytypeid varchar(50),
    ordernumber varchar(40),
    shiptophone varchar(40),
    delivereddate varchar(10),
    customerid varchar(50),
    warehouseid varchar(50),
    stage varchar(40),
    statuscode varchar(40),
    statusdescription varchar(40),
	CONSTRAINT carriertracking_id PRIMARY KEY (id)
);","Carrier tracking information for shipments including package details, addresses, delivery schedules, and current status. Links shipments to real-time tracking data from carriers."
tracking.trackingactivity,"CREATE TABLE tracking.trackingactivity (
    id varchar(40) NOT NULL,
    carriertrackingid varchar(40),
    carrieractivitytypeid varchar(40),
    activitylocationname varchar(100),
    activitylocationstate varchar(100),
    activitylocationcountry varchar(100),
    rescheduleddeliverydate varchar(10),
    receivingaddressname varchar(100),
    deliveryservice varchar(100),
    createdby varchar(320),
    createddate timestamp,
    modifiedby varchar(320),
    modifieddate timestamp,
    timeinepoch numeric,
    isdeleted bpchar,
    isarchived bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    updatedlocation varchar(200),
    activitylocationcity varchar(50),
    activitylocationpostalcode varchar(50),
    activitylocationlatitude varchar(40),
    activitylocationlongitude varchar(40),
    viteactivitytypeid varchar(40),
    activitydate varchar(10),
    activitytime varchar(10),
    activitytimezone varchar(10),
    rescheduleddeliverytime varchar(10),
    rescheduleddeliverytimezone varchar(10),
    activitytimestamp timestamp,
    rescheduleddeliverytimestamp timestamp,
    accountid varchar(50),
    businessunitid varchar(50),
    customerid varchar(50),
    warehouseid varchar(50),
    activitytype varchar(40),
    activitytypedescription varchar(50),
    carrieractivitytypedescription varchar(50),
    activitystage varchar(50),
	CONSTRAINT trackingactivity_id PRIMARY KEY (id)
);","Records individual tracking events or activities for a shipment, including details about the activity type, location, and any rescheduled delivery information. It links to the main carrier tracking record."
tracking.trackingdelivery,"CREATE TABLE tracking.trackingdelivery (
    id varchar(40) NOT NULL,
    carriertrackingid varchar(40),
    activitytype varchar(40),
    activitylocationname varchar(100),
    activitylocationstate varchar(100),
    activitylocationcountry varchar(100),
    deliveryaddressname varchar(100),
    deliveryaddressbuildingname varchar(100),
    deliveryaddresssuite varchar(100),
    deliveryaddresscity varchar(100),
    deliveryaddressstate varchar(100),
    deliveryaddresspostalcode varchar(100),
    deliveryaddresscountry varchar(100),
    deliverylocation varchar(100),
    signedforby varchar(100),
    lastpickupbydate varchar(10),
    createdby varchar(320),
    createddate timestamp,
    modifiedby varchar(320),
    modifieddate timestamp,
    timeinepoch numeric,
    isdeleted bpchar,
    isarchived bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    updatedlocation varchar(200),
    activitylocationcity varchar(50),
    activitylocationpostalcode varchar(40),
    activitylocationlatitude varchar(40),
    activitylocationlongitude varchar(40),
    deliveryaddresslatitude varchar(20),
    deliveryaddresslongitude varchar(20),
    activitydate varchar(10),
    activitytime varchar(10),
    activitytimezone varchar(10),
    lastpickupbytime varchar(10),
    lastpickupbytimezone varchar(10),
    deliveryaddressline1 varchar(100),
    deliveryaddressline2 varchar(100),
    deliveryaddressline3 varchar(100),
    activitytimestamp timestamp,
    lastpickupbytimestamp timestamp,
    accountid varchar(50),
    businessunitid varchar(50),
    customerid varchar(50),
    warehouseid varchar(50),
    activitytypedescription varchar(50),
    activitystage varchar(50),
	CONSTRAINT trackingdelivery_id PRIMARY KEY (id)
);","Stores specific delivery event details for tracked shipments, including the activity location, the final delivery address, who signed for the package, and the last pickup date and time. It provides a snapshot of the completed delivery."
tracking.trackingexception,"CREATE TABLE tracking.trackingexception (
    id varchar(40) NOT NULL,
    carriertrackingid varchar(40),
    exceptionreasoncode varchar(4),
    exceptionreasondescription varchar(100),
    exceptionlocationname varchar(100),
    exceptionlocationstate varchar(100),
    exceptionlocationcountry varchar(100),
    exceptionstatuscode varchar(40),
    exceptionstatusdescription varchar(100),
    exceptionresolutiontype varchar(40),
    exceptionresolutiondescription varchar(100),
    rescheduleddeliverydate varchar(10),
    updatedaddressname varchar(100),
    updatedaddressbuildingname varchar(100),
    updatedaddresssuite varchar(100),
    updatedaddresscity varchar(100),
    updatedaddressstate varchar(100),
    updatedaddresspostalcode varchar(100),
    updatedaddresscountry varchar(100),
    exceptiondetails varchar(100),
    createdby varchar(320),
    createddate timestamp,
    modifiedby varchar(320),
    modifieddate timestamp,
    timeinepoch numeric,
    isdeleted bpchar,
    isarchived bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    updatedlocation varchar(200),
    exceptionlocationcity varchar(50),
    exceptionlocationpostalcode varchar(40),
    exceptionlocationlatitude varchar(40),
    exceptionlocationlongitude varchar(40),
    updatedaddresslatitude varchar(20),
    updatedaddresslongitude varchar(20),
    exceptiondate varchar(10),
    exceptiontime varchar(10),
    exceptiontimezone varchar(10),
    rescheduleddeliverytime varchar(10),
    rescheduleddeliverytimezone varchar(10),
    updatedaddressline1 varchar(100),
    updatedaddressline2 varchar(100),
    updatedaddressline3 varchar(100),
    exceptiontimestamp timestamp,
    rescheduleddeliverytimestamp timestamp,
    accountid varchar(50),
    businessunitid varchar(50),
    customerid varchar(50),
    warehouseid varchar(50),
    exceptionstage varchar(50),
	CONSTRAINT trackingexception_id PRIMARY KEY (id)
);","Records instances where a shipment tracking encounters an exception, providing details such as the reason, status, resolution type, location of the exception, and any updated address information due to the exception."
wave.itembusinessunit,"CREATE TABLE wave.itembusinessunit (
    id varchar(40) NOT NULL,
    createddate timestamp,
    createdby varchar(250),
    modifieddate timestamp,
    modifiedby varchar(250),
    timeinepoch numeric,
    businessunitid varchar(40),
    itemnumber varchar(40),
    description varchar(250),
    productgroupbusinessunitid varchar(40),
    isregulated bpchar,
    isdeleted bpchar,
    isarchived bpchar,
    isdefault bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    updatedlocation varchar(200),
    rategroup varchar(4),
    rategroupdescription varchar(40),
    productgroupdescription varchar(40),
    retailprice float4,
    color varchar(4),
    colordescription varchar(40),
    pattern varchar(4),
    patterndescription varchar(40),
    sizedescription varchar(40),
    gender varchar(4),
    genderdescription varchar(40),
    season varchar(4),
    seasondescription varchar(40),
    durability varchar(4),
    unitsperpickactive float4,
    unitsperpickreserve float4,
    productgroupcode varchar(40),
    itemsize varchar(40),
    listprice float8,
    shelflife numeric,
    title varchar(40),
    productname varchar(40),
    upc varchar(40),
    ean varchar(40),
    brandname varchar(40),
    gtin varchar(40),
    mpn varchar(40),
    nmfc varchar(40),
    nmfcarticle varchar(40),
    tmsproductcode varchar(40),
    extractid varchar(40),
    extlines varchar(40),
    condition varchar(40),
    producttype varchar(40),
	CONSTRAINT itembusinessunit_id PRIMARY KEY (id, id)
);","Defines various attributes of an item specific to a business unit, influencing how it's handled in warehouse operations (e.g., picking, packing). This includes descriptive details like color, size, and gender, as well as pricing, regulatory status, and identification codes (UPC, GTIN)."
wave.itemmaintenance,"CREATE TABLE wave.itemmaintenance (
    id varchar(40) NOT NULL,
    createddate timestamp,
    createdby varchar(250),
    modifieddate timestamp,
    modifiedby varchar(250),
    timeinepoch numeric,
    itembusinessunitid varchar(40),
    accountid varchar(40),
    businessunitid varchar(50),
    warehouseid varchar(50),
    customerid varchar(50),
    isvendortracked bpchar,
    reorderpoint float8,
    isshortdate bpchar,
    shortdateoffset int4,
    daysbeforeexpiration float8,
    isnoninventory bpchar,
    isnonasn bpchar,
    istopshelfeligible bpchar,
    rategroup varchar(40),
    expirationaction varchar(40),
    isdeleted bpchar,
    isarchived bpchar,
    isdefault bpchar,
    currentappversion varchar(7),
    modifiedappversion varchar(7),
    createdlocation varchar(200),
    updatedlocation varchar(200),
	CONSTRAINT itemmaintenance_id PRIMARY KEY (id, id)
);","Stores maintenance and inventory management settings for items, specific to an account, business unit, warehouse, or customer. This includes reorder points, expiration handling, and flags for inventory behavior."