Feature: IVF FAISS Store

  Scenario: Add and search documents
    Given an IVFFAISSStore instance
    When I add documents and search with a query embedding
    Then search results should be returned

  Scenario: Get stats with documents
    Given an IVFFAISSStore instance with documents
    When I get stats
    Then stats should include document count

  Scenario: Get stats with no documents
    Given an IVFFAISSStore instance
    When I get stats with no documents
    Then an error message should be returned

  Scenario: Save and load store
    Given an IVFFAISSStore instance with documents
    When I save and load the store
    Then the loaded store should have the same document count

  Scenario: Search with no index or documents
    Given an empty IVFFAISSStore instance
    When I search with a query embedding
    Then no results or exception results should be returned

  Scenario: Compress and decompress document
    Given an IVFFAISSStore instance
    When I compress and decompress a document
    Then the original document should be recovered 