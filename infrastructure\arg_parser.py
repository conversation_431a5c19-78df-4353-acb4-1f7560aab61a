"""This module provides a command-line argument parser for the application."""

import argparse
import os
from typing import Any, Dict


def parse_args() -> Dict[str, Any]:
    """Parse command-line arguments for the application."""
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--config-file-path",
        default="profiles/dev/api/appsettings.json",
        help="Path to the environment configuration file",
    )
    args = parser.parse_args()

    os.environ["CONFIG_FILE_PATH"] = args.config_file_path

    return vars(args)
