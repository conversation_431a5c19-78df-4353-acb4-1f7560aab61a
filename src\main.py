"""Entry point for the cartonization service.

This module can be used to launch or test the cartonization application.
"""

import os
import sys

project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from infrastructure.bootstrap import bootstrap_application  # noqa: E402


def main():
    """Main function to run the cartonization service.
    It sets up the environment and starts the Uvicorn server with the application.
    The server will reload automatically on code changes.
    """
    bootstrap_application()


if __name__ == "__main__":
    main()
