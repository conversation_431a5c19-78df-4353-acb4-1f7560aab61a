"""Unit tests for ConversationStore and conversation management."""

import os
import sys

sys.path.insert(
    0, os.path.abspath(os.path.join(os.path.dirname(__file__), "../../"))
)

import shutil
import tempfile
import unittest
from unittest.mock import mock_open, patch

from langchain_core.messages import AIMessage, HumanMessage

from src.database.conversations import ConversationStore


class TestConversationStore(unittest.TestCase):
    """Test cases for ConversationStore class."""

    def setUp(self):
        """Set up a temporary directory and ConversationStore instance."""
        self.temp_dir = tempfile.mkdtemp()
        self.store = ConversationStore(storage_path=self.temp_dir)

    def tearDown(self):
        """Remove the temporary directory after each test."""
        shutil.rmtree(self.temp_dir)

    def test_create_and_list_conversation(self):
        """Test creating and listing a conversation."""
        conv_id = self.store.create_conversation("Test Conversation")
        conversations = self.store.list_conversations()
        self.assertEqual(len(conversations), 1)
        self.assertEqual(conversations[0]["id"], conv_id)
        self.assertEqual(conversations[0]["title"], "Test Conversation")

    def test_save_and_load_messages(self):
        """Test saving and loading messages in a conversation."""
        conv_id = self.store.create_conversation()
        messages = [
            HumanMessage(content="Hello"),
            AIMessage(content="Hi there!"),
        ]
        success = self.store.save_messages(conv_id, messages)
        self.assertTrue(success)
        loaded = self.store.load_messages(conv_id)
        self.assertEqual(len(loaded), 2)
        self.assertEqual(loaded[0].content, "Hello")
        self.assertEqual(loaded[1].content, "Hi there!")

    def test_update_conversation_title(self):
        """Test updating the title of a conversation."""
        conv_id = self.store.create_conversation("Old Title")
        updated = self.store.update_conversation_title(conv_id, "New Title")
        self.assertTrue(updated)
        conversations = self.store.list_conversations()
        self.assertEqual(conversations[0]["title"], "New Title")

    def test_delete_conversation(self):
        """Test deleting a conversation."""
        conv_id = self.store.create_conversation()
        deleted = self.store.delete_conversation(conv_id)
        self.assertTrue(deleted)
        conversations = self.store.list_conversations()
        self.assertEqual(len(conversations), 0)

    def test_save_messages_invalid_id(self):
        """Test saving messages to a non-existent conversation."""
        messages = [HumanMessage(content="Hello")]
        success = self.store.save_messages("invalid_id", messages)
        self.assertFalse(success)

    def test_load_messages_invalid_id(self):
        """Test loading messages from a non-existent conversation."""
        loaded = self.store.load_messages("invalid_id")
        self.assertEqual(loaded, [])

    def test_update_conversation_title_invalid_id(self):
        """Test updating the title of a non-existent conversation."""
        updated = self.store.update_conversation_title("invalid_id", "Title")
        self.assertFalse(updated)

    def test_delete_conversation_invalid_id(self):
        """Test deleting a non-existent conversation."""
        deleted = self.store.delete_conversation("invalid_id")
        self.assertFalse(deleted)

    @patch(
        "src.database.conversations.os.remove",
        side_effect=Exception("remove error"),
    )
    def test_delete_conversation_exception(self, mock_remove):
        """Test exception handling when deleting a conversation."""
        conv_id = "fakeid"
        deleted = self.store.delete_conversation(conv_id)
        self.assertFalse(deleted)

    @patch("builtins.open", new_callable=mock_open)
    @patch("json.load", side_effect=Exception("load error"))
    def test__load_conversation_exception(
        self, mock_json_load, mock_open_file, mock_exists
    ):
        """Test exception handling when loading a conversation."""
        store = ConversationStore(storage_path=self.temp_dir)
        with patch("os.path.exists", return_value=True):
            result = store._load_conversation("fakeid")
            self.assertIsNone(result)

    @patch("builtins.open", new_callable=mock_open)
    @patch("json.dump", side_effect=Exception("dump error"))
    def test__save_conversation_exception(self, mock_json_dump, mock_open_file):
        """Test exception handling when saving a conversation."""
        store = ConversationStore(storage_path=self.temp_dir)
        try:
            store._save_conversation("fakeid", {})
        except Exception:
            self.fail("_save_conversation should not raise an exception")

    @patch(
        "src.database.conversations.os.listdir",
        side_effect=Exception("listdir error"),
    )
    def test_list_conversations_exception(self, mock_listdir):
        """Test exception handling when listing conversations."""
        store = ConversationStore(storage_path=self.temp_dir)
        result = store.list_conversations()
        self.assertEqual(result, [])

    @patch(
        "src.database.conversations.ConversationStore._load_conversation",
        return_value=None,
    )
    def test_update_conversation_title_no_conversation(self, mock_load):
        """Test updating the title when conversation is not found."""
        store = ConversationStore(storage_path=self.temp_dir)
        result = store.update_conversation_title("fakeid", "title")
        self.assertFalse(result)

    @patch(
        "src.database.conversations.ConversationStore._load_conversation",
        side_effect=Exception("load error"),
    )
    def test_update_conversation_title_exception(self, mock_load):
        """Test exception handling when updating conversation title."""
        store = ConversationStore(storage_path=self.temp_dir)
        result = store.update_conversation_title("fakeid", "title")
        self.assertFalse(result)

    @patch("src.database.conversations.os.path.exists", return_value=True)
    @patch(
        "src.database.conversations.os.remove",
        side_effect=Exception("remove error"),
    )
    def test_delete_conversation_os_remove_exception(
        self, mock_remove, mock_exists
    ):
        """Test exception handling when os.remove raises an error."""
        store = ConversationStore(storage_path=self.temp_dir)
        result = store.delete_conversation("fakeid")
        self.assertFalse(result)


if __name__ == "__main__":
    unittest.main()
