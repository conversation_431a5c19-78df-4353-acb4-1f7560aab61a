"""Controller for handling ask-related API endpoints."""

from litestar import Controller, Request, post

from src.api.mappers import (  # Use schemas directly for now
    ask_question_from_data,
)


class Ask<PERSON><PERSON>roller(Controller):
    """Controller for the /ask endpoint."""

    path = "/ask"

    @post("/")
    async def ask_question(self, request: Request) -> dict:
        """Handle a question asked by the user and return a response."""
        data = await request.json()
        return ask_question_from_data(data)
