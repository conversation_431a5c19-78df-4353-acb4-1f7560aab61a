"""Global exception handler for the RAG API (RFC 9457-compliant).

This module defines a handler that catches all unhandled exceptions in the Litestar app and returns a standardized problem+json response, logging the error for observability.

Compliant with RFC 9457 (obsoletes RFC 7807):
- Uses absolute URI for 'type' if available, otherwise 'about:blank'.
- Sets 'title' to HTTP status phrase if 'type' is 'about:blank'.
- Allows extension members (e.g., 'errors', 'code') via kwargs.
"""

from http import HTTPStatus

import loguru
from litestar import Request, Response
from litestar.status_codes import HTTP_500_INTERNAL_SERVER_ERROR

logger = loguru.logger


def global_exception_handler(
    request: Request, exc: Exception, **extensions
) -> Response:
    """Handle all uncaught exceptions and return a standardized RFC 9457 problem+json response.

    Args:
        request (Request): The incoming request object.
        exc (Exception): The exception that was raised.
        extensions: Optional extension members for the problem details object.

    Returns:
        Response: A Litestar Response object with problem+json content.
    """
    status_code = getattr(exc, "status_code", HTTP_500_INTERNAL_SERVER_ERROR)
    detail = getattr(exc, "detail", str(exc))
    method = request.method
    path = request.url.path
    # Use an absolute URI for type if available, else about:blank
    type_uri = getattr(exc, "type", None) or "about:blank"
    # Use HTTP status phrase for title if type is about:blank, else use exception class name
    if type_uri == "about:blank":
        try:
            title = HTTPStatus(status_code).phrase
        except Exception:
            title = exc.__class__.__name__
    else:
        title = getattr(exc, "title", exc.__class__.__name__)
    logger.error(f"Unhandled exception: {exc}", exc_info=True)
    problem_response = {
        "type": type_uri,
        "title": title,
        "status": status_code,
        "detail": detail,
        "instance": f"{method} {path}",
    }
    # Add extension members if provided
    problem_response.update(extensions)
    return Response(
        content=problem_response,
        status_code=status_code,
        media_type="application/problem+json",
    )
