"""Pytest configuration and session fixtures for the test suite."""

import os
import sys

sys.path.insert(
    0, os.path.abspath(os.path.join(os.path.dirname(__file__), ".."))
)
import shutil
import tempfile
from unittest.mock import patch

from src.database.conversations import ConversationStore

_temp_dir = tempfile.mkdtemp()
test_conversation_store = ConversationStore(storage_path=_temp_dir)
patcher_store = patch(
    "src.database.conversations.conversation_store", test_conversation_store
)
patcher_store.start()


def pytest_sessionfinish(session, exitstatus):
    """Clean up resources after the test session finishes."""
    patcher_store.stop()
    shutil.rmtree(_temp_dir)
