"""Mapping logic for TerraBLOOM RAG API."""

from typing import List

from src.api.schemas import (
    AskRequest,
    AskResponse,
    ConversationCreateRequest,
    ConversationListResponse,
    ConversationResponse,
    MessageListResponse,
    TableSchemaListResponse,
)
from src.api.schemas import (
    Conversation as ConversationSchema,
)
from src.api.schemas import (
    Message as MessageSchema,
)
from src.api.schemas import (
    TableSchema as TableSchemaSchema,
)
from src.api.service_container import service_container
from src.models.conversation import Conversation
from src.models.message import Message
from src.models.schema import TableSchema


def message_to_schema(message: Message) -> MessageSchema:
    """Convert internal Message model to API Message schema."""
    return MessageSchema(role=message.role, content=message.content)

def schema_to_message(schema: MessageSchema) -> Message:
    """Convert API Message schema to internal Message model."""
    return Message(role=schema.role, content=schema.content)

def conversation_to_schema(conversation: Conversation) -> ConversationSchema:
    """Convert internal Conversation model to API Conversation schema."""
    messages = [message_to_schema(msg) for msg in (conversation.messages or [])]
    return ConversationSchema(
        id=conversation.id,
        title=conversation.title,
        message_count=conversation.message_count,
        messages=messages,
    )

def schema_to_conversation(schema: ConversationSchema) -> Conversation:
    """Convert API Conversation schema to internal Conversation model."""
    messages = [schema_to_message(msg) for msg in (schema.messages or [])]
    return Conversation(
        id=schema.id,
        title=schema.title,
        message_count=schema.message_count,
        messages=messages,
    )

def table_schema_to_schema(table: TableSchema) -> TableSchemaSchema:
    """Convert internal TableSchema model to API TableSchema schema."""
    return TableSchemaSchema(table_name=table.table_name, ddl=table.ddl)

def schema_to_table_schema(schema: TableSchemaSchema) -> TableSchema:
    """Convert API TableSchema schema to internal TableSchema model."""
    return TableSchema(table_name=schema.table_name, ddl=schema.ddl)

def conversation_list_to_schema(conversations: List[Conversation]) -> ConversationListResponse:
    """Convert a list of Conversation models to ConversationListResponse schema."""
    return ConversationListResponse(conversations=conversations)

def message_list_to_schema(messages: List[Message]) -> MessageListResponse:
    """Convert a list of Message models to MessageListResponse schema."""
    return MessageListResponse(messages=messages)

def table_schema_list_to_schema(tables: List[TableSchema]) -> TableSchemaListResponse:
    """Convert a list of TableSchema models to TableSchemaListResponse schema."""
    return TableSchemaListResponse(schemas=tables)

def dict_to_conversation(conv) -> Conversation:
    """Convert a dict to a Conversation model, handling message conversion as needed."""
    if isinstance(conv, Conversation):
        return conv
    messages = conv.get("messages", [])
    msg_objs = [
        msg if isinstance(msg, Message) else Message(**msg)
        for msg in messages
    ]
    return Conversation(
        id=conv["id"],
        title=conv["title"],
        message_count=conv["message_count"],
        messages=msg_objs,
    )


def dict_list_to_conversations(conv_dicts) -> list[Conversation]:
    """Convert a list of dicts to a list of Conversation models."""
    return [dict_to_conversation(conv) for conv in conv_dicts]

def create_conversation_from_data(data) -> dict:
    """Create a conversation from request data and return a ConversationResponse as dict."""
    req = ConversationCreateRequest(**data)
    conv_id = service_container.get_rag_chain().create_conversation(req.title)
    return ConversationResponse(id=conv_id).model_dump()

def ask_question_from_data(data) -> dict:
    """Handle a question asked by the user and return an AskResponse as dict."""
    req = AskRequest(**data)
    rag_chain = service_container.get_rag_chain()
    # Only load conversation if switching to a different conversation
    if req.conversation_id:
        if rag_chain.active_conversation_id != req.conversation_id:
            loaded = rag_chain.load_conversation(req.conversation_id)
            if not loaded:
                return {"error": "Conversation not found"}
        conversation_id = req.conversation_id
    else:
        conversation_id = rag_chain.create_conversation()
    answer = rag_chain.ask_question(req.question)
    return AskResponse(
        answer=answer, conversation_id=conversation_id
    ).model_dump()
