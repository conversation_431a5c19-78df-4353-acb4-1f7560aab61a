Feature: Config Management # tests/bdd/config_management.feature:1

  Scenario: Config uses default values when no environment variables are set  # tests/bdd/config_management.feature:3
    Given no environment variables are set for config                         # tests/bdd/steps/config_management_steps.py:6
    When I create a Config instance                                           # tests/bdd/steps/config_management_steps.py:32
    Then the config should use default values                                 # tests/bdd/steps/config_management_steps.py:36

  Scenario: Config uses environment variable values when set   # tests/bdd/config_management.feature:8
    Given environment variables are set for config             # tests/bdd/steps/config_management_steps.py:16
    When I create a Config instance                            # tests/bdd/steps/config_management_steps.py:32
    Then the config should use the environment variable values # tests/bdd/steps/config_management_steps.py:43

  Scenario: Get database URL                 # tests/bdd/config_management.feature:13
    Given a Config instance                  # tests/bdd/steps/config_management_steps.py:50
    When I get the database URL              # tests/bdd/steps/config_management_steps.py:54
    Then it should be a valid PostgreSQL URL # tests/bdd/steps/config_management_steps.py:58

  Scenario: Set environment variables from config            # tests/bdd/config_management.feature:18
    Given a Config instance                                  # tests/bdd/steps/config_management_steps.py:50
    When I call set_environment_variables                    # tests/bdd/steps/config_management_steps.py:63
    Then the environment variables should be set accordingly # tests/bdd/steps/config_management_steps.py:67

  Scenario: Get foreign key patterns                      # tests/bdd/config_management.feature:23
    Given a Config instance                               # tests/bdd/steps/config_management_steps.py:50
    When I get the foreign key patterns                   # tests/bdd/steps/config_management_steps.py:73
    Then it should return a dictionary with expected keys # tests/bdd/steps/config_management_steps.py:77

Feature: Conversation Management # tests/bdd/conversation_management.feature:1

  Scenario: Create a new conversation and list it                                       # tests/bdd/conversation_management.feature:3
    Given I have a conversation store                                                   # tests/bdd/steps/conversation_management_steps.py:93
    When I create a new conversation with the title "Test Conversation"                 # tests/bdd/steps/rag_chain_steps.py:37
    Then the conversation list should include a conversation titled "Test Conversation" # tests/bdd/steps/conversation_management_steps.py:103

  Scenario: Save and load messages in a conversation                          # tests/bdd/conversation_management.feature:8
    Given I have a conversation store                                         # tests/bdd/steps/conversation_management_steps.py:93
    And I create a new conversation with the title "Message Test"             # tests/bdd/steps/conversation_management_steps.py:97
    When I save the messages ["Hello", "Hi"] to the conversation              # tests/bdd/steps/conversation_management_steps.py:115
    Then loading messages from the conversation should return ["Hello", "Hi"] # tests/bdd/steps/conversation_management_steps.py:122

  Scenario: Update conversation title                                           # tests/bdd/conversation_management.feature:14
    Given I have a conversation store                                           # tests/bdd/steps/conversation_management_steps.py:93
    And I create a new conversation with the title "Old Title"                  # tests/bdd/steps/conversation_management_steps.py:97
    When I update the conversation title to "New Title"                         # tests/bdd/steps/conversation_management_steps.py:129
    Then the conversation list should include a conversation titled "New Title" # tests/bdd/steps/conversation_management_steps.py:103

  Scenario: Delete a conversation                                                   # tests/bdd/conversation_management.feature:20
    Given I have a conversation store                                               # tests/bdd/steps/conversation_management_steps.py:93
    And I create a new conversation with the title "To Delete"                      # tests/bdd/steps/conversation_management_steps.py:97
    When I delete the conversation                                                  # tests/bdd/steps/conversation_management_steps.py:134
    Then the conversation list should not include a conversation titled "To Delete" # tests/bdd/steps/conversation_management_steps.py:109

  Scenario: Save messages to a non-existent conversation                     # tests/bdd/conversation_management.feature:26
    Given I have a conversation store                                        # tests/bdd/steps/conversation_management_steps.py:93
    When I try to save the messages ["Ghost"] to a non-existent conversation # tests/bdd/steps/conversation_management_steps.py:138
    Then saving should fail                                                  # tests/bdd/steps/conversation_management_steps.py:22

  Scenario: Update title of a non-existent conversation                         # tests/bdd/conversation_management.feature:31
    Given I have a conversation store                                           # tests/bdd/steps/conversation_management_steps.py:93
    When I try to update the title of a non-existent conversation to "No Title" # tests/bdd/steps/conversation_management_steps.py:38
    Then updating should fail                                                   # tests/bdd/steps/conversation_management_steps.py:34

  Scenario: Delete a non-existent conversation       # tests/bdd/conversation_management.feature:36
    Given I have a conversation store                # tests/bdd/steps/conversation_management_steps.py:93
    When I try to delete a non-existent conversation # tests/bdd/steps/conversation_management_steps.py:42
    Then deleting should fail                        # tests/bdd/steps/conversation_management_steps.py:50

  Scenario: Save messages to a non-existent conversation       # tests/bdd/conversation_management.feature:41
    Given a conversation store                                 # tests/bdd/steps/conversation_management_steps.py:10
    When I try to save messages to a non-existent conversation # tests/bdd/steps/conversation_management_steps.py:14
    Then saving should fail gracefully                         # tests/bdd/steps/conversation_management_steps.py:18

  Scenario: Update the title of a non-existent conversation       # tests/bdd/conversation_management.feature:46
    Given a conversation store                                    # tests/bdd/steps/conversation_management_steps.py:10
    When I try to update the title of a non-existent conversation # tests/bdd/steps/conversation_management_steps.py:26
    Then updating should fail gracefully                          # tests/bdd/steps/conversation_management_steps.py:30

  Scenario: Delete a non-existent conversation       # tests/bdd/conversation_management.feature:51
    Given a conversation store                       # tests/bdd/steps/conversation_management_steps.py:10
    When I try to delete a non-existent conversation # tests/bdd/steps/conversation_management_steps.py:42
    Then deleting should fail gracefully             # tests/bdd/steps/conversation_management_steps.py:46

  Scenario: Load messages from a corrupted conversation file    # tests/bdd/conversation_management.feature:56
    Given a corrupted conversation file exists                  # tests/bdd/steps/conversation_management_steps.py:54
    When I try to load messages from the corrupted conversation # tests/bdd/steps/conversation_management_steps.py:63
    Then loading should fail gracefully                         # tests/bdd/steps/conversation_management_steps.py:67

  Scenario: List conversations when a file is corrupted                         # tests/bdd/conversation_management.feature:61
    Given a conversation store with a corrupted file                            # tests/bdd/steps/conversation_management_steps.py:71
    When I list all conversations                                               # tests/bdd/steps/conversation_management_steps.py:79
    Then the corrupted file should be skipped and no exception should be raised # tests/bdd/steps/conversation_management_steps.py:88

Feature: Database Connection Management # tests/bdd/database_connection.feature:1

  Scenario: Initialize DatabaseManager successfully  # tests/bdd/database_connection.feature:3
    When I initialize the DatabaseManager            # tests/bdd/steps/database_connection_steps.py:5
    Then the engine and db should be set             # tests/bdd/steps/database_connection_steps.py:15

  Scenario: Handle database connection error             # tests/bdd/database_connection.feature:7
    When I initialize the DatabaseManager with a bad URL # tests/bdd/steps/database_connection_steps.py:20
    Then a connection error should be handled            # tests/bdd/steps/database_connection_steps.py:29

  Scenario: Execute a SQL query successfully  # tests/bdd/database_connection.feature:11
    Given a DatabaseManager instance          # tests/bdd/steps/database_connection_steps.py:33
    When I execute a valid SQL query          # tests/bdd/steps/database_connection_steps.py:43
    Then the result should be returned        # tests/bdd/steps/database_connection_steps.py:48

  Scenario: Execute a SQL query with error                     # tests/bdd/database_connection.feature:16
    Given a DatabaseManager instance                           # tests/bdd/steps/database_connection_steps.py:33
    When I execute an invalid SQL query                        # tests/bdd/steps/database_connection_steps.py:52
    Then an error message should be returned for db connection # tests/bdd/steps/database_connection_steps.py:57

  Scenario: Strip SQL markdown from query     # tests/bdd/database_connection.feature:21
    Given a DatabaseManager instance          # tests/bdd/steps/database_connection_steps.py:33
    When I strip SQL markdown from a query    # tests/bdd/steps/database_connection_steps.py:61
    Then the cleaned query should be returned # tests/bdd/steps/database_connection_steps.py:65

Feature: IVF FAISS Store # tests/bdd/ivf_faiss_store.feature:1

  Scenario: Add and search documents                       # tests/bdd/ivf_faiss_store.feature:3
    Given an IVFFAISSStore instance                        # tests/bdd/steps/ivf_faiss_store_steps.py:7
    When I add documents and search with a query embedding # tests/bdd/steps/ivf_faiss_store_steps.py:28
    Then search results should be returned                 # tests/bdd/steps/ivf_faiss_store_steps.py:40

  Scenario: Get stats with documents               # tests/bdd/ivf_faiss_store.feature:8
    Given an IVFFAISSStore instance with documents # tests/bdd/steps/ivf_faiss_store_steps.py:11
    When I get stats                               # tests/bdd/steps/ivf_faiss_store_steps.py:45
    Then stats should include document count       # tests/bdd/steps/ivf_faiss_store_steps.py:49

  Scenario: Get stats with no documents      # tests/bdd/ivf_faiss_store.feature:13
    Given an IVFFAISSStore instance          # tests/bdd/steps/ivf_faiss_store_steps.py:7
    When I get stats with no documents       # tests/bdd/steps/ivf_faiss_store_steps.py:54
    Then an error message should be returned # tests/bdd/steps/ivf_faiss_store_steps.py:58

  Scenario: Save and load store                               # tests/bdd/ivf_faiss_store.feature:18
    Given an IVFFAISSStore instance with documents            # tests/bdd/steps/ivf_faiss_store_steps.py:11
    When I save and load the store                            # tests/bdd/steps/ivf_faiss_store_steps.py:62
    Then the loaded store should have the same document count # tests/bdd/steps/ivf_faiss_store_steps.py:70

  Scenario: Search with no index or documents               # tests/bdd/ivf_faiss_store.feature:23
    Given an empty IVFFAISSStore instance                   # tests/bdd/steps/ivf_faiss_store_steps.py:24
    When I search with a query embedding                    # tests/bdd/steps/ivf_faiss_store_steps.py:74
    Then no results or exception results should be returned # tests/bdd/steps/ivf_faiss_store_extra_steps.py:32

  Scenario: Compress and decompress document       # tests/bdd/ivf_faiss_store.feature:28
    Given an IVFFAISSStore instance                # tests/bdd/steps/ivf_faiss_store_steps.py:7
    When I compress and decompress a document      # tests/bdd/steps/ivf_faiss_store_steps.py:82
    Then the original document should be recovered # tests/bdd/steps/ivf_faiss_store_steps.py:88

Feature: IVF FAISS Store Edge Cases # tests/bdd/ivf_faiss_store_extra.feature:1

  Scenario: Add documents with empty embeddings  # tests/bdd/ivf_faiss_store_extra.feature:3
    Given an IVFFAISSStore instance (extra)      # tests/bdd/steps/ivf_faiss_store_extra_steps.py:6
    When I add documents with empty embeddings   # tests/bdd/steps/ivf_faiss_store_extra_steps.py:10
    Then no documents should be added            # tests/bdd/steps/ivf_faiss_store_extra_steps.py:14

  Scenario: Add documents with no metadata             # tests/bdd/ivf_faiss_store_extra.feature:8
    Given an IVFFAISSStore instance (extra)            # tests/bdd/steps/ivf_faiss_store_extra_steps.py:6
    When I add documents with no metadata              # tests/bdd/steps/ivf_faiss_store_extra_steps.py:18
    Then documents should be added with empty metadata # tests/bdd/steps/ivf_faiss_store_extra_steps.py:24

  Scenario: Search with no index or documents               # tests/bdd/ivf_faiss_store_extra.feature:13
    Given an IVFFAISSStore instance (extra)                 # tests/bdd/steps/ivf_faiss_store_extra_steps.py:6
    When I search with a query embedding and no index       # tests/bdd/steps/ivf_faiss_store_extra_steps.py:28
    Then no results or exception results should be returned # tests/bdd/steps/ivf_faiss_store_extra_steps.py:32

  Scenario: Search with exception                           # tests/bdd/ivf_faiss_store_extra.feature:18
    Given an IVFFAISSStore instance (extra)                 # tests/bdd/steps/ivf_faiss_store_extra_steps.py:6
    When I search and an exception is raised                # tests/bdd/steps/ivf_faiss_store_extra_steps.py:36
    Then no results or exception results should be returned # tests/bdd/steps/ivf_faiss_store_extra_steps.py:32

  Scenario: Get stats with no documents                          # tests/bdd/ivf_faiss_store_extra.feature:23
    Given an IVFFAISSStore instance (extra)                      # tests/bdd/steps/ivf_faiss_store_extra_steps.py:6
    When I get stats with no documents                           # tests/bdd/steps/ivf_faiss_store_steps.py:54
    Then an error message should be returned for ivf faiss store # tests/bdd/steps/ivf_faiss_store_extra_steps.py:43

  Scenario: Save with exception                            # tests/bdd/ivf_faiss_store_extra.feature:28
    Given an IVFFAISSStore instance (extra)                # tests/bdd/steps/ivf_faiss_store_extra_steps.py:6
    When I save and an exception is raised                 # tests/bdd/steps/ivf_faiss_store_extra_steps.py:47
    Then saving should fail gracefully for ivf faiss store # tests/bdd/steps/ivf_faiss_store_extra_steps.py:53

  Scenario: Load with exception                             # tests/bdd/ivf_faiss_store_extra.feature:33
    When I load an IVFFAISSStore and an exception is raised # tests/bdd/steps/ivf_faiss_store_extra_steps.py:72
    Then loading should fail gracefully for ivf faiss store # tests/bdd/steps/ivf_faiss_store_extra_steps.py:83

  Scenario: Decompress with corrupted data  # tests/bdd/ivf_faiss_store_extra.feature:37
    Given an IVFFAISSStore instance (extra) # tests/bdd/steps/ivf_faiss_store_extra_steps.py:6
    When I decompress corrupted data        # tests/bdd/steps/ivf_faiss_store_extra_steps.py:57
    Then an exception should be handled     # tests/bdd/steps/ivf_faiss_store_extra_steps.py:68

Feature: LLM Chat Models # tests/bdd/llm_chat_models.feature:1

  Scenario: Initialize LLMManager successfully  # tests/bdd/llm_chat_models.feature:3
    When I initialize the LLMManager            # tests/bdd/steps/llm_chat_models_steps.py:5
    Then the LLM should be set                  # tests/bdd/steps/llm_chat_models_steps.py:11

  Scenario: Generate SQL query                             # tests/bdd/llm_chat_models.feature:7
    Given a LLMManager instance                            # tests/bdd/steps/llm_chat_models_steps.py:15
    When I generate a SQL query from a question and schema # tests/bdd/steps/llm_chat_models_steps.py:24
    Then a SQL query string should be returned             # tests/bdd/steps/llm_chat_models_steps.py:28

  Scenario: Format response                                           # tests/bdd/llm_chat_models.feature:12
    Given a LLMManager instance                                       # tests/bdd/steps/llm_chat_models_steps.py:15
    When I format a response from question, query, result, and schema # tests/bdd/steps/llm_chat_models_steps.py:33
    Then a formatted string should be returned                        # tests/bdd/steps/llm_chat_models_steps.py:37

  Scenario: Get LLM instance                 # tests/bdd/llm_chat_models.feature:17
    Given a LLMManager instance              # tests/bdd/steps/llm_chat_models_steps.py:15
    When I get the LLM instance              # tests/bdd/steps/llm_chat_models_steps.py:42
    Then the LLM instance should be returned # tests/bdd/steps/llm_chat_models_steps.py:46

Feature: RAGChain # tests/bdd/rag_chain.feature:1

  Scenario: Ask a valid question       # tests/bdd/rag_chain.feature:3
    Given a RAGChain instance          # tests/bdd/steps/rag_chain_steps.py:82
    When I ask a valid question        # tests/bdd/steps/rag_chain_steps.py:88
    Then a response should be returned # tests/bdd/steps/rag_chain_steps.py:96

  Scenario: Ask with empty question            # tests/bdd/rag_chain.feature:8
    Given a RAGChain instance                  # tests/bdd/steps/rag_chain_steps.py:82
    When I ask an empty question               # tests/bdd/steps/rag_chain_steps.py:101
    Then a validation error should be returned # tests/bdd/steps/rag_chain_steps.py:109

  Scenario: Ask with error in chain                       # tests/bdd/rag_chain.feature:13
    Given a RAGChain instance                             # tests/bdd/steps/rag_chain_steps.py:82
    When I ask a question and an error occurs             # tests/bdd/steps/rag_chain_steps.py:113
    Then an error message for RAGChain should be returned # tests/bdd/steps/rag_chain_steps.py:118

  Scenario: Get store stats       # tests/bdd/rag_chain.feature:18
    Given a RAGChain instance     # tests/bdd/steps/rag_chain_steps.py:82
    When I get store stats        # tests/bdd/steps/rag_chain_steps.py:122
    Then stats should be returned # tests/bdd/steps/rag_chain_steps.py:127

  Scenario: Create a conversation                  # tests/bdd/rag_chain.feature:23
    Given a RAGChain instance                      # tests/bdd/steps/rag_chain_steps.py:82
    When I create a conversation with title "Test" # tests/bdd/steps/rag_chain_steps.py:131
    Then a conversation ID should be returned      # tests/bdd/steps/rag_chain_steps.py:135

  Scenario: Load a conversation              # tests/bdd/rag_chain.feature:28
    Given a RAGChain instance                # tests/bdd/steps/rag_chain_steps.py:82
    When I load a conversation with ID "123" # tests/bdd/steps/rag_chain_steps.py:140
    Then loading should succeed              # tests/bdd/steps/rag_chain_steps.py:147

  Scenario: Save current conversation    # tests/bdd/rag_chain.feature:33
    Given a RAGChain instance            # tests/bdd/steps/rag_chain_steps.py:82
    When I save the current conversation # tests/bdd/steps/rag_chain_steps.py:51
    Then saving should succeed           # tests/bdd/steps/rag_chain_steps.py:151

  Scenario: Get all conversations                   # tests/bdd/rag_chain.feature:38
    Given a RAGChain instance                       # tests/bdd/steps/rag_chain_steps.py:82
    When I get all conversations                    # tests/bdd/steps/rag_chain_steps.py:155
    Then a list of conversations should be returned # tests/bdd/steps/rag_chain_steps.py:159

  Scenario: Update conversation title                               # tests/bdd/rag_chain.feature:43
    Given a RAGChain instance                                       # tests/bdd/steps/rag_chain_steps.py:82
    When I update the conversation title to "New Title" in RAGChain # tests/bdd/steps/rag_chain_steps.py:163
    Then updating should succeed                                    # tests/bdd/steps/rag_chain_steps.py:172

  Scenario: Delete a conversation              # tests/bdd/rag_chain.feature:48
    Given a RAGChain instance                  # tests/bdd/steps/rag_chain_steps.py:82
    When I delete a conversation with ID "123" # tests/bdd/steps/rag_chain_steps.py:176
    Then deleting should succeed               # tests/bdd/steps/rag_chain_steps.py:182

Feature: IVFFAISSRetriever # tests/bdd/retriever.feature:1

  Scenario: Load or create store successfully  # tests/bdd/retriever.feature:3
    When I initialize the IVFFAISSRetriever    # tests/bdd/steps/retriever_steps.py:6
    Then the store should be set               # tests/bdd/steps/retriever_steps.py:14

  Scenario: Handle error when loading store                   # tests/bdd/retriever.feature:7
    When I initialize the IVFFAISSRetriever and loading fails # tests/bdd/steps/retriever_steps.py:18
    Then a new store should be created                        # tests/bdd/steps/retriever_steps.py:26

  Scenario: Invoke with a query and return documents  # tests/bdd/retriever.feature:11
    Given an IVFFAISSRetriever instance               # tests/bdd/steps/retriever_steps.py:30
    When I invoke with a query that matches documents # tests/bdd/steps/retriever_steps.py:40
    Then documents should be returned                 # tests/bdd/steps/retriever_steps.py:44

  Scenario: Invoke with a query and get no results  # tests/bdd/retriever.feature:16
    Given an IVFFAISSRetriever instance             # tests/bdd/steps/retriever_steps.py:30
    When I invoke with a query that matches nothing # tests/bdd/steps/retriever_steps.py:50
    Then no documents should be returned            # tests/bdd/steps/retriever_steps.py:55

  Scenario: Invoke with a query and search raises error  # tests/bdd/retriever.feature:21
    Given an IVFFAISSRetriever instance                  # tests/bdd/steps/retriever_steps.py:30
    When I invoke and search raises an error             # tests/bdd/steps/retriever_steps.py:59
    Then no documents should be returned                 # tests/bdd/steps/retriever_steps.py:55

  Scenario: Get store stats               # tests/bdd/retriever.feature:26
    Given an IVFFAISSRetriever instance   # tests/bdd/steps/retriever_steps.py:30
    When I get store stats from retriever # tests/bdd/steps/retriever_steps.py:64
    Then stats should be returned         # tests/bdd/steps/rag_chain_steps.py:127

  Scenario: Invoke with a query that causes an exception in the retriever  # tests/bdd/retriever.feature:31
    Given an IVFFAISSRetriever instance with a faulty store                # tests/bdd/steps/retriever_steps.py:69
    When I invoke the retriever with a query that triggers an error        # tests/bdd/steps/retriever_steps.py:76
    Then no documents should be returned                                   # tests/bdd/steps/retriever_steps.py:55

  Scenario: Invoke with a query that returns results missing metadata fields       # tests/bdd/retriever.feature:36
    Given an IVFFAISSRetriever instance with incomplete metadata in results        # tests/bdd/steps/retriever_steps.py:84
    When I invoke the retriever with a query                                       # tests/bdd/steps/retriever_steps.py:92
    Then no exception should be raised and documents should be returned or skipped # tests/bdd/steps/retriever_steps.py:101

