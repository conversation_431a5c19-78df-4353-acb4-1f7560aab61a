"""BDD step definitions for LLM chat model features."""

from unittest.mock import <PERSON>Mock, patch

from behave import given, then, when

from src.llm.chat_models import LLMManager


@when("I initialize the LLMManager")
def step_when_init_llm_manager(context):
    """Initialize the LLMManager with a mocked ChatOpenAI."""
    with patch("src.llm.chat_models.ChatOpenAI") as mock_llm:
        mock_llm.return_value = MagicMock()
        context.llm_manager = LLMManager()


@then("the LLM should be set")
def step_then_llm_set(context):
    """Assert that the LLM is set in the LLMManager."""
    assert context.llm_manager.llm is not None


@given("a LLMManager instance")
def step_given_llm_manager(context):
    """Provide a LLMManager instance with mocked methods."""
    with patch("src.llm.chat_models.ChatOpenAI") as mock_llm:
        mock_llm.return_value = MagicMock()
        m = LLMManager()
        m.generate_query = MagicMock(invoke=lambda x: "SELECT 1")
        m.rephrase_answer = MagicMock(invoke=lambda x: "Formatted")
        context.llm_manager = m


@when("I generate a SQL query from a question and schema")
def step_when_gen_sql(context):
    """Generate a SQL query from a question and schema using the LLMManager."""
    context.sql_query = context.llm_manager.generate_sql_query(
        "What is foo?", "schema"
    )


@then("a SQL query string should be returned")
def step_then_sql_query(context):
    """Assert that a SQL query string is returned."""
    assert isinstance(context.sql_query, str)
    assert context.sql_query


@when("I format a response from question, query, result, and schema")
def step_when_format_resp(context):
    """Format a response using the LLMManager from question, query, result, and.

    schema.
    """
    context.formatted = context.llm_manager.format_response("q", "q", "r", "s")


@then("a formatted string should be returned")
def step_then_formatted(context):
    """Assert that a formatted string is returned."""
    assert isinstance(context.formatted, str)
    assert context.formatted


@when("I get the LLM instance")
def step_when_get_llm(context):
    """Get the LLM instance from the LLMManager."""
    context.llm_instance = context.llm_manager.get_llm()


@then("the LLM instance should be returned")
def step_then_llm_instance(context):
    """Assert that the LLM instance is returned and not None."""
    assert context.llm_instance is not None
