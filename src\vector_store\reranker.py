"""Reranker module for document ranking in RAG pipeline.

This module provides the Reranker class, which uses a cross-encoder model to score and rank documents based on their relevance to a query.
"""

from typing import List

import numpy as np
from sentence_transformers import CrossEncoder

from src.config.config import config


class Reranker:
    """Reranker class for ranking documents using a cross-encoder model.

    Attributes:
        model (CrossEncoder): The cross-encoder model used for scoring.
    """

    def __init__(self, model_name: str = config.RERANKER_MODEL):
        """Initialize the Reranker with a specified model name.

        Args:
            model_name (str): Name of the cross-encoder model to use.
        """
        self.model = CrossEncoder(model_name)

    def predict_scores(self, query: str, documents: List[str], k: int = 10):
        """Predict relevance scores for a query and a list of documents.

        Args:
            query (str): The query string.
            documents (List[str]): List of document strings to score.
            k (int, optional): Number of top documents to consider. Defaults to 10.

        Returns:
            np.ndarray: Array of relevance scores for each document.
        """
        scores = self.model.predict([(query, passage) for passage in documents])
        return scores

    def rerank(
        self, query: str, documents: List[str], k: int = config.RERANKER_K
    ):
        """Rerank documents for a given query and return indices of top k documents.

        Args:
            query (str): The query string.
            documents (List[str]): List of document strings to rank.
            k (int, optional): Number of top documents to return. Defaults to config.RERANKER_K.

        Returns:
            np.ndarray: Indices of the top k ranked documents.
        """
        scores = self.predict_scores(query, documents)
        sorted_indices = np.argsort(scores)[::-1]
        return sorted_indices[:k]
