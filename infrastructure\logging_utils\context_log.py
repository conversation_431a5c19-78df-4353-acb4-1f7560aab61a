"""
Loguru filter to add request context to logs.

This module provides a filter that adds the current request context to all log messages.
"""

import json
from datetime import datetime
from typing import Any, Dict

from infrastructure.middleware.log_enricher import request_context


def enrich_log_with_context(record: Dict[str, Any]) -> bool:
    """Enrich log records with the current request context.

    This is a Loguru filter function that adds fields from the current request
    context to all log records.

    Args:
        record: The log record to enrich

    Returns:
        bool: Always True (to include the record)
    """
    # Get the current context
    context = request_context.get()

    # Add context to the "extra" field if it exists
    if context:
        # Add all context fields to the record
        for key, value in context.items():
            record["extra"][key] = value

    return True


def format_structured_log(record: Dict[str, Any]) -> str:
    """Format log records as structured JSON.

    Args:
        record: The log record to format

    Returns:
        str: JSON formatted log record
    """
    # Extract standard fields
    log_data = {
        "timestamp": record["time"].isoformat(),
        "level": record["level"].name,
        "message": record["message"],
    }

    # Add context from record["extra"]
    for key, value in record["extra"].items():
        if key not in log_data and value is not None:
            # Handle datetime objects
            if isinstance(value, datetime):
                log_data[key] = value.isoformat()
            else:
                log_data[key] = value

    # Add exception info if present
    if record["exception"]:
        log_data["exception"] = record["exception"]

    # Return as JSON
    return json.dumps(log_data)
