Feature: IVF FAISS Store Edge Cases

  Scenario: Add documents with empty embeddings
    Given an IVFFAISSStore instance (extra)
    When I add documents with empty embeddings
    Then no documents should be added

  Scenario: Add documents with no metadata
    Given an IVFFAISSStore instance (extra)
    When I add documents with no metadata
    Then documents should be added with empty metadata

  Sc<PERSON>rio: Search with no index or documents
    Given an IVFFAISSStore instance (extra)
    When I search with a query embedding and no index
    Then no results or exception results should be returned

  Scenario: Search with exception
    Given an IVFFAISSStore instance (extra)
    When I search and an exception is raised
    Then no results or exception results should be returned

  Scenario: Get stats with no documents
    Given an IVFFAISSStore instance (extra)
    When I get stats with no documents
    Then an error message should be returned for ivf faiss store

  Scenario: Save with exception
    Given an IVFFAISSStore instance (extra)
    When I save and an exception is raised
    Then saving should fail gracefully for ivf faiss store

  Scenario: Load with exception
    When I load an IVFFAISSStore and an exception is raised
    Then loading should fail gracefully for ivf faiss store

  Scenario: Decompress with corrupted data
    Given an IVFFAISSStore instance (extra)
    When I decompress corrupted data
    Then an exception should be handled 