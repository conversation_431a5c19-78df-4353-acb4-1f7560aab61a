"""Structured logging utility for logging messages with various parameters.
This module provides a function to log structured messages using the loguru logger.
"""

import inspect
from typing import Any, Dict, Optional

from loguru import logger

from .application_log_model import ApplicationLogMessage
from .json_encoder import json_dumps


def log_structured(
    level: str,
    message: str,
    input_params=None,
    output_params=None,
    additional_props=None,
    folder=None,
    route_id=None,
    route_name=None,
    custom_links=None,
    custom_timings_json=None,
    exception: Exception = None,
    context: Optional[Dict[str, Any]] = None,
):
    """Logs a structured message with various parameters.

    :param level: Log level (e.g., 'info', 'error').
    :param message: The message to log.
    :param input_params: Input parameters to log.
    :param output_params: Output parameters to log.
    :param additional_props: Additional properties to log.
    :param folder: Folder key-value to log.
    :param route_id: Route ID to log.
    :param route_name: Route name to log.
    :param custom_links: Custom links to log.
    :param custom_timings_json: Custom timings in JSON format to log.
    :param exception: Exception to log, if any.
    """
    input_params = input_params or {}
    output_params = output_params or {}
    additional_props = additional_props or {}
    custom_links = custom_links or {}

    log_msg = ApplicationLogMessage()
    log_msg.MethodName = inspect.stack()[1].function
    log_msg.Message = message
    log_msg.InputParams = input_params
    log_msg.OutPutParams = output_params
    log_msg.AppMetrics.RouteId = route_id
    log_msg.AppMetrics.RouteName = route_name
    log_msg.AppMetrics.CustomLinks = custom_links
    log_msg.AppMetrics.CustomTimingsJson = custom_timings_json
    log_msg.AppMetrics.end()
    log_msg.AdditionalProperties = additional_props
    log_msg.FolderKeyValue = folder

    app_message_dict = log_msg.to_dict()

    # Create a custom JSON string where context fields are at root level
    # and application message fields are nested under "message"
    log_data = {}

    log_data["Level"] = level.upper()

    # First add context values to the root level
    if context:
        for key, value in context.items():
            if value is not None and key != "log_level":
                log_data[key] = value

    # Then add application message as a nested object
    log_data["message"] = app_message_dict

    # Convert to JSON
    json_log = json_dumps(log_data)

    logger.log(level.upper(), json_log)
