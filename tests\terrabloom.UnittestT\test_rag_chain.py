"""Unit tests for the RAG chain functionality."""

import os
import sys

sys.path.insert(
    0, os.path.abspath(os.path.join(os.path.dirname(__file__), "../../"))
)

import shutil
import tempfile
import unittest
from unittest.mock import Mock, patch

from langchain_core.messages import AIMessage, HumanMessage

from src.database.conversations import ConversationStore
from src.rag.chain import RAGChain

_temp_dir = tempfile.mkdtemp()
test_conversation_store = ConversationStore(storage_path=_temp_dir)
patcher_store = patch(
    "src.database.conversations.conversation_store", test_conversation_store
)
patcher_store.start()


def teardown_module(module):
    """Tear down resources after the module tests complete."""
    patcher_store.stop()
    shutil.rmtree(_temp_dir)


class TestRAGChain(unittest.TestCase):
    """Test cases for RAGChain class."""

    @patch("src.rag.chain.IVFFAISSRetriever")
    @patch("src.rag.chain.db_manager")
    @patch("src.rag.chain.llm_manager")
    def setUp(self, mock_llm_manager, mock_db_manager, mock_retriever_class):
        """Set up test fixtures with mocked dependencies."""
        self.mock_retriever = Mock()
        self.mock_db_manager = Mock()
        self.mock_llm_manager = Mock()

        mock_retriever_class.return_value = self.mock_retriever
        mock_db_manager.return_value = self.mock_db_manager
        mock_llm_manager.return_value = self.mock_llm_manager

        self.rag_chain = RAGChain()

    def test_initialization(self):
        """Test RAGChain initialization."""
        self.assertIsNotNone(self.rag_chain.retriever)
        self.assertEqual(self.rag_chain.chat_history, [])
        self.assertIsNotNone(self.rag_chain.chain)

    def test_get_schema_context(self):
        """Test schema context retrieval."""
        # Mock documents
        mock_doc1 = Mock()
        mock_doc1.page_content = "CREATE TABLE customers"
        mock_doc2 = Mock()
        mock_doc2.page_content = "CREATE TABLE orders"

        self.mock_retriever.invoke.return_value = [mock_doc1, mock_doc2]

        context = self.rag_chain._get_schema_context(
            {"question": "test question"}
        )
        expected = "CREATE TABLE customers\nCREATE TABLE orders"
        self.assertEqual(context, expected)

    @patch("src.rag.chain.llm_manager")
    def test_generate_query(self, mock_llm_manager):
        """Test SQL query generation."""
        mock_llm_manager.generate_sql_query.return_value = (
            "SELECT * FROM customers"
        )

        inputs = {
            "question": "Show all customers",
            "schema": "CREATE TABLE customers",
            "chat_history": [],
        }

        query = self.rag_chain._generate_query(inputs)
        self.assertEqual(query, "SELECT * FROM customers")
        mock_llm_manager.generate_sql_query.assert_called_once()

    @patch("src.rag.chain.db_manager")
    def test_safe_execute_query_success(self, mock_db_manager):
        """Test successful query execution."""
        mock_db_manager.execute_query.return_value = "Query result"

        result = self.rag_chain._safe_execute_query("SELECT * FROM customers")
        self.assertEqual(result, "Query result")

    @patch("src.rag.chain.db_manager")
    def test_safe_execute_query_exception(self, mock_db_manager):
        """Test query execution with exception."""
        mock_db_manager.execute_query.side_effect = Exception("Database error")

        result = self.rag_chain._safe_execute_query("SELECT * FROM customers")
        self.assertIn("Error executing query", result)
        self.assertIn("Database error", result)

    @patch("src.rag.chain.llm_manager")
    def test_format_response(self, mock_llm_manager):
        """Test response formatting."""
        mock_llm_manager.format_response.return_value = "Formatted response"

        inputs = {
            "question": "test question",
            "query": "SELECT * FROM customers",
            "result": "query result",
            "schema": "CREATE TABLE customers",
        }

        response = self.rag_chain._format_response(inputs)
        self.assertEqual(response, "Formatted response")

    @patch("src.rag.chain.RAGChain._setup_chain")
    def test_ask_question(self, mock_setup_chain):
        """Test asking a question."""
        # Mock the chain
        mock_chain = Mock()
        mock_chain.invoke.return_value = "Answer to question"
        self.rag_chain.chain = mock_chain

        response = self.rag_chain.ask_question("What is the total sales?")

        self.assertEqual(response, "Answer to question")
        # Only one message (HumanMessage) is added to chat_history in the current
        # implementation
        self.assertEqual(len(self.rag_chain.chat_history), 1)
        self.assertIsInstance(self.rag_chain.chat_history[0], HumanMessage)

    @patch("src.rag.chain.RAGChain._setup_chain")
    def test_ask_question_value_error(self, mock_setup_chain):
        """Test ask_question handles ValueError and appends AIMessage."""
        # No question provided (empty string)
        response = self.rag_chain.ask_question("")
        self.assertIn("Validation error", response)
        self.assertIsInstance(self.rag_chain.chat_history[-1], AIMessage)
        self.assertIn(
            "Validation error", self.rag_chain.chat_history[-1].content
        )

    @patch("src.rag.chain.RAGChain._setup_chain")
    def test_ask_question_generic_exception(self, mock_setup_chain):
        """Test ask_question handles generic Exception and appends AIMessage."""
        # Simulate chain.invoke raising an exception
        mock_chain = Mock()
        mock_chain.invoke.side_effect = Exception("Something went wrong")
        self.rag_chain.chain = mock_chain
        response = self.rag_chain.ask_question("What is the total sales?")
        self.assertIn("Error processing question", response)
        self.assertIsInstance(self.rag_chain.chat_history[-1], AIMessage)
        self.assertIn(
            "Error processing question", self.rag_chain.chat_history[-1].content
        )

    def test_clear_history(self):
        """Test clearing chat history."""
        self.rag_chain.chat_history = [HumanMessage(content="test")]
        self.rag_chain.clear_history()
        self.assertEqual(self.rag_chain.chat_history, [])

    def test_get_store_stats(self):
        """Test getting vector store statistics."""
        self.mock_retriever.get_store_stats.return_value = {"total_docs": 100}

        stats = self.rag_chain.get_store_stats()
        self.assertEqual(stats, {"total_docs": 100})
        self.mock_retriever.get_store_stats.assert_called_once()

    @patch("src.rag.chain.conversation_store")
    def test_create_conversation(self, mock_conversation_store):
        """Test creating a new conversation."""
        mock_conversation_store.create_conversation.return_value = "conv123"
        conv_id = self.rag_chain.create_conversation("Test Title")
        self.assertEqual(conv_id, "conv123")
        self.assertEqual(self.rag_chain.active_conversation_id, "conv123")
        self.assertEqual(self.rag_chain.chat_history, [])
        mock_conversation_store.create_conversation.assert_called_with(
            "Test Title"
        )

    @patch("src.rag.chain.conversation_store")
    def test_load_conversation_success(self, mock_conversation_store):
        """Test loading a conversation successfully."""
        messages = [HumanMessage(content="hi"), AIMessage(content="hello")]
        mock_conversation_store.load_messages.return_value = messages
        result = self.rag_chain.load_conversation("conv123")
        self.assertTrue(result)
        self.assertEqual(self.rag_chain.chat_history, messages)
        self.assertEqual(self.rag_chain.active_conversation_id, "conv123")
        mock_conversation_store.load_messages.assert_called_with("conv123")

    @patch("src.rag.chain.conversation_store")
    def test_load_conversation_failure(self, mock_conversation_store):
        """Test loading a conversation that does not exist."""
        mock_conversation_store.load_messages.return_value = []
        result = self.rag_chain.load_conversation("conv123")
        self.assertFalse(result)
        self.assertNotEqual(self.rag_chain.active_conversation_id, "conv123")

    @patch("src.rag.chain.conversation_store")
    def test_save_current_conversation(self, mock_conversation_store):
        """Test saving the current conversation."""
        self.rag_chain.active_conversation_id = "conv123"
        self.rag_chain.chat_history = [HumanMessage(content="hi")]
        mock_conversation_store.save_messages.return_value = True
        result = self.rag_chain.save_current_conversation()
        self.assertTrue(result)
        mock_conversation_store.save_messages.assert_called_with(
            "conv123", self.rag_chain.chat_history
        )

    @patch("src.rag.chain.conversation_store")
    def test_save_current_conversation_creates_new(
        self, mock_conversation_store
    ):
        """Test saving a conversation when there is no active conversation ID."""
        self.rag_chain.active_conversation_id = None
        self.rag_chain.chat_history = [HumanMessage(content="hi")]
        mock_conversation_store.create_conversation.return_value = "newid"
        mock_conversation_store.save_messages.return_value = True
        result = self.rag_chain.save_current_conversation()
        self.assertTrue(result)
        self.assertEqual(self.rag_chain.active_conversation_id, "newid")
        mock_conversation_store.save_messages.assert_called_with(
            "newid", self.rag_chain.chat_history
        )

    @patch("src.rag.chain.conversation_store")
    def test_get_all_conversations(self, mock_conversation_store):
        """Test retrieving all conversations."""
        mock_conversation_store.list_conversations.return_value = [
            {"id": "1", "title": "A"}
        ]
        result = self.rag_chain.get_all_conversations()
        self.assertEqual(result, [{"id": "1", "title": "A"}])
        mock_conversation_store.list_conversations.assert_called_once()

    @patch("src.rag.chain.conversation_store")
    def test_update_conversation_title_success(self, mock_conversation_store):
        """Test updating the title of the active conversation."""
        self.rag_chain.active_conversation_id = "conv123"
        mock_conversation_store.update_conversation_title.return_value = True
        result = self.rag_chain.update_conversation_title("New Title")
        self.assertTrue(result)
        mock_conversation_store.update_conversation_title.assert_called_with(
            "conv123", "New Title"
        )

    @patch("src.rag.chain.conversation_store")
    def test_update_conversation_title_no_active(self, mock_conversation_store):
        """Test updating the title when there is no active conversation."""
        self.rag_chain.active_conversation_id = None
        result = self.rag_chain.update_conversation_title("New Title")
        self.assertFalse(result)
        mock_conversation_store.update_conversation_title.assert_not_called()

    @patch("src.rag.chain.conversation_store")
    def test_delete_conversation_active(self, mock_conversation_store):
        """Test deleting the active conversation."""
        self.rag_chain.active_conversation_id = "conv123"
        self.rag_chain.chat_history = [HumanMessage(content="hi")]
        mock_conversation_store.delete_conversation.return_value = True
        result = self.rag_chain.delete_conversation("conv123")
        self.assertTrue(result)
        self.assertIsNone(self.rag_chain.active_conversation_id)
        self.assertEqual(self.rag_chain.chat_history, [])
        mock_conversation_store.delete_conversation.assert_called_with(
            "conv123"
        )

    @patch("src.rag.chain.conversation_store")
    def test_delete_conversation_non_active(self, mock_conversation_store):
        """Test deleting a non-active conversation."""
        self.rag_chain.active_conversation_id = "otherid"
        self.rag_chain.chat_history = [HumanMessage(content="hi")]
        mock_conversation_store.delete_conversation.return_value = True
        result = self.rag_chain.delete_conversation("conv123")
        self.assertTrue(result)
        self.assertEqual(self.rag_chain.active_conversation_id, "otherid")
        self.assertEqual(
            self.rag_chain.chat_history, [HumanMessage(content="hi")]
        )
        mock_conversation_store.delete_conversation.assert_called_with(
            "conv123"
        )


if __name__ == "__main__":
    unittest.main()
