"""API application entry point module for the RAG system.

This module sets up the Litestar app, initializes core services via the ServiceContainer,
and provides dependency injection for controllers.
"""

import os
from litestar import Litestar
from litestar.contrib.opentelemetry import (
    OpenTelemetryConfig,
    OpenTelemetryPlugin,
)
from litestar.di import Provide
from litestar.middleware import DefineMiddleware
from litestar.plugins.prometheus import <PERSON>metheusConfig, PrometheusController
from loguru import logger
from opentelemetry import trace
from opentelemetry.exporter.otlp.proto.grpc.trace_exporter import OTLPSpanExporter
from opentelemetry.sdk.trace import TracerProvider
from opentelemetry.sdk.trace.export import BatchSpanProcessor

from infrastructure.bootstrap import bootstrapper
from infrastructure.controllers.admin_controller import AdminController
from infrastructure.controllers.health_controller import HealthController
from infrastructure.handler import global_exception_handler
from infrastructure.middleware.context_middleware import (
    ApplicationContextMiddleware,
)
from infrastructure.middleware.log_enricher import LogEnricherMiddleware
from infrastructure.middleware.logrequest import LogRequestBodyMiddleware
from infrastructure.config import ConfigService
from src.api.controllers.ask import <PERSON><PERSON><PERSON>roller
from src.api.controllers.config import ConfigController
from src.api.controllers.conversation import ConversationController
from src.api.controllers.schema import SchemaController
from src.api.service_container import service_container
from src.config.config import Config
from src.database.connection import DatabaseManager
from src.llm.chat_models import LLMManager
from src.vector_store.retriever import IVFFAISSRetriever


def setup_opentelemetry():
    """Set up OpenTelemetry tracing manually."""
    # Configure OpenTelemetry with proper OTLP exporter
    otlp_endpoint = os.getenv("OTEL_EXPORTER_OTLP_ENDPOINT", "http://localhost:4317")
    service_name = os.getenv("OTEL_SERVICE_NAME", "terrabloom-rag-api")
    
    logger.info(f"Configuring OpenTelemetry - Service: {service_name}, Endpoint: {otlp_endpoint}")
    
    # Create OTLP exporter
    otlp_exporter = OTLPSpanExporter(
        endpoint=otlp_endpoint,
        insecure=True
    )
    
    # Set up tracer provider
    tracer_provider = TracerProvider()
    trace.set_tracer_provider(tracer_provider)
    
    # Add span processor
    span_processor = BatchSpanProcessor(otlp_exporter)
    tracer_provider.add_span_processor(span_processor)


def initialize_app_services() -> None:
    """Initialize all core services in the ServiceContainer at app startup."""
    logger.info("Initializing OpenTelemetry tracing...")
    logger.info(f"OTLP endpoint: {os.getenv('OTEL_EXPORTER_OTLP_ENDPOINT', 'http://localhost:4317')}")
    setup_opentelemetry()
    service_container.initialize()


def get_llm_manager() -> LLMManager:
    """Dependency provider for LLMManager from the ServiceContainer."""
    return service_container.get_llm_manager()


def get_db_manager() -> DatabaseManager:
    """Dependency provider for DatabaseManager from the ServiceContainer."""
    return service_container.get_db_manager()


def get_retriever() -> IVFFAISSRetriever:
    """Dependency provider for IVFFAISSRetriever from the ServiceContainer."""
    return service_container.get_retriever()


def get_config() -> ConfigService:
    """Dependency provider for Config from the ServiceContainer."""
    return service_container.get_config()


class CustomPrometheusController(PrometheusController):
    """Custom Prometheus controller to expose metrics at a specific path."""

    path = "/admin/metrics"
    include_in_schema = False


def create_app() -> Litestar:
    """Create the Litestar app with a dynamic API version prefix from config."""
    logger.info("Cartonization API service starting up")
    bootstrapper.initialize()
    config = bootstrapper.get_config()
    
    # Use basic OpenTelemetry configuration for Litestar
    service_name = os.getenv("OTEL_SERVICE_NAME", "terrabloom-rag-api")
    open_telemetry_config = OpenTelemetryConfig()
    open_telemetry_plugin = OpenTelemetryPlugin(open_telemetry_config)
    
    # Set up Prometheus
    prometheus_config = PrometheusConfig()
    
    version = (
        config.version
        if (hasattr(config, "version") and config.version != "")
        else "v1"
    )
    api_prefix = f"/api/{version}"
    print(f"API version prefix: {api_prefix}")
    
    return Litestar(
        route_handlers=[
            SchemaController,
            ConversationController,
            ConfigController,
            AskController,
            HealthController,
            AdminController,
            CustomPrometheusController,
        ],
        dependencies={
        "llm_manager": Provide(get_llm_manager),
        "db_manager": Provide(get_db_manager),
        "retriever": Provide(get_retriever),
        "config": Provide(get_config),
    },
        exception_handlers={Exception: global_exception_handler},
        on_startup=[initialize_app_services],
        debug=True,
        path=api_prefix,
        plugins=[open_telemetry_plugin],
        middleware=[
            prometheus_config.middleware,
            DefineMiddleware(ApplicationContextMiddleware),
            DefineMiddleware(LogEnricherMiddleware),
            DefineMiddleware(LogRequestBodyMiddleware),
        ],
    )


# Create the app instance using the factory
app = create_app()