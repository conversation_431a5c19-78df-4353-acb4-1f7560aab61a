"""BDD step definitions for extra IVF FAISS store scenarios."""

from unittest.mock import MagicMock, patch

import numpy as np
from behave import given, then, when

from src.vector_store.ivf_faiss_store import IVFFAISSStore


@given("an IVFFAISSStore instance (extra)")
def step_given_store_extra(context):
    """Provide an IVFFAISSStore instance for extra tests."""
    context.store = IVFFAISSStore(embedding_dim=4, nlist=2)


@when("I add documents with empty embeddings")
def step_when_add_empty_embeddings(context):
    """Add documents to the store with empty embeddings."""
    context.store.add_documents(np.array([]), [], [])


@then("no documents should be added")
def step_then_no_docs(context):
    """Assert that no documents are added to the store."""
    assert len(context.store.document_store) == 0


@when("I add documents with no metadata")
def step_when_add_no_metadata(context):
    """Add documents to the store without metadata."""
    emb = np.random.rand(2, 4).astype("float32")
    docs = [{"content": "a"}, {"content": "b"}]
    context.store.add_documents(emb, docs)


@then("documents should be added with empty metadata")
def step_then_empty_metadata(context):
    """Assert that documents are added with empty metadata dictionaries."""
    assert all(
        isinstance(m, dict) for m in context.store.metadata_store.values()
    )


@when("I search with a query embedding and no index")
def step_when_search_no_index(context):
    """Search the store with a query embedding when no index exists."""
    context.no_index_results = context.store.search(
        np.random.rand(4).astype("float32")
    )


@then("no results or exception results should be returned")
def step_then_no_results_or_exception(context):
    """Assert that no results or exception results are returned."""
    assert (
        getattr(context, "no_index_results", []) == []
        or getattr(context, "exception_results", []) == []
    )


@when("I search and an exception is raised")
def step_when_search_with_exception(context):
    """Simulate an exception during search in the store."""
    with patch.object(context.store, "index", create=True):
        context.store.index = MagicMock()
        context.store.index.search.side_effect = Exception("fail")
        context.exception_results = context.store.search(
            np.random.rand(4).astype("float32")
        )


@then("an error message should be returned for ivf faiss store")
def step_then_stats_error(context):
    """Assert that an error message is present in the store stats."""
    assert "error" in context.stats


@when("I save and an exception is raised")
def step_when_save_with_exception(context):
    """Simulate an exception during saving the store."""
    with patch("builtins.open", side_effect=Exception("fail")):
        result = context.store.save("dummy_dir")
        context.save_failed = not result


@then("saving should fail gracefully for ivf faiss store")
def step_then_save_fail_ivf(context):
    """Assert that saving fails gracefully for the store."""
    assert getattr(context, "save_failed", False) is True


@when("I decompress corrupted data")
def step_when_decompress_corrupted(context):
    """Attempt to decompress corrupted data and handle exceptions."""
    result = None
    try:
        result = context.store._decompress_document(b"not a valid zlib")
        context.decompress_failed = False
    except Exception:
        context.decompress_failed = True
    if result is None:
        context.decompress_failed = True


@then("an exception should be handled")
def step_then_decompress_failed(context):
    """Assert that an exception during decompression is handled."""
    assert getattr(context, "decompress_failed", False)


@when("I load an IVFFAISSStore and an exception is raised")
def step_when_load_with_exception(context):
    """Simulate an exception during loading the store from disk."""
    from unittest.mock import patch

    with patch(
        "src.vector_store.ivf_faiss_store.open", side_effect=Exception("fail")
    ):
        from src.vector_store.ivf_faiss_store import IVFFAISSStore

        try:
            context.load_result = IVFFAISSStore.load("dummy_dir")
            context.load_failed = False
        except Exception:
            context.load_failed = True


@then("loading should fail gracefully for ivf faiss store")
def step_then_load_fail_ivf(context):
    """Assert that loading fails gracefully for the store."""
    assert (
        getattr(context, "load_result", None) is None
        or getattr(context, "load_failed", False) is True
    )
