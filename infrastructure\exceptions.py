"""Custom exception classes for the cartonization API.

This module defines exception classes that represent various business logic errors
that can occur during the cartonization process.
"""

from typing import Any, Dict, Optional


class BusinessExceptionError(Exception):
    """Base class for all business exceptions."""

    status_code: int = 400
    default_detail: str = "An error occurred"

    def __init__(
        self,
        detail: Optional[str] = None,
        data: Optional[Dict[str, Any]] = None,
    ):
        """Initialize the exception.

        Args:
            detail: A human-readable explanation of the error
            data: Additional data to include in the error response
        """
        self.detail = detail or self.default_detail
        self.data = data or {}
        super().__init__(self.detail)


class ValidationBehaviorExceptionError(BusinessExceptionError):
    """Exception raised when input validation fails."""

    status_code = 512
    default_detail = "Validation error"


class DomainExceptionError(BusinessExceptionError):
    """Exception raised when a domain rule is violated."""

    status_code = 513
    default_detail = "Domain rule violation"


class ArgumentNullExceptionError(BusinessExceptionError):
    """Exception raised when a required argument is null."""

    status_code = 514
    default_detail = "Required argument is null"


class ArgumentExceptionError(BusinessExceptionError):
    """Exception raised when an argument is invalid."""

    status_code = 515
    default_detail = "Invalid argument"


class ApplicationLicenseExceptionError(BusinessExceptionError):
    """Exception raised when there's a license issue."""

    status_code = 516
    default_detail = "License error"


class StaleObjectStateExceptionError(BusinessExceptionError):
    """Exception raised when an object's state is stale."""

    status_code = 517
    default_detail = "Object state is stale"


class RecordVersionNotFoundExceptionError(BusinessExceptionError):
    """Exception raised when a record version is not found."""

    status_code = 518
    default_detail = "Record version not found"


class CartonizationStrategyNotFoundExceptionError(DomainExceptionError):
    """Exception raised when a cartonization strategy is not found."""

    default_detail = "Cartonization strategy not found"
