"""BDD step definitions for database connection features."""

from unittest.mock import MagicMock, patch

from behave import given, then, when

from src.database.connection import DatabaseManager


@when("I initialize the DatabaseManager")
def step_when_init_db_manager(context):
    """Initialize the DatabaseManager with mocked dependencies."""
    with (
        patch("src.database.connection.create_engine") as mock_engine,
        patch("src.database.connection.SQLDatabase") as mock_sqldb,
        patch("src.database.connection.QuerySQLDataBaseTool") as mock_tool,
    ):
        mock_engine.return_value = MagicMock()
        mock_sqldb.return_value = MagicMock()
        mock_tool.return_value = MagicMock()
        context.db_manager = DatabaseManager()


@then("the engine and db should be set")
def step_then_engine_db(context):
    """Assert that the engine and db are set in the DatabaseManager."""
    assert context.db_manager.engine is not None
    assert context.db_manager.db is not None


@when("I initialize the DatabaseManager with a bad URL")
def step_when_init_db_manager_bad(context):
    """Attempt to initialize the DatabaseManager with a bad URL and handle the error."""
    with patch(
        "src.database.connection.create_engine", side_effect=Exception("fail")
    ):
        try:
            DatabaseManager()
            assert False, "Should have raised"
        except Exception as e:
            context.connection_error = str(e)


@then("a connection error should be handled")
def step_then_connection_error(context):
    """Assert that a connection error is handled and recorded in context."""
    assert "fail" in getattr(context, "connection_error", "")


@given("a DatabaseManager instance")
def step_given_db_manager(context):
    """Provide a DatabaseManager instance with mocked dependencies."""
    with (
        patch("src.database.connection.create_engine") as mock_engine,
        patch("src.database.connection.SQLDatabase") as mock_sqldb,
        patch("src.database.connection.QuerySQLDataBaseTool") as mock_tool,
    ):
        mock_engine.return_value = MagicMock()
        mock_sqldb.return_value = MagicMock()
        mock_tool.return_value = MagicMock(invoke=lambda q: "result")
        context.db_manager = DatabaseManager()


@when("I execute a valid SQL query")
def step_when_exec_valid_query(context):
    """Execute a valid SQL query using the DatabaseManager."""
    context.db_manager.query_tool.invoke = lambda q: "ok"
    context.query_result = context.db_manager.execute_query("SELECT 1")


@then("the result should be returned")
def step_then_query_result(context):
    """Assert that the correct result is returned for a valid query."""
    assert context.query_result == "ok"


@when("I execute an invalid SQL query")
def step_when_exec_invalid_query(context):
    """Execute an invalid SQL query and handle the resulting error."""
    context.db_manager.query_tool.invoke = lambda q: (_ for _ in ()).throw(
        Exception("bad sql")
    )
    context.error_result = context.db_manager.execute_query("BAD SQL")


@then("an error message should be returned for db connection")
def step_then_error_result(context):
    """Assert that an error message is returned for a failed query."""
    assert "Error executing query" in context.error_result


@when("I strip SQL markdown from a query")
def step_when_strip_sql(context):
    """Strip SQL markdown from a query string using the DatabaseManager."""
    context.cleaned_query = context.db_manager._strip_sql_markdown(
        "```sql\nSELECT 1\n```"
    )


@then("the cleaned query should be returned")
def step_then_cleaned_query(context):
    """Assert that the cleaned query string is returned."""
    assert context.cleaned_query == "SELECT 1"
