Feature: Database Connection Management

  Scenario: Initialize DatabaseManager successfully
    When I initialize the DatabaseManager
    Then the engine and db should be set

  Scenario: Handle database connection error
    When I initialize the DatabaseManager with a bad URL
    Then a connection error should be handled

  Scenario: Execute a SQL query successfully
    Given a DatabaseManager instance
    When I execute a valid SQL query
    Then the result should be returned

  Scenario: Execute a SQL query with error
    Given a DatabaseManager instance
    When I execute an invalid SQL query
    Then an error message should be returned for db connection

  Scenario: Strip SQL markdown from query
    Given a DatabaseManager instance
    When I strip SQL markdown from a query
    Then the cleaned query should be returned 