Feature: Conversation Management

  Scenario: Create a new conversation and list it
    Given I have a conversation store
    When I create a new conversation with the title "Test Conversation"
    Then the conversation list should include a conversation titled "Test Conversation"

  Scenario: Save and load messages in a conversation
    Given I have a conversation store
    And I create a new conversation with the title "Message Test"
    When I save the messages ["Hello", "Hi"] to the conversation
    Then loading messages from the conversation should return ["Hello", "Hi"]

  Scenario: Update conversation title
    Given I have a conversation store
    And I create a new conversation with the title "Old Title"
    When I update the conversation title to "New Title"
    Then the conversation list should include a conversation titled "New Title"

  Scenario: Delete a conversation
    Given I have a conversation store
    And I create a new conversation with the title "To Delete"
    When I delete the conversation
    Then the conversation list should not include a conversation titled "To Delete"

  Scenario: Save messages to a non-existent conversation
    Given I have a conversation store
    When I try to save the messages ["Ghost"] to a non-existent conversation
    Then saving should fail

  Scenario: Update title of a non-existent conversation
    Given I have a conversation store
    When I try to update the title of a non-existent conversation to "No Title"
    Then updating should fail

  Scenario: Delete a non-existent conversation
    Given I have a conversation store
    When I try to delete a non-existent conversation
    Then deleting should fail 

Scenario: Save messages to a non-existent conversation
  Given a conversation store
  When I try to save messages to a non-existent conversation
  Then saving should fail gracefully

<PERSON><PERSON>rio: Update the title of a non-existent conversation
  Given a conversation store
  When I try to update the title of a non-existent conversation
  Then updating should fail gracefully

Scenario: Delete a non-existent conversation
  Given a conversation store
  When I try to delete a non-existent conversation
  Then deleting should fail gracefully

Scenario: Load messages from a corrupted conversation file
  Given a corrupted conversation file exists
  When I try to load messages from the corrupted conversation
  Then loading should fail gracefully

Scenario: List conversations when a file is corrupted
  Given a conversation store with a corrupted file
  When I list all conversations
  Then the corrupted file should be skipped and no exception should be raised 