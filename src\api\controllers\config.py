"""Controller for handling config-related API endpoints."""

from litestar import Controller, get

from src.config.config import config


class Config<PERSON><PERSON><PERSON>er(Controller):
    """Controller for the /config endpoint."""

    path = "/config"

    @get("/")
    def get_config(self) -> dict:
        """Return the current configuration settings."""
        return {
            "host": f"{config.DB_HOST}:{config.DB_PORT}",
            "database": config.DB_NAME,
            "user": config.DB_USER,
            "model": config.LLM_MODEL,
        }
