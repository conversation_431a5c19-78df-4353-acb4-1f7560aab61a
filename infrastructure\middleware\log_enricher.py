"""
Log enricher middleware for adding application context to logs.

This middleware extends the ApplicationContextMiddleware by adding the context
to a contextvars-based logging context, making it available to all loggers.
"""

import contextvars
from datetime import datetime, timezone

from loguru import logger
from starlette.middleware.base import BaseHTTPMiddleware

# Create a context variable to store request context
request_context = contextvars.ContextVar("request_context", default={})


class LogEnricherMiddleware(BaseHTTPMiddleware):
    """Middleware that enriches logs with application context."""

    async def dispatch(self, request, call_next):
        """Set up logging context with application context values.

        Args:
            request: The HTTP request
            call_next: The next middleware in the chain

        Returns:
            The HTTP response
        """
        # Extract headers
        headers = dict(request.headers)

        # Log the request
        logger.debug(f"Processing request: {request.method} {request.url.path}")

        # Clear any existing context
        request_context.set({})

        # Build context from request attributes
        context = {}

        # Add basic request info
        context.update(
            {
                "Timestamp": datetime.now(timezone.utc).isoformat(),
                "request_id": headers.get("x-request-id", "unknown"),
                "client_ip": headers.get(
                    "x-forwarded-for",
                    request.client.host if request.client else "unknown",
                ),
            }
        )

        # Add application context if available
        if hasattr(request, "state") and hasattr(request.state, "context"):
            app_context = request.state.context
            context.update(
                {
                    "Environment": app_context.environment,
                    "UserId": app_context.user_id,
                    "UserName": app_context.user_name,
                    "UserAgent": app_context.user_agent,
                    "Customer": app_context.customer_id,
                    "Vendor": app_context.vendor,
                    "Account": app_context.account_id,
                    "BU": app_context.business_unit_id,
                    "Warehouse": app_context.warehouse_id,
                    "CorrelationId": app_context.correlation_id,
                    "SessionId": app_context.session_id,
                    "TraceId": app_context.trace_id,
                    "SpanId": app_context.span_id,
                    "ParentTraceId": app_context.parent_trace_id,
                    "BrowserId": app_context.browser_id,
                    "BrowserDetail": app_context.browser_detail,
                    "RecordVersion": app_context.record_version,
                    "CurrentLocation": app_context.current_location,
                    "ApplicationName": app_context.application_name,
                    "InstanceName": app_context.instance_name,
                    "ApplicationVersion": app_context.application_version,
                    "OriginalRequestUri": app_context.original_request_uri,
                    "CurrentRequestUri": app_context.current_request_uri,
                    "PartitionKey": app_context.partition_key,
                }
            )

            # Log that we've enriched with context
            logger.debug("Enriched logs with application context")

        # Set the context for this request
        request_context.set(context)

        # Process the request
        try:
            response = await call_next(request)

            # Add response info to context
            status_code = response.status_code
            context = request_context.get()
            context["status_code"] = status_code
            request_context.set(context)

            # Log the response
            logger.debug(
                f"Response: {status_code} for {request.method} {request.url.path}"
            )

            return response
        finally:
            # Clear the context when we're done
            request_context.set({})
