"""Configuration constants for the cartonization service.

This module loads configuration from profile-specific config files and defines constants
used throughout the cartonization service.
"""

import datetime
import json
import os
import socket
from typing import Any, Dict


def default_json_serializer(obj):
    """Default JSON serializer for non-serializable objects.
    Converts datetime, timedelta, and other objects to string representations.

    Args:
        obj: The object to serialize.

    Returns:
        str: String representation of the object.
    """
    if isinstance(obj, datetime.timedelta):
        return str(obj)
    if isinstance(obj, datetime.datetime):
        return obj.isoformat()
    return str(obj)


def fluentbit_tcp_sink(message):
    """Send log messages to Fluent Bit over TCP.

    Args:
        message: The log message to send.

    Returns:
        None
    """
    try:
        log_line = message.record["message"] + "\n"
        with socket.create_connection(("127.0.0.1", 24224), timeout=1) as s:
            s.sendall(log_line.encode("utf-8"))
    except Exception:
        pass


class ConfigService:
    """Service for managing application configuration.

    Loads configuration from profile-specific JSON files and provides
    type-safe access to configuration values.
    """

    def __init__(self, config_path: str):
        """Initialize the configuration service.

        Args:
            config_path (str): Path to the configuration file.
        """
        self._config_path = config_path
        self._config = self._load_config()

        self.service_name = self._config.get("ServiceName", "cartonization-api")

    def _load_config(self) -> Dict[str, Any]:
        """Load configuration from the profile-specific JSON file.

        Returns:
            Dict[str, Any]: Loaded configuration.

        Raises:
            FileNotFoundError: If config file doesn't exist.
            json.JSONDecodeError: If config file is invalid JSON.
        """
        config_path = self._config_path

        if not os.path.exists(config_path):
            raise FileNotFoundError(
                f"Configuration file not found: {config_path}"
            )

        try:
            with open(config_path, "r") as f:
                return json.load(f)
        except json.JSONDecodeError as e:
            raise ValueError(f"Invalid JSON in config file {config_path}: {e}")

    def get_server_config(self) -> Dict[str, Any]:
        """Get server configuration parameters.

        Returns:
            Dictionary with server configuration parameters.
        """
        default_port = 8000

        # Extract port from config
        port_str = self._config.get("Kestrel", {}).get(
            "HttpsPorts", str(default_port)
        )

        try:
            port = int(port_str)
        except ValueError:
            port = default_port

        # Extract TLS certificate paths
        cert_path = (
            self._config.get("Kestrel", {}).get("Tls", {}).get("Cert", None)
        )
        key_path = (
            self._config.get("Kestrel", {}).get("Tls", {}).get("Key", None)
        )

        # Get OpenTelemetry config
        otlp_endpoint = self._config.get("OpenTelemetryTracing", {}).get(
            "Endpoint", "localhost:4317"
        )

        return {
            "port": port,
            "cert_path": cert_path,
            "key_path": key_path,
            "service_name": self.service_name,
            "otlp_endpoint": otlp_endpoint,
        }

    @property
    def app_name(self) -> str:
        """Get the application name.

        Returns:
            str: Application name.
        """
        return self._config.get("metadata", {}).get(
            "ApplicationName", "Unknown"
        )

    @property
    def version(self) -> str:
        """Get the application version.

        Returns:
            str: Application version.
        """
        return self._config.get("metadata", {}).get("Version", "v1")

    @property
    def description(self) -> str:
        """Get the application description.

        Returns:
            str: Application description.
        """
        return self._config.get("metadata", {}).get("Description", "")

    @property
    def release_id(self) -> str:
        """Get the release ID.

        Returns:
            str: Release ID.
        """
        return self._config.get("metadata", {}).get("ReleaseId", "")

    @property
    def service_id(self) -> str:
        """Get the service ID.

        Returns:
            str: Service ID.
        """
        return self._config.get("metadata", {}).get("ServiceId", "")

    @property
    def log_path(self) -> str:
        """Get the log file path from config.

        Returns:
            str: Log file path.
        """
        return self._config.get("logging", {}).get("Path", "/var/log.log")

    @property
    def log_rotation(self) -> str:
        """Get the log rotation size.

        Returns:
            str: Log rotation size.
        """
        return self._config.get("logging", {}).get("Rotation", "10MB")

    @property
    def log_max_files(self) -> int:
        """Get the maximum number of log files to keep.

        Returns:
            int: Maximum number of log files.
        """
        return self._config.get("logging", {}).get("MaxFiles", 5)

    @property
    def log_level(self) -> str:
        """Get the logging level.

        Returns:
            str: Logging level.
        """
        return self._config.get("logging", {}).get("Level", "ERROR")
