"""Pydantic schemas for API request and response models."""

from typing import List, Optional

from pydantic import BaseModel

from src.models import Conversation, Message, TableSchema


class AskRequest(BaseModel):
    """Request model for the /ask endpoint."""

    question: str
    conversation_id: Optional[str] = None


class AskResponse(BaseModel):
    """Response model for the /ask endpoint."""

    answer: str
    conversation_id: str


class ConversationCreateRequest(BaseModel):
    """Request model for creating a new conversation."""

    title: Optional[str] = "New Conversation"


class ConversationResponse(BaseModel):
    """Response model for a conversation."""

    id: str


class ConversationListResponse(BaseModel):
    """Response model for a list of conversations."""

    conversations: List[Conversation]


class MessageListResponse(BaseModel):
    """Response model for a list of messages."""

    messages: List[Message]


class TableSchemaListResponse(BaseModel):
    """Response model for a list of table schemas."""

    schemas: List[TableSchema]
