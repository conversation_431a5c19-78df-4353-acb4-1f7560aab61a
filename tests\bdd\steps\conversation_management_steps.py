"""BDD step definitions for conversation management features."""

import ast
import os

from behave import given, then, when
from langchain_core.messages import HumanMessage

from src.database.conversations import ConversationStore


@given("a conversation store")
def step_given_conversation_store(context):
    """Provide a ConversationStore instance for testing."""
    context.store = ConversationStore()


@when("I try to save messages to a non-existent conversation")
def step_when_save_nonexistent(context):
    """Attempt to save messages to a non-existent conversation."""
    context.save_result = context.store.save_messages(
        "nonexistent-id", [HumanMessage(content="hi")]
    )


@then("saving should fail gracefully")
def step_then_save_fail(context):
    """Assert that saving to a non-existent conversation fails gracefully."""
    assert context.save_result is False


@then("saving should fail")
def step_then_saving_should_fail(context):
    """Assert that saving fails."""
    assert context.save_result is False


@when("I try to update the title of a non-existent conversation")
def step_when_update_title_nonexistent(context):
    """Attempt to update the title of a non-existent conversation."""
    context.update_result = context.store.update_conversation_title(
        "nonexistent-id", "New Title"
    )


@then("updating should fail gracefully")
def step_then_update_fail(context):
    """Assert that updating the title fails gracefully."""
    assert context.update_result is False


@then("updating should fail")
def step_then_updating_should_fail(context):
    """Assert that updating the title fails."""
    assert context.update_result is False


@when('I try to update the title of a non-existent conversation to "{title}"')
def step_when_try_update_title_nonexistent(context, title):
    """Attempt to update the title of a non-existent conversation to a specific.

    title.
    """
    context.update_result = context.store.update_conversation_title(
        "nonexistent-id", title
    )


@when("I try to delete a non-existent conversation")
def step_when_delete_nonexistent(context):
    """Attempt to delete a non-existent conversation."""
    context.delete_result = context.store.delete_conversation("nonexistent-id")


@then("deleting should fail gracefully")
def step_then_delete_fail(context):
    """Assert that deleting a non-existent conversation fails gracefully."""
    assert context.delete_result is False


@then("deleting should fail")
def step_then_deleting_should_fail(context):
    """Assert that deleting a non-existent conversation fails."""
    assert context.delete_result is False


@given("a corrupted conversation file exists")
def step_given_corrupted_file(context):
    """Create a corrupted conversation file for testing."""
    context.store = ConversationStore()
    conv_id = "corrupted-conv-id"
    file_path = os.path.join(context.store.storage_path, f"{conv_id}.json")
    with open(file_path, "w") as f:
        f.write("{corrupted json")
    context.corrupted_conv_id = conv_id


@when("I try to load messages from the corrupted conversation")
def step_when_load_corrupted(context):
    """Attempt to load messages from a corrupted conversation file."""
    context.load_result = context.store.load_messages(context.corrupted_conv_id)


@then("loading should fail gracefully")
def step_then_load_fail(context):
    """Assert that loading from a corrupted conversation file fails gracefully."""
    assert context.load_result == []


@given("a conversation store with a corrupted file")
def step_given_store_with_corrupted(context):
    """Provide a ConversationStore with a corrupted file present."""
    context.store = ConversationStore()
    conv_id = "corrupted-list-id"
    file_path = os.path.join(context.store.storage_path, f"{conv_id}.json")
    with open(file_path, "w") as f:
        f.write("{corrupted json")


@when("I list all conversations")
def step_when_list_convs(context):
    """List all conversations, handling any exceptions from corrupted files."""
    try:
        context.listed_convs = context.store.list_conversations()
        context.list_exception = None
    except Exception as e:
        context.listed_convs = []
        context.list_exception = e


@then("the corrupted file should be skipped and no exception should be raised")
def step_then_list_skip_corrupted(context):
    """Assert that corrupted files are skipped and no exception is raised."""
    assert context.list_exception is None
    assert isinstance(context.listed_convs, list)


@given("I have a conversation store")
def step_given_i_have_conversation_store(context):
    """Provide a ConversationStore instance for testing (alternate phrasing)."""
    context.store = ConversationStore()


@given('I create a new conversation with the title "{title}"')
def step_given_create_conversation_with_title(context, title):
    """Create a new conversation with the specified title."""
    context.conversation_id = context.store.create_conversation(title=title)
    context.conversation_title = title


@then('the conversation list should include a conversation titled "{title}"')
def step_then_conversation_list_includes_title(context, title):
    """Assert that the conversation list includes a conversation with the.

    specified title.
    """
    conversations = context.store.list_conversations()
    titles = [conv["title"] for conv in conversations]
    assert title in titles


@then(
    'the conversation list should not include a conversation titled "{title}"'
)
def step_then_conversation_list_not_includes_title(context, title):
    """Assert that the conversation list does not include a conversation with.

    the specified title.
    """
    conversations = context.store.list_conversations()
    titles = [conv["title"] for conv in conversations]
    assert title not in titles


@when("I save the messages {messages} to the conversation")
def step_when_save_messages_to_conversation(context, messages):
    """Save the specified messages to the conversation."""
    # messages is a string representation of a list
    msg_list = ast.literal_eval(messages)
    human_msgs = [HumanMessage(content=m) for m in msg_list]
    context.save_result = context.store.save_messages(
        context.conversation_id, human_msgs
    )


@then("loading messages from the conversation should return {messages}")
def step_then_loading_messages_should_return(context, messages):
    """Assert that loading messages from the conversation returns the expected.

    messages.
    """
    expected = ast.literal_eval(messages)
    loaded = context.store.load_messages(context.conversation_id)
    loaded_contents = [m.content for m in loaded]
    assert loaded_contents == expected


@when('I update the conversation title to "{title}"')
def step_when_update_conversation_title(context, title):
    """Update the conversation title to the specified value."""
    context.update_result = context.store.update_conversation_title(
        context.conversation_id, title
    )
    context.conversation_title = title


@when("I delete the conversation")
def step_when_delete_conversation(context):
    """Delete the current conversation from the store."""
    context.delete_result = context.store.delete_conversation(
        context.conversation_id
    )


@when("I try to save the messages {messages} to a non-existent conversation")
def step_when_try_save_messages_nonexistent(context, messages):
    """Attempt to save messages to a non-existent conversation."""
    msg_list = ast.literal_eval(messages)
    human_msgs = [HumanMessage(content=m) for m in msg_list]
    context.save_result = context.store.save_messages(
        "nonexistent-id", human_msgs
    )
