"""This file defines the custom class for logging equest body"""

from opentelemetry import trace
from starlette.middleware.base import BaseHTTPMiddleware


class LogRequestBodyMiddleware(BaseHTTPMiddleware):
    """Log the request body."""

    async def dispatch(self, request, call_next):
        """Log the request body."""
        body = await request.body()
        headers = dict(request.headers)
        correlation_id = headers.get("app-correlation-id", "unknown")
        span = trace.get_current_span()
        if span.is_recording():
            span.set_attribute("request.body", body.decode("utf-8"))
            span.set_attribute("testid", correlation_id)
        if hasattr(request, "state") and hasattr(request.state, "context"):
            context = request.state.context
            context_dict = {
                "App-Environment": getattr(context, "environment", "unknown"),
                "App-User-Id": getattr(context, "user_id", "unknown"),
                "App-User-Agent": getattr(context, "user_agent", "unknown"),
                "App-Customer-Id": getattr(context, "customer_id", "unknown"),
                "App-Account-Id": getattr(context, "account_id", "unknown"),
                "App-BU-Id": getattr(context, "business_unit_id", "unknown"),
                "App-Warehouse-Id": getattr(context, "warehouse_id", "unknown"),
                "CorrelationId": getattr(context, "correlation_id", "unknown"),
                "App-Session-Id": getattr(context, "session_id", "unknown"),
            }
            for key, value in context_dict.items():
                span.set_attribute(key, value)
        response = await call_next(request)
        return response
