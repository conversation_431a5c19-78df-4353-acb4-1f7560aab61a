"""BDD step definitions for configuration management features."""

import os

from behave import given, then, when

from src.config.config import Config


@given("no environment variables are set for config")
def step_given_clear_env(context):
    """Clear all relevant environment variables for config testing."""
    keys = [
        "GEMINI_API_KEY",
        "OPENAI_API_KEY",
        "GOOGLE_API_KEY",
        "GROQ_API_KEY",
        "LANGCHAIN_API_KEY",
        "LANGCHAIN_TRACING_V2",
        "LANGCHAIN_ENDPOINT",
        "LANGCHAIN_PROJECT",
        "DB_USER",
        "DB_PASSWORD",
        "DB_HOST",
        "DB_NAME",
        "DB_PORT",
    ]
    for key in keys:
        os.environ.pop(key, None)


@given("environment variables are set for config")
def step_given_set_env(context):
    """Set all relevant environment variables for config testing."""
    os.environ["GEMINI_API_KEY"] = "GEMINI_TEST"
    os.environ["OPENAI_API_KEY"] = "OPENAI_TEST"
    os.environ["GOOGLE_API_KEY"] = "GOOGLE_TEST"
    os.environ["GROQ_API_KEY"] = "GROQ_TEST"
    os.environ["LANGCHAIN_API_KEY"] = "LANGCHAIN_TEST"
    os.environ["LANGCHAIN_TRACING_V2"] = "false"
    os.environ["LANGCHAIN_ENDPOINT"] = "http://test.endpoint"
    os.environ["LANGCHAIN_PROJECT"] = "test_project"
    os.environ["DB_USER"] = "test_user"
    os.environ["DB_PASSWORD"] = "test_pass"
    os.environ["DB_HOST"] = "test_host"
    os.environ["DB_NAME"] = "test_db"
    os.environ["DB_PORT"] = "1234"


@when("I create a Config instance")
def step_when_create_config(context):
    """Create a Config instance for testing."""
    context.config_instance = Config()


@then("the config should use default values")
def step_then_defaults(context):
    """Assert that the config instance uses default values."""
    assert context.config_instance.GEMINI_API_KEY.startswith("AIza")
    assert context.config_instance.OPENAI_API_KEY.startswith("sk-")
    assert context.config_instance.DB_USER == "usr_reporting"
    assert context.config_instance.DB_PORT == 6432


@then("the config should use the environment variable values")
def step_then_env_values(context):
    """Assert that the config instance uses environment variable values."""
    assert context.config_instance.GEMINI_API_KEY == "GEMINI_TEST"
    assert context.config_instance.OPENAI_API_KEY == "OPENAI_TEST"
    assert context.config_instance.DB_USER == "test_user"
    assert context.config_instance.DB_PORT == 1234


@given("a Config instance")
def step_given_config_instance(context):
    """Provide a Config instance for testing."""
    context.config_instance = Config()


@when("I get the database URL")
def step_when_get_db_url(context):
    """Get the database URL from the config instance."""
    context.db_url = context.config_instance.get_db_url()


@then("it should be a valid PostgreSQL URL")
def step_then_db_url(context):
    """Assert that the database URL is a valid PostgreSQL URL."""
    assert context.db_url.startswith("postgresql+psycopg2://")
    assert "@" in context.db_url and "/" in context.db_url


@when("I call set_environment_variables")
def step_when_call_set_env(context):
    """Call set_environment_variables on the config instance."""
    context.config_instance.set_environment_variables()


@then("the environment variables should be set accordingly")
def step_then_env_set(context):
    """Assert that the environment variables are set according to the config.

    instance.
    """
    assert (
        os.environ["GEMINI_API_KEY"] == context.config_instance.GEMINI_API_KEY
    )
    assert (
        os.environ["OPENAI_API_KEY"] == context.config_instance.OPENAI_API_KEY
    )
    assert os.environ["DB_USER"] == context.config_instance.DB_USER


@when("I get the foreign key patterns")
def step_when_get_fk_patterns(context):
    """Get the foreign key patterns from the config instance."""
    context.fk_patterns = context.config_instance.foreign_key_patterns()


@then("it should return a dictionary with expected keys")
def step_then_fk_patterns(context):
    """Assert that the foreign key patterns dictionary contains expected keys."""
    assert isinstance(context.fk_patterns, dict)
    assert "customerid" in context.fk_patterns
    assert "orderid" in context.fk_patterns
