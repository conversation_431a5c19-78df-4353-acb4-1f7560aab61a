{"Kestrel": {"HttpPorts": "", "HttpsPorts": "8000", "Tls": {"Cert": "certs/tls.crt", "Key": "certs/tls.key", "CertificationAuthority": [{"Domain": "wms-sso.wms-core-pg.npaz.ohl.com", "Ca": "/opt/wms/certs/ca.crt"}]}, "Limits": {"MaxConcurrentConnections": 100, "MaxConcurrentUpgradedConnections": 100, "MaxRequestBodySize": 52428800}, "Request": {"BufferThresholdInKb": 300, "BufferLimitInKb": 400}, "DisableStringReuse": true}, "metadata": {"ApplicationName": "Vite SQL", "Description": "ViteSQL- SQL Chatbot", "Version": "2024-2100", "ReleaseId": "", "ServiceId": ""}, "ServiceName": "terrabloom-rag-api", "OpenTelemetryTracing": {"Endpoint": "localhost:4317"}, "logging": {"Path": "/var/log.log", "Rotation": "10MB", "MaxFiles": 5, "Level": "ERROR"}}