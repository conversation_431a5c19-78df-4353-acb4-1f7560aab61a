"""BDD step definitions for testing the IVFFAISSRetriever component."""

from unittest.mock import MagicMock, patch

from behave import given, then, when
from langchain_core.documents import Document

from src.vector_store.retriever import IVFFAISSRetriever


@when("I initialize the IVFFAISSRetriever")
def step_when_init_retriever(context):
    """Initialize the IVFFAISSRetriever with mocked dependencies."""
    with (
        patch("src.vector_store.retriever.IVFFAISSStore") as mock_store,
        patch(
            "src.vector_store.retriever.GoogleGenerativeAIEmbeddings"
        ) as mock_embed,
    ):
        mock_store.load.return_value = MagicMock()
        mock_embed.return_value = MagicMock(
            embed_documents=lambda x: [[0.1] * 4] * len(x),
            embed_query=lambda x: [0.1] * 4,
        )
        context.retriever = IVFFAISSRetriever()


@then("the store should be set")
def step_then_store_set(context):
    """Assert that the retriever's store is set."""
    assert context.retriever.store is not None


@when("I initialize the IVFFAISSRetriever and loading fails")
def step_when_init_retriever_fail(context):
    """Initialize the IVFFAISSRetriever and simulate a loading failure."""
    with (
        patch(
            "src.vector_store.retriever.IVFFAISSStore.load",
            side_effect=Exception("fail"),
        ),
        patch(
            "src.vector_store.retriever.IVFFAISSRetriever._create_schema_store",
            return_value=MagicMock(),
        ),
        patch(
            "src.vector_store.retriever.GoogleGenerativeAIEmbeddings"
        ) as mock_embed,
    ):
        mock_embed.return_value = MagicMock(
            embed_documents=lambda x: [[0.1] * 4] * len(x),
            embed_query=lambda x: [0.1] * 4,
        )
        context.retriever = IVFFAISSRetriever()


@then("a new store should be created")
def step_then_new_store(context):
    """Assert that a new store is created for the retriever."""
    assert context.retriever.store is not None


@given("an IVFFAISSRetriever instance")
def step_given_retriever(context):
    """Provide an IVFFAISSRetriever instance with mocked dependencies."""
    with (
        patch("src.vector_store.retriever.IVFFAISSStore") as mock_store,
        patch(
            "src.vector_store.retriever.GoogleGenerativeAIEmbeddings"
        ) as mock_embed,
    ):
        store = MagicMock()
        store.search.return_value = [
            {
                "metadata": {
                    "table": "t",
                    "schema": "s",
                    "table_name": "t",
                    "comments": "c",
                    "ddl": "ddl",
                },
                "distance": 0.1,
            }
        ]
        mock_store.load.return_value = store
        mock_embed.return_value = MagicMock(
            embed_documents=lambda x: [[0.1] * 4] * len(x),
            embed_query=lambda x: [0.1] * 4,
        )
        context.retriever = IVFFAISSRetriever(store=store)


@when("I invoke with a query that matches documents")
def step_when_invoke_with_results(context):
    """Invoke the retriever with a query that matches documents."""
    context.docs = context.retriever.invoke("find table")


@then("documents should be returned")
def step_then_docs(context):
    """Assert that documents are returned and are of type Document."""
    assert isinstance(context.docs, list)
    assert all(isinstance(d, Document) for d in context.docs)
    assert len(context.docs) > 0


@when("I invoke with a query that matches nothing")
def step_when_invoke_no_results(context):
    """Invoke the retriever with a query that matches no documents."""
    context.retriever.store.search.return_value = []
    context.no_docs = context.retriever.invoke("no match")


@then("no documents should be returned")
def step_then_no_docs(context):
    """Assert that no documents are returned or an error occurred."""
    assert (
        getattr(context, "no_docs", []) == []
        or getattr(context, "err_docs", []) == []
    )


@when("I invoke and search raises an error")
def step_when_invoke_search_error(context):
    """Invoke the retriever and simulate a search error."""
    context.retriever.store.search.side_effect = Exception("fail")
    context.err_docs = context.retriever.invoke("fail")


@when("I get store stats from retriever")
def step_when_get_stats_retriever(context):
    """Get store statistics from the retriever."""
    context.retriever.store.get_stats.return_value = {"document_count": 1}
    context.stats = context.retriever.get_store_stats()


@given("an IVFFAISSRetriever instance with a faulty store")
def step_given_faulty_retriever(context):
    """Provide an IVFFAISSRetriever instance with a faulty store."""
    # Store with search method that raises an exception
    faulty_store = MagicMock()
    faulty_store.search.side_effect = Exception("Simulated search error")
    context.retriever = IVFFAISSRetriever(store=faulty_store)


@when("I invoke the retriever with a query that triggers an error")
def step_when_invoke_error(context):
    """Invoke the retriever with a query that triggers an error."""
    try:
        context.docs = context.retriever.invoke("error query")
    except Exception:
        context.docs = None


@given("an IVFFAISSRetriever instance with incomplete metadata in results")
def step_given_incomplete_metadata_retriever(context):
    """Provide an IVFFAISSRetriever instance with incomplete metadata in results."""
    # Store with search method that returns incomplete metadata
    incomplete_result = [
        {"metadata": {"table": "t"}}
    ]  # missing required fields
    store = MagicMock()
    store.search.return_value = incomplete_result
    context.retriever = IVFFAISSRetriever(store=store)


@when("I invoke the retriever with a query")
def step_when_invoke_incomplete(context):
    """Invoke the retriever with a query and handle incomplete metadata."""
    try:
        context.docs = context.retriever.invoke("any query")
        context.invoke_exception = None
    except Exception as e:
        context.docs = []
        context.invoke_exception = e


@then(
    "no exception should be raised and documents should be returned or skipped"
)
def step_then_no_exception_docs_or_skipped(context):
    """Assert that no exception was raised and documents were returned or skipped."""
    assert context.invoke_exception is None
    assert isinstance(context.docs, list)
