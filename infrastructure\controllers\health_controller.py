"""# src/api/controllers/health_controller.py.

Controller for handling health check requests.

This module defines an endpoint to check the health of the cartonization
service.
"""

from datetime import timedelta
from litestar import Controller, get

from infrastructure.config import ConfigService
from infrastructure.schemas.health_response import HealthDetail, HealthResponse


class HealthController(Controller):
    """Controller for handling health check requests."""

    path = "/health"

    @get()
    def health_check(self, config: ConfigService) -> HealthResponse:
        """Health check endpoint to verify the service is running.

        Returns:
            HealthResponse: Health status and service metadata.
        """
        app_name = config.app_name
        version = config.version
        if not app_name or not version:
            return HealthResponse(
                status="unhealthy",
                status_code=500,
                appname=app_name,
                instname="vitesql",
                version=version,
                release_id="",
                notes="",
                service_id="",
                description="vitesql service is not properly configured.",
                message="Service is not running.",
                details=[
                    HealthDetail(
                        component="configuration",
                        status="unhealthy",
                        description="Missing app name or version",
                        response_time=timedelta(microseconds=1.0)
                    )
                ]
            )
        return HealthResponse(
            status="healthy",
            status_code=200,
            appname=app_name,
            instname="vitesql",
            version=version,
            release_id="",
            notes="",
            service_id="",
            description="vitesql service is running.",
            message="Service is running.",
            details=[
                HealthDetail(
                    component="self",
                    status="healthy",
                    description="Service is operational",
                    response_time=timedelta(microseconds=1.0)
                )
            ]
        )
